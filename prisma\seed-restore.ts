import { PrismaClient } from '@prisma/client'
import * as fs from 'fs'
import * as path from 'path'

const prisma = new PrismaClient()

async function main() {
  try {
    // Baca file backup
    const backupPath = path.join(__dirname, '../backups/backup_2025-02-18T03-10-54-732Z.json')
    const backupData = JSON.parse(fs.readFileSync(backupPath, 'utf-8'))
    
    console.log('<PERSON><PERSON> restore database...')

    // Restore Users
    console.log('Restore users...')
    for (const user of backupData.data.users) {
      await prisma.user.upsert({
        where: { id: user.id },
        update: user,
        create: user
      })
    }

    // Restore Positions
    console.log('Restore positions...')
    for (const position of backupData.data.positions) {
      await prisma.position.upsert({
        where: { id: position.id },
        update: position,
        create: position
      })
    }

    // Restore Outlets
    console.log('Restore outlets...')
    for (const outlet of backupData.data.outlets) {
      await prisma.outlet.upsert({
        where: { id: outlet.id },
        update: outlet,
        create: outlet
      })
    }

    // Restore Employees
    console.log('Restore employees...')
    for (const employee of backupData.data.employees) {
      await prisma.employee.upsert({
        where: { id: employee.id },
        update: employee,
        create: employee
      })
    }

    // Restore Kasbons dengan transformasi data
    console.log('Restore kasbons...')
    for (const kasbon of backupData.data.kasbons) {
      // Pastikan type adalah string
      const transformedKasbon = {
        ...kasbon,
        type: typeof kasbon.type === 'string' ? kasbon.type : 'KASBON'
      }

      await prisma.kasbon.upsert({
        where: { id: kasbon.id },
        update: transformedKasbon,
        create: transformedKasbon
      })
    }

    // Restore SlipGaji
    console.log('Restore slip gaji...')
    for (const slip of backupData.data.slipGaji) {
      await prisma.slipGaji.upsert({
        where: { id: slip.id },
        update: slip,
        create: slip
      })
    }

    // Restore DailyCommissions
    console.log('Restore daily commissions...')
    for (const commission of backupData.data.dailyCommissions) {
      await prisma.dailyCommission.upsert({
        where: { id: commission.id },
        update: commission,
        create: commission
      })
    }

    console.log('Restore database selesai!')

  } catch (error) {
    console.error('Error restoring database:', error)
    process.exit(1)
  } finally {
    await prisma.$disconnect()
  }
}

main() 