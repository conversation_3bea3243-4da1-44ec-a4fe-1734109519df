"use client"

import { useState, useEffect } from "react"
import {
  Search,
  Calendar,
  User,
  Building2,
  Phone,
  DollarSign,
  Clock,
  Gift,
  MinusCircle,
  Download,
  FileText,
  MessageCircle,
  Trash2
} from "lucide-react"
import { toast } from "react-hot-toast"
import { SlipGaji } from "@/types/slip-gaji"
import { Employee } from "@/types/employee"
import {
  formatCurrency,
  formatDate,
  formatPeriode,
  calculateTotalPendapatan,
  calculateTotalPotongan,
  generatePDFSlipGaji,
  generateExcelSlipGaji
} from "@/services/slip-gaji"

export default function SlipGajiPage() {
  const [slipGaji, setSlipGaji] = useState<SlipGaji[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 30
  const [selectedSlip, setSelectedSlip] = useState<SlipGaji | null>(null)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [selectedSlipToDelete, setSelectedSlipToDelete] = useState<SlipGaji | null>(null)
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1)
  const [selectedYear, setSelectedYear] = useState(2025)

  // Fetch data
  useEffect(() => {
    fetchData()
  }, [selectedMonth, selectedYear])

  const fetchData = async () => {
    try {
      const response = await fetch(`/api/slip-gaji?month=${selectedMonth}&year=${selectedYear}`)
      const result = await response.json()

      if (!result.success) {
        throw new Error(result.message)
      }

      setSlipGaji(result.data)
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Gagal mengambil data")
    } finally {
      setIsLoading(false)
    }
  }

  // Filter slip gaji berdasarkan pencarian
  const filteredSlipGaji = slipGaji.filter(slip =>
    slip.employee.user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    slip.employee.position.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    formatPeriode(slip.periode).toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Pagination logic
  const totalPages = Math.ceil(filteredSlipGaji.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentData = filteredSlipGaji.slice(startIndex, endIndex)

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  // Handle slip selection
  const handleSlipSelection = (slip: SlipGaji) => {
    setSelectedSlip(slip)
    setIsModalOpen(true)
  }

  // Handle PDF download
  const handleDownloadPDF = async (slip: SlipGaji) => {
    try {
      const pdfBlob = await generatePDFSlipGaji(slip)
      const url = URL.createObjectURL(pdfBlob)
      const a = document.createElement('a')
      a.href = url
      a.download = `slip-gaji-${slip.employee.user.name}-${formatPeriode(slip.periode).toLowerCase().replace(' ', '-')}.pdf`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      toast.error("Gagal mengunduh slip gaji")
    }
  }

  // Handle Excel download
  const handleDownloadExcel = async (month: number, year: number) => {
    try {
      const excelBlob = await generateExcelSlipGaji(slipGaji, month, year)
      const url = URL.createObjectURL(excelBlob)
      const a = document.createElement('a')
      a.href = url
      a.download = `slip-gaji-${formatPeriode({ bulan: month, tahun: year }).toLowerCase().replace(' ', '-')}.xlsx`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error("Error downloading Excel:", error)
      toast.error("Gagal mengunduh file Excel")
    }
  }

  // Handle WhatsApp send
  const handleSendWhatsApp = (slip: SlipGaji) => {
    // Format nomor WhatsApp
    const waNumber = slip.employee.noTelp.replace(/^0/, "62").replace(/[^0-9]/g, "")

    // Format pesan
    const message = `*Slip Gaji - ${formatPeriode(slip.periode)}*

Yth. ${slip.employee.user.name}
Jabatan: ${slip.employee.position.name}

*Rincian Pendapatan:*
Gaji Pokok: ${formatCurrency(slip.gaji.pokok)}
Tunjangan Transport: ${formatCurrency(slip.gaji.tunjangan.transport)}
Tunjangan Pulsa: ${formatCurrency(slip.gaji.tunjangan.pulsa)}
Tunjangan Makan: ${formatCurrency(slip.gaji.tunjangan.makan)}
Tunjangan BPJS: ${formatCurrency(slip.gaji.tunjangan.bpjs)}
Tunjangan Penyesuaian: ${formatCurrency(slip.gaji.tunjangan.penyesuaian)}
Komisi: ${formatCurrency(slip.gaji.komisi)}
Lembur: ${formatCurrency(slip.gaji.lembur)}
Bonus: ${formatCurrency(slip.gaji.bonus)}
Total Pendapatan: ${formatCurrency(calculateTotalPendapatan(slip.gaji))}

*Rincian Potongan:*
Kasbon: ${formatCurrency(slip.gaji.potongan.kasbon)}
Piutang: ${formatCurrency(slip.gaji.potongan.piutang)}
BPJS TP: ${formatCurrency(slip.gaji.potongan.bpjsTP)}
BPJS TK: ${formatCurrency(slip.gaji.potongan.bpjsTK)}
Lainnya: ${formatCurrency(slip.gaji.potongan.lainnya)}
Penyesuaian: ${formatCurrency(slip.gaji.potongan.penyesuaian || 0)}
Total Potongan: ${formatCurrency(calculateTotalPotongan(slip.gaji.potongan))}

*Total Gaji Bersih: ${formatCurrency(slip.gaji.total)}*

Hormat Kami,
Breaktime - Badan Segar Urusan Lancar`

    // Buka link WhatsApp
    window.open(`https://wa.me/${waNumber}?text=${encodeURIComponent(message)}`, '_blank')
  }

  // Handle delete slip
  const handleDeleteClick = (slip: SlipGaji) => {
    setSelectedSlipToDelete(slip)
    setIsDeleteModalOpen(true)
  }

  const handleConfirmDelete = async () => {
    if (!selectedSlipToDelete) return

    try {
      const response = await fetch(`/api/slip-gaji/${selectedSlipToDelete.id}`, {
        method: 'DELETE'
      })
      const result = await response.json()

      if (!result.success) {
        throw new Error(result.message)
      }

      toast.success("Slip gaji berhasil dihapus")
      setIsDeleteModalOpen(false)
      setSelectedSlipToDelete(null)
      fetchData() // Refresh data
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Gagal menghapus slip gaji")
    }
  }

  return (
    <div className="flex flex-col space-y-4">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h2 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-breaktime-primary to-breaktime-secondary bg-clip-text text-transparent">
          Slip Gaji
        </h2>
        <div className="flex gap-2 items-center">
          {/* Filter Bulan dan Tahun */}
          <div className="join">
            <select
              className="select select-bordered join-item text-base-content"
              value={selectedMonth}
              onChange={(e) => setSelectedMonth(Number(e.target.value))}
            >
              {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                <option key={month} value={month}>
                  {new Date(2024, month - 1).toLocaleDateString('id-ID', { month: 'long' })}
                </option>
              ))}
            </select>
            <select
              className="select select-bordered join-item text-base-content"
              value={selectedYear}
              onChange={(e) => setSelectedYear(Number(e.target.value))}
            >
              {Array.from({ length: 5 }, (_, i) => 2025 + i).map((year) => (
                <option key={year} value={year}>
                  {year}
                </option>
              ))}
            </select>
          </div>
          <button
            onClick={() => handleDownloadExcel(selectedMonth, selectedYear)}
            className="btn btn-sm sm:btn-md bg-success/10 hover:bg-success/20 text-success border-0 gap-2"
          >
            <Download className="h-4 w-4 sm:h-5 sm:w-5" />
            <span className="hidden xs:inline">Export Excel</span>
            <span className="inline xs:hidden">Excel</span>
          </button>
        </div>
      </div>

      {/* Search Bar */}
      <div className="form-control w-full sm:w-96">
        <div className="relative">
          <input
            type="text"
            placeholder="Cari berdasarkan nama, periode..."
            className="input input-bordered w-full pr-12 placeholder:text-base-content/50 text-base-content"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <button className="btn btn-ghost btn-sm absolute right-0 top-0 h-full px-3 hover:bg-transparent">
            <Search className="h-5 w-5 text-base-content/60" />
          </button>
        </div>
      </div>

      {/* Tabel */}
      <div className="bg-base-100 rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="table table-zebra">
            <thead className="bg-base-200/50 text-xs sm:text-sm">
              <tr>
                <th className="font-semibold text-base-content">Periode</th>
                <th className="font-semibold text-base-content">Karyawan</th>
                <th className="hidden sm:table-cell font-semibold text-base-content">Jabatan</th>
                <th className="font-semibold text-base-content">Total Gaji</th>
                <th className="font-semibold text-base-content">Aksi</th>
              </tr>
            </thead>
            <tbody className="text-xs sm:text-sm">
              {isLoading ? (
                <tr>
                  <td colSpan={7} className="text-center py-4">
                    <span className="loading loading-spinner loading-md text-primary"></span>
                  </td>
                </tr>
              ) : currentData.length === 0 ? (
                <tr>
                  <td colSpan={7} className="text-center py-4 text-base-content">
                    Tidak ada data
                  </td>
                </tr>
              ) : (
                currentData.map((slip, index) => (
                  <tr key={slip.id} className="hover:bg-base-200/30">
                    <td>
                      <div className="flex items-center gap-2 text-base-content">
                        <Calendar className="h-3 w-3 sm:h-4 sm:w-4" />
                        <span className="line-clamp-1">{formatPeriode(slip.periode)}</span>
                      </div>
                    </td>
                    <td>
                      <div className="flex items-center gap-2">
                        <User className="h-3 w-3 sm:h-4 sm:w-4 text-primary" />
                        <span className="font-medium text-base-content line-clamp-1">{slip.employee.user.name}</span>
                      </div>
                    </td>
                    <td className="hidden sm:table-cell">
                      <div className="flex flex-col">
                        <Building2 className="h-3 w-3 sm:h-4 sm:w-4 text-primary" />
                        <span className="text-base-content">{slip.employee.position.name}</span>
                      </div>
                    </td>
                    <td>
                      <div className="flex items-center gap-2">
                        <DollarSign className="h-3 w-3 sm:h-4 sm:w-4 text-primary" />
                        <span className="font-medium text-base-content">{formatCurrency(slip.gaji.total)}</span>
                      </div>
                    </td>
                    <td>
                      <div className="flex gap-1 sm:gap-2">
                        <button
                          onClick={() => handleSlipSelection(slip)}
                          className="btn btn-square btn-xs sm:btn-sm btn-ghost hover:bg-primary/20 text-primary"
                          title="Lihat Detail"
                        >
                          <FileText className="h-3 w-3 sm:h-4 sm:w-4" />
                        </button>
                        <button
                          onClick={() => handleDownloadPDF(slip)}
                          className="btn btn-square btn-xs sm:btn-sm btn-ghost hover:bg-success/20 text-success"
                          title="Unduh PDF"
                        >
                          <Download className="h-3 w-3 sm:h-4 sm:w-4" />
                        </button>
                        <button
                          onClick={() => handleSendWhatsApp(slip)}
                          className="btn btn-square btn-xs sm:btn-sm btn-ghost hover:bg-green-500/20 text-green-500"
                          title="Kirim ke WhatsApp"
                        >
                          <MessageCircle className="h-3 w-3 sm:h-4 sm:w-4" />
                        </button>
                        <button
                          onClick={() => handleDeleteClick(slip)}
                          className="btn btn-square btn-xs sm:btn-sm btn-ghost hover:bg-error/20 text-error"
                          title="Hapus"
                        >
                          <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
                        </button>
                      </div>
                    </td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>
      </div>

      {/* Pagination */}
      {!isLoading && filteredSlipGaji.length > 0 && (
        <div className="flex justify-center items-center gap-2 py-4">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="btn btn-sm btn-ghost text-base-content"
          >
            «
          </button>

          {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`btn btn-sm ${
                currentPage === page
                ? 'bg-gradient-to-r from-breaktime-primary to-breaktime-secondary text-white hover:from-breaktime-primary/90 hover:to-breaktime-secondary/90 border-0'
                : 'btn-ghost text-base-content'
              }`}
            >
              {page}
            </button>
          ))}

          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="btn btn-sm btn-ghost text-base-content"
          >
            »
          </button>
        </div>
      )}

      {/* Detail Modal */}
      {isModalOpen && selectedSlip && (
        <div className="modal modal-open">
          <div className="modal-box w-11/12 max-w-3xl">
            <div className="flex flex-col gap-6">
              {/* Header */}
              <div>
                <h3 className="font-bold text-lg bg-gradient-to-r from-breaktime-primary to-breaktime-secondary bg-clip-text text-transparent">
                  Slip Gaji - {formatPeriode(selectedSlip.periode)}
                </h3>
                <p className="text-sm text-base-content/70">
                  Dicetak pada {formatDate(new Date())}
                </p>
              </div>

              {/* Informasi Karyawan */}
              <div className="bg-base-200/50 p-4 rounded-box space-y-2">
                <h4 className="font-semibold text-base-content">Informasi Karyawan</h4>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <div className="flex items-center gap-2">
                    <User className="h-4 w-4 text-primary" />
                    <div>
                      <p className="text-sm text-base-content/70">Nama</p>
                      <p className="font-medium text-base-content">{selectedSlip.employee.user.name}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4 text-primary" />
                    <div>
                      <p className="text-sm text-base-content/70">Jabatan</p>
                      <p className="font-medium text-base-content">{selectedSlip.employee.position.name}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Phone className="h-4 w-4 text-primary" />
                    <div>
                      <p className="text-sm text-base-content/70">Kontak</p>
                      <p className="font-medium text-base-content">{selectedSlip.employee.noTelp}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-primary" />
                    <div>
                      <p className="text-sm text-base-content/70">Tanggal Bergabung</p>
                      <p className="font-medium text-base-content">{formatDate(selectedSlip.employee.user.joinDate)}</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* Rincian Pendapatan */}
              <div className="space-y-2">
                <h4 className="font-semibold text-base-content">Rincian Pendapatan</h4>
                <div className="bg-base-200/50 rounded-box">
                  <table className="table table-sm">
                    <tbody className="text-sm">
                      <tr className="hover:bg-base-200">
                        <td className="flex items-center gap-2 text-base-content">
                          <div className="p-1.5 rounded-lg bg-primary/10">
                            <DollarSign className="h-4 w-4 text-primary" />
                          </div>
                          <span>Gaji Pokok</span>
                        </td>
                        <td className="text-right font-medium text-base-content">{formatCurrency(selectedSlip.gaji.pokok)}</td>
                      </tr>
                      <tr className="hover:bg-base-200">
                        <td className="flex items-center gap-2 text-base-content">
                          <div className="p-1.5 rounded-lg bg-success/10">
                            <Gift className="h-4 w-4 text-success" />
                          </div>
                          <span>Tunjangan Transport</span>
                        </td>
                        <td className="text-right font-medium text-base-content">{formatCurrency(selectedSlip.gaji.tunjangan.transport)}</td>
                      </tr>
                      <tr className="hover:bg-base-200">
                        <td className="flex items-center gap-2 text-base-content">
                          <div className="p-1.5 rounded-lg bg-success/10">
                            <Gift className="h-4 w-4 text-success" />
                          </div>
                          <span>Tunjangan Pulsa</span>
                        </td>
                        <td className="text-right font-medium text-base-content">{formatCurrency(selectedSlip.gaji.tunjangan.pulsa)}</td>
                      </tr>
                      <tr className="hover:bg-base-200">
                        <td className="flex items-center gap-2 text-base-content">
                          <div className="p-1.5 rounded-lg bg-success/10">
                            <Gift className="h-4 w-4 text-success" />
                          </div>
                          <span>Tunjangan Makan</span>
                        </td>
                        <td className="text-right font-medium text-base-content">{formatCurrency(selectedSlip.gaji.tunjangan.makan)}</td>
                      </tr>
                      <tr className="hover:bg-base-200">
                        <td className="flex items-center gap-2 text-base-content">
                          <div className="p-1.5 rounded-lg bg-success/10">
                            <Gift className="h-4 w-4 text-success" />
                          </div>
                          <span>Tunjangan BPJS</span>
                        </td>
                        <td className="text-right font-medium text-base-content">{formatCurrency(selectedSlip.gaji.tunjangan.bpjs)}</td>
                      </tr>
                      <tr className="hover:bg-base-200">
                        <td className="flex items-center gap-2 text-base-content">
                          <div className="p-1.5 rounded-lg bg-success/10">
                            <Gift className="h-4 w-4 text-success" />
                          </div>
                          <span>Tunjangan Penyesuaian</span>
                        </td>
                        <td className="text-right font-medium text-base-content">{formatCurrency(selectedSlip.gaji.tunjangan.penyesuaian)}</td>
                      </tr>
                      <tr className="hover:bg-base-200">
                        <td className="flex items-center gap-2 text-base-content">
                          <div className="p-1.5 rounded-lg bg-primary/10">
                            <Clock className="h-4 w-4 text-primary" />
                          </div>
                          <span>Komisi</span>
                        </td>
                        <td className="text-right font-medium text-base-content">{formatCurrency(selectedSlip.gaji.komisi)}</td>
                      </tr>
                      <tr className="hover:bg-base-200">
                        <td className="flex items-center gap-2 text-base-content">
                          <div className="p-1.5 rounded-lg bg-primary/10">
                            <Clock className="h-4 w-4 text-primary" />
                          </div>
                          <span>Lembur</span>
                        </td>
                        <td className="text-right font-medium text-base-content">{formatCurrency(selectedSlip.gaji.lembur)}</td>
                      </tr>
                      <tr className="hover:bg-base-200">
                        <td className="flex items-center gap-2 text-base-content">
                          <div className="p-1.5 rounded-lg bg-primary/10">
                            <Gift className="h-4 w-4 text-primary" />
                          </div>
                          <span>Bonus</span>
                        </td>
                        <td className="text-right font-medium text-base-content">{formatCurrency(selectedSlip.gaji.bonus)}</td>
                      </tr>
                      <tr className="font-semibold bg-base-200/70">
                        <td className="text-base-content">Total Pendapatan</td>
                        <td className="text-right text-primary">{formatCurrency(calculateTotalPendapatan(selectedSlip.gaji))}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Rincian Potongan */}
              <div className="space-y-2">
                <h4 className="font-semibold text-base-content">Rincian Potongan</h4>
                <div className="bg-base-200/50 rounded-box">
                  <table className="table table-sm">
                    <tbody className="text-sm">
                      <tr className="hover:bg-base-200">
                        <td className="flex items-center gap-2 text-base-content">
                          <div className="p-1.5 rounded-lg bg-error/10">
                            <MinusCircle className="h-4 w-4 text-error" />
                          </div>
                          <span>Kasbon</span>
                        </td>
                        <td className="text-right font-medium text-error">{formatCurrency(selectedSlip.gaji.potongan.kasbon)}</td>
                      </tr>
                      <tr className="hover:bg-base-200">
                        <td className="flex items-center gap-2 text-base-content">
                          <div className="p-1.5 rounded-lg bg-error/10">
                            <MinusCircle className="h-4 w-4 text-error" />
                          </div>
                          <span>Piutang</span>
                        </td>
                        <td className="text-right font-medium text-error">{formatCurrency(selectedSlip.gaji.potongan.piutang)}</td>
                      </tr>
                      <tr className="hover:bg-base-200">
                        <td className="flex items-center gap-2 text-base-content">
                          <div className="p-1.5 rounded-lg bg-error/10">
                            <MinusCircle className="h-4 w-4 text-error" />
                          </div>
                          <span>BPJS TP</span>
                        </td>
                        <td className="text-right font-medium text-error">{formatCurrency(selectedSlip.gaji.potongan.bpjsTP)}</td>
                      </tr>
                      <tr className="hover:bg-base-200">
                        <td className="flex items-center gap-2 text-base-content">
                          <div className="p-1.5 rounded-lg bg-error/10">
                            <MinusCircle className="h-4 w-4 text-error" />
                          </div>
                          <span>BPJS TK</span>
                        </td>
                        <td className="text-right font-medium text-error">{formatCurrency(selectedSlip.gaji.potongan.bpjsTK)}</td>
                      </tr>
                      <tr className="hover:bg-base-200">
                        <td className="flex items-center gap-2 text-base-content">
                          <div className="p-1.5 rounded-lg bg-error/10">
                            <MinusCircle className="h-4 w-4 text-error" />
                          </div>
                          <span>Lainnya</span>
                        </td>
                        <td className="text-right font-medium text-error">{formatCurrency(selectedSlip.gaji.potongan.lainnya)}</td>
                      </tr>
                      <tr className="hover:bg-base-200">
                        <td className="flex items-center gap-2 text-base-content">
                          <div className="p-1.5 rounded-lg bg-error/10">
                            <MinusCircle className="h-4 w-4 text-error" />
                          </div>
                          <span>Penyesuaian</span>
                        </td>
                        <td className="text-right font-medium text-error">{formatCurrency(selectedSlip.gaji.potongan.penyesuaian || 0)}</td>
                      </tr>
                      <tr className="font-semibold bg-base-200/70">
                        <td className="text-base-content">Total Potongan</td>
                        <td className="text-right text-error">{formatCurrency(calculateTotalPotongan(selectedSlip.gaji.potongan))}</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>

              {/* Total Gaji */}
              <div className="bg-base-200/50 p-4 rounded-box">
                <div className="flex justify-between items-center">
                  <span className="font-bold text-lg text-base-content">Total Gaji Bersih</span>
                  <span className="font-bold text-lg text-primary">{formatCurrency(selectedSlip.gaji.total)}</span>
                </div>
              </div>

              {/* Actions */}
              <div className="modal-action flex-wrap gap-2">
                <button
                  type="button"
                  className="btn btn-sm sm:btn-md bg-base-200 hover:bg-base-300 text-base-content border-base-300"
                  onClick={() => setIsModalOpen(false)}
                >
                  Tutup
                </button>
                <button
                  type="button"
                  className="btn btn-sm sm:btn-md bg-green-500/10 hover:bg-green-500/20 text-green-500 border-0"
                  onClick={() => handleSendWhatsApp(selectedSlip)}
                >
                  <MessageCircle className="h-4 w-4 sm:h-5 sm:w-5" />
                  Kirim WhatsApp
                </button>
                <button
                  type="button"
                  className="btn btn-sm sm:btn-md bg-gradient-to-r from-breaktime-primary to-breaktime-secondary text-white hover:from-breaktime-primary/90 hover:to-breaktime-secondary/90 border-0"
                  onClick={() => handleDownloadPDF(selectedSlip)}
                >
                  <Download className="h-4 w-4 sm:h-5 sm:w-5" />
                  Unduh PDF
                </button>
              </div>
            </div>
          </div>
          <div className="modal-backdrop bg-base-content/20" onClick={() => setIsModalOpen(false)}></div>
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && selectedSlipToDelete && (
        <div className="modal modal-open">
          <div className="modal-box bg-base-100">
            <h3 className="font-bold text-lg text-base-content">Konfirmasi Hapus</h3>
            <p className="py-4 text-base-content">
              Apakah Anda yakin ingin menghapus slip gaji {selectedSlipToDelete.employee.user.name} periode {formatPeriode(selectedSlipToDelete.periode)}?
            </p>
            <div className="modal-action">
              <button
                className="btn btn-ghost text-base-content"
                onClick={() => {
                  setIsDeleteModalOpen(false)
                  setSelectedSlipToDelete(null)
                }}
              >
                Batal
              </button>
              <button
                className="btn bg-error/10 hover:bg-error/20 text-error border-0"
                onClick={handleConfirmDelete}
              >
                Hapus
              </button>
            </div>
          </div>
          <div className="modal-backdrop bg-base-content/20" onClick={() => {
            setIsDeleteModalOpen(false)
            setSelectedSlipToDelete(null)
          }}></div>
        </div>
      )}
    </div>
  )
}