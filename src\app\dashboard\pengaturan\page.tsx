"use client"

import { useState, useEffect } from "react"
import { 
  Building2,
  Mail, 
  Phone,
  MapPin,
  User,
  Lock,
  Save,
  Edit,
  Upload,
  Settings as SettingIcon
} from "lucide-react"
import { toast } from "react-hot-toast"
import { CompanySettings, UserSettings } from "@/types/settings"
import Image from "next/image"

export default function SettingsPage() {
  // Settings State
  const [companySettings, setCompanySettings] = useState<CompanySettings>({
    id: "",
    name: "",
    email: "",
    phone: "",
    address: "",
    logo: "",
    createdAt: new Date(),
    updatedAt: new Date()
  })

  const [userSettings, setUserSettings] = useState({
    username: "",
    oldPassword: "",
    newPassword: "",
    confirmPassword: ""
  })

  // Edit States
  const [isEditingCompany, setIsEditingCompany] = useState(false)
  const [isEditingUser, setIsEditingUser] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Fetch settings
  useEffect(() => {
    fetchSettings()
  }, [])

  const fetchSettings = async () => {
    try {
      const response = await fetch("/api/settings")
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.message)
      }

      setCompanySettings(result.data.company)
      setUserSettings(prev => ({
        ...prev,
        username: result.data.user.username
      }))
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Gagal mengambil pengaturan")
    } finally {
      setIsLoading(false)
    }
  }

  // Handle Company Settings Update
  const handleCompanyUpdate = async () => {
    try {
      const response = await fetch("/api/settings", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          type: "company",
          data: {
            name: companySettings.name,
            email: companySettings.email,
            phone: companySettings.phone,
            address: companySettings.address
          }
        })
      })

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.message)
      }

      setCompanySettings(result.data)
      setIsEditingCompany(false)
      toast.success("Pengaturan perusahaan berhasil diperbarui")
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Gagal memperbarui pengaturan perusahaan")
    }
  }

  // Handle User Settings Update
  const handleUserUpdate = async () => {
    try {
      if (userSettings.newPassword !== userSettings.confirmPassword) {
        throw new Error("Password baru tidak cocok")
      }

      const response = await fetch("/api/settings", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          type: "user",
          data: {
            username: userSettings.username,
            oldPassword: userSettings.oldPassword,
            newPassword: userSettings.newPassword
          }
        })
      })

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.message)
      }

      setUserSettings(prev => ({
        ...prev,
        oldPassword: "",
        newPassword: "",
        confirmPassword: "",
        username: result.data.username
      }))
      setIsEditingUser(false)
      toast.success("Pengaturan pengguna berhasil diperbarui")
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Gagal memperbarui pengaturan pengguna")
    }
  }

  // Handle Logo Upload
  const handleLogoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    try {
      const file = e.target.files?.[0]
      if (!file) return

      const formData = new FormData()
      formData.append("logo", file)

      const response = await fetch("/api/settings/logo", {
        method: "POST",
        body: formData
      })

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.message)
      }

      setCompanySettings(prev => ({
        ...prev,
        logo: result.data.logoUrl
      }))
      toast.success("Logo berhasil diperbarui")
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Gagal mengupload logo")
    }
  }

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[200px]">
        <span className="loading loading-spinner loading-lg text-primary"></span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h2 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-breaktime-primary to-breaktime-secondary bg-clip-text text-transparent">
          Pengaturan
        </h2>
      </div>

      {/* Company Settings */}
      <div className="bg-base-100 rounded-box shadow-sm border border-base-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <Building2 className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-semibold text-base-content">Pengaturan Perusahaan</h3>
          </div>
          <button
            onClick={() => setIsEditingCompany(!isEditingCompany)}
            className="btn btn-sm btn-ghost text-primary"
          >
            {isEditingCompany ? (
              <Save className="h-4 w-4" />
            ) : (
              <Edit className="h-4 w-4" />
            )}
          </button>
        </div>

        {/* Logo Upload */}
        <div className="mb-6">
          <label className="label">
            <span className="label-text text-base-content/70">Logo Perusahaan</span>
          </label>
          <div className="flex items-center gap-4">
            <div className="relative w-20 h-20 rounded-lg overflow-hidden bg-base-200">
              <Image
                src={companySettings.logo || "/placeholder.png"}
                alt="Logo"
                fill
                className="object-contain"
              />
            </div>
            {isEditingCompany && (
              <label className="btn btn-sm btn-outline">
                <Upload className="h-4 w-4" />
                Upload Logo
                <input
                  type="file"
                  className="hidden"
                  accept="image/*"
                  onChange={handleLogoUpload}
                />
              </label>
            )}
          </div>
        </div>

        <div className="grid gap-6 sm:grid-cols-2">
          {/* Company Name */}
          <div className="form-control">
            <label className="label">
              <span className="label-text text-base-content/70">Nama Perusahaan</span>
            </label>
            <div className="relative">
              <input
                type="text"
                className="input input-bordered w-full pr-10 text-base-content"
                value={companySettings.name}
                onChange={(e) => setCompanySettings(prev => ({ ...prev, name: e.target.value }))}
                disabled={!isEditingCompany}
              />
              <Building2 className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-base-content/40" />
            </div>
          </div>

          {/* Company Email */}
          <div className="form-control">
            <label className="label">
              <span className="label-text text-base-content/70">Email</span>
            </label>
            <div className="relative">
              <input
                type="email"
                className="input input-bordered w-full pr-10 text-base-content"
                value={companySettings.email}
                onChange={(e) => setCompanySettings(prev => ({ ...prev, email: e.target.value }))}
                disabled={!isEditingCompany}
              />
              <Mail className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-base-content/40" />
            </div>
          </div>

          {/* Company Phone */}
          <div className="form-control">
            <label className="label">
              <span className="label-text text-base-content/70">Telepon</span>
            </label>
            <div className="relative">
              <input
                type="tel"
                className="input input-bordered w-full pr-10 text-base-content"
                value={companySettings.phone}
                onChange={(e) => setCompanySettings(prev => ({ ...prev, phone: e.target.value }))}
                disabled={!isEditingCompany}
              />
              <Phone className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-base-content/40" />
            </div>
          </div>

          {/* Company Address */}
          <div className="form-control">
            <label className="label">
              <span className="label-text text-base-content/70">Alamat</span>
            </label>
            <div className="relative">
              <input
                type="text"
                className="input input-bordered w-full pr-10 text-base-content"
                value={companySettings.address}
                onChange={(e) => setCompanySettings(prev => ({ ...prev, address: e.target.value }))}
                disabled={!isEditingCompany}
              />
              <MapPin className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-base-content/40" />
            </div>
          </div>
        </div>

        {/* Save Button */}
        {isEditingCompany && (
          <div className="mt-6 flex justify-end">
            <button
              onClick={handleCompanyUpdate}
              className="btn btn-primary"
            >
              <Save className="h-4 w-4" />
              Simpan Perubahan
            </button>
          </div>
        )}
      </div>

      {/* User Settings */}
      <div className="bg-base-100 rounded-box shadow-sm border border-base-200 p-6">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center gap-2">
            <User className="h-5 w-5 text-primary" />
            <h3 className="text-lg font-semibold text-base-content">Pengaturan Pengguna</h3>
          </div>
          <button
            onClick={() => setIsEditingUser(!isEditingUser)}
            className="btn btn-sm btn-ghost text-primary"
          >
            {isEditingUser ? (
              <Save className="h-4 w-4" />
            ) : (
              <Edit className="h-4 w-4" />
            )}
          </button>
        </div>

        <div className="grid gap-6 sm:grid-cols-2">
          {/* Username */}
          <div className="form-control">
            <label className="label">
              <span className="label-text text-base-content/70">Username</span>
            </label>
            <div className="relative">
              <input
                type="text"
                className="input input-bordered w-full pr-10 text-base-content"
                value={userSettings.username}
                onChange={(e) => setUserSettings(prev => ({ ...prev, username: e.target.value }))}
                disabled={!isEditingUser}
              />
              <User className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-base-content/40" />
            </div>
          </div>

          {/* Old Password */}
          {isEditingUser && (
            <div className="form-control">
              <label className="label">
                <span className="label-text text-base-content/70">Password Lama</span>
              </label>
              <div className="relative">
                <input
                  type="password"
                  className="input input-bordered w-full pr-10 text-base-content"
                  value={userSettings.oldPassword}
                  onChange={(e) => setUserSettings(prev => ({ ...prev, oldPassword: e.target.value }))}
                />
                <Lock className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-base-content/40" />
              </div>
            </div>
          )}

          {/* New Password */}
          {isEditingUser && (
            <div className="form-control">
              <label className="label">
                <span className="label-text text-base-content/70">Password Baru</span>
              </label>
              <div className="relative">
                <input
                  type="password"
                  className="input input-bordered w-full pr-10 text-base-content"
                  value={userSettings.newPassword}
                  onChange={(e) => setUserSettings(prev => ({ ...prev, newPassword: e.target.value }))}
                />
                <Lock className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-base-content/40" />
              </div>
            </div>
          )}

          {/* Confirm Password */}
          {isEditingUser && (
            <div className="form-control">
              <label className="label">
                <span className="label-text text-base-content/70">Konfirmasi Password</span>
              </label>
              <div className="relative">
                <input
                  type="password"
                  className="input input-bordered w-full pr-10 text-base-content"
                  value={userSettings.confirmPassword}
                  onChange={(e) => setUserSettings(prev => ({ ...prev, confirmPassword: e.target.value }))}
                />
                <Lock className="absolute right-3 top-1/2 -translate-y-1/2 h-5 w-5 text-base-content/40" />
              </div>
            </div>
          )}
        </div>

        {/* Save Button */}
        {isEditingUser && (
          <div className="mt-6 flex justify-end">
            <button
              onClick={handleUserUpdate}
              className="btn btn-primary"
            >
              <Save className="h-4 w-4" />
              Simpan Perubahan
            </button>
          </div>
        )}
      </div>
    </div>
  )
} 