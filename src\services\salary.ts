import { CreateSalaryInput, Salary, SalaryResponse, UpdateSalaryInput } from "@/types/salary"

const API_URL = process.env.NEXT_PUBLIC_API_URL || "http://localhost:3000/api"

export async function getSalaries(): Promise<Salary[]> {
  try {
    const response = await fetch(`${API_URL}/salaries`)
    const data: SalaryResponse = await response.json()

    if (!data.success) {
      throw new Error(data.error || "Gagal mengambil data gaji")
    }

    return data.data as Salary[]
  } catch (error) {
    console.error("Error fetching salaries:", error)
    throw error
  }
}

export async function getSalary(id: string): Promise<Salary> {
  try {
    const response = await fetch(`${API_URL}/salaries/${id}`)
    const data: SalaryResponse = await response.json()

    if (!data.success) {
      throw new Error(data.error || "Gagal mengambil data gaji")
    }

    return data.data as Salary
  } catch (error) {
    console.error("Error fetching salary:", error)
    throw error
  }
}

export async function createSalary(input: CreateSalaryInput): Promise<Salary> {
  try {
    const response = await fetch(`${API_URL}/salaries`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(input),
    })
    const data: SalaryResponse = await response.json()

    if (!data.success) {
      throw new Error(data.error || "Gagal membuat data gaji")
    }

    return data.data as Salary
  } catch (error) {
    console.error("Error creating salary:", error)
    throw error
  }
}

export async function updateSalary(input: UpdateSalaryInput): Promise<Salary> {
  try {
    const response = await fetch(`${API_URL}/salaries/${input.id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(input),
    })
    const data: SalaryResponse = await response.json()

    if (!data.success) {
      throw new Error(data.error || "Gagal mengupdate data gaji")
    }

    return data.data as Salary
  } catch (error) {
    console.error("Error updating salary:", error)
    throw error
  }
}

export async function deleteSalary(id: string): Promise<void> {
  try {
    const response = await fetch(`${API_URL}/salaries/${id}`, {
      method: "DELETE",
    })
    const data: SalaryResponse = await response.json()

    if (!data.success) {
      throw new Error(data.error || "Gagal menghapus data gaji")
    }
  } catch (error) {
    console.error("Error deleting salary:", error)
    throw error
  }
}

// Format currency ke format Rupiah
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat("id-ID", {
    style: "currency",
    currency: "IDR",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0,
  }).format(amount)
}

// Format periode gaji
export function formatPeriod(start: Date, end: Date): string {
  const startDate = new Date(start)
  const endDate = new Date(end)
  
  const formatMonth = new Intl.DateTimeFormat("id-ID", { month: "long" })
  const formatYear = new Intl.DateTimeFormat("id-ID", { year: "numeric" })
  
  if (startDate.getMonth() === endDate.getMonth() && startDate.getFullYear() === endDate.getFullYear()) {
    return `${formatMonth.format(startDate)} ${formatYear.format(startDate)}`
  }
  
  return `${formatMonth.format(startDate)} - ${formatMonth.format(endDate)} ${formatYear.format(endDate)}`
}

// Hitung total gaji
export function calculateSalary({
  totalOmset,
  omsetPercentage,
  deductions,
  bonuses
}: {
  totalOmset: number
  omsetPercentage: number
  deductions: { loans: number; bpjs: number; other: number }
  bonuses: { amount: number }
}): {
  salaryAmount: number
  totalDeductions: number
  finalAmount: number
} {
  const salaryAmount = (totalOmset * omsetPercentage) / 100
  const totalDeductions = deductions.loans + deductions.bpjs + deductions.other
  const finalAmount = salaryAmount - totalDeductions + bonuses.amount

  return {
    salaryAmount,
    totalDeductions,
    finalAmount
  }
} 