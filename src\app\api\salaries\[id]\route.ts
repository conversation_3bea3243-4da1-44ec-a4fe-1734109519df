import { NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"
import { SalaryResponse, Salary } from "@/types/salary"

const prisma = new PrismaClient()

// GET /api/salaries/:id
export async function GET(request: Request, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const salary = await prisma.salary.findUnique({
      where: { id: params.id },
      include: {
        employee: {
          include: {
            user: {
              select: {
                name: true
              }
            },
            position: true
          }
        }
      }
    })

    if (!salary) {
      const response: SalaryResponse = {
        success: false,
        message: "Data gaji tidak ditemukan",
        error: "Not found"
      }
      return NextResponse.json(response, { status: 404 })
    }

    // Transform data to match expected format
    const transformedSalary: Salary = {
      id: salary.id,
      employeeId: salary.employeeId,
      employee: {
        name: salary.employee.user.name,
        position: {
          id: salary.employee.position.id,
          name: salary.employee.position.name,
          omsetPercentage: salary.employee.position.omsetPercentage
        }
      },
      periodStart: salary.periodStart,
      periodEnd: salary.periodEnd,
      baseOmset: salary.baseOmset,
      totalOmset: salary.totalOmset,
      salaryAmount: salary.salaryAmount,
      earnings: {
        baseSalary: 0,
        transportation: 0,
        phoneAllowance: 0,
        mealAllowance: 0,
        commission: 0,
        overtime: 0,
        bpjsAllowance: 0,
        adjustmentRatio: 0
      },
      deductions: salary.deductions,
      bonuses: salary.bonuses,
      finalAmount: salary.finalAmount,
      isPaid: salary.isPaid,
      paidAt: salary.paidAt,
      createdAt: salary.createdAt,
      updatedAt: salary.updatedAt
    }

    const response: SalaryResponse = {
      success: true,
      message: "Data gaji berhasil diambil",
      data: transformedSalary
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error("Error fetching salary:", error)
    const response: SalaryResponse = {
      success: false,
      message: "Terjadi kesalahan saat mengambil data gaji",
      error: error instanceof Error ? error.message : "Unknown error"
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// PUT /api/salaries/:id
export async function PUT(request: Request, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const body = await request.json()
    const salary = await prisma.salary.update({
      where: { id: params.id },
      data: {
        baseOmset: body.baseOmset,
        totalOmset: body.totalOmset,
        deductions: body.deductions,
        bonuses: body.bonuses,
        isPaid: body.isPaid,
        paidAt: body.isPaid ? new Date() : null
      },
      include: {
        employee: {
          include: {
            user: {
              select: {
                name: true
              }
            },
            position: true
          }
        }
      }
    })

    // Transform data to match expected format
    const transformedSalary: Salary = {
      id: salary.id,
      employeeId: salary.employeeId,
      employee: {
        name: salary.employee.user.name,
        position: {
          id: salary.employee.position.id,
          name: salary.employee.position.name,
          omsetPercentage: salary.employee.position.omsetPercentage
        }
      },
      periodStart: salary.periodStart,
      periodEnd: salary.periodEnd,
      baseOmset: salary.baseOmset,
      totalOmset: salary.totalOmset,
      salaryAmount: salary.salaryAmount,
      earnings: {
        baseSalary: 0,
        transportation: 0,
        phoneAllowance: 0,
        mealAllowance: 0,
        commission: 0,
        overtime: 0,
        bpjsAllowance: 0,
        adjustmentRatio: 0
      },
      deductions: salary.deductions,
      bonuses: salary.bonuses,
      finalAmount: salary.finalAmount,
      isPaid: salary.isPaid,
      paidAt: salary.paidAt,
      createdAt: salary.createdAt,
      updatedAt: salary.updatedAt
    }

    const response: SalaryResponse = {
      success: true,
      message: "Data gaji berhasil diupdate",
      data: transformedSalary
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error("Error updating salary:", error)
    const response: SalaryResponse = {
      success: false,
      message: "Terjadi kesalahan saat mengupdate data gaji",
      error: error instanceof Error ? error.message : "Unknown error"
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// DELETE /api/salaries/:id
export async function DELETE(request: Request, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    await prisma.salary.delete({
      where: { id: params.id }
    })

    const response: SalaryResponse = {
      success: true,
      message: "Data gaji berhasil dihapus"
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error("Error deleting salary:", error)
    const response: SalaryResponse = {
      success: false,
      message: "Terjadi kesalahan saat menghapus data gaji",
      error: error instanceof Error ? error.message : "Unknown error"
    }
    return NextResponse.json(response, { status: 500 })
  }
} 