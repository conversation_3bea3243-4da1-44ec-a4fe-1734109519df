# Database Backups

Folder ini berisi backup database sistem gaji. Ada dua jenis backup yang tersedia:

1. **SQL Backup** (`db_backup.sql`)
   - Backup lengkap database dalam format SQL
   - Dibuat menggunakan `pg_dump`
   - Dapat digunakan untuk restore database secara penuh

2. **JSON Backup** (`backup_[timestamp].json`)
   - Backup data dalam format JSON
   - Berisi data dari semua tabel:
     - Users
     - Employees
     - Positions
     - Salaries
     - Kasbons
     - Slip <PERSON>
     - Daily Commissions
     - Outlets
   - Termasuk timestamp kapan backup dibuat
   - Format yang mudah dibaca dan diparse

## Cara Membuat Backup

1. **SQL Backup**:
   ```bash
   pg_dump -U postgres -d sistemgaji > ./@backups/db_backup.sql
   ```

2. **JSON Backup**:
   ```bash
   node ./@backups/backup.js
   ```

## Cara Restore

1. **SQL Backup**:
   ```bash
   psql -U postgres -d sistemgaji < ./@backups/db_backup.sql
   ```

2. **JSON Backup**:
   - <PERSON>akan script restore yang sesuai (akan dibuat jika diperlukan)
   - Data JSON dapat diimpor kembali ke database menggunakan Prisma

## Penting

- Pastikan untuk membuat backup secara berkala
- Simpan backup di tempat yang aman
- Verifikasi backup secara berkala untuk memastikan data dapat di-restore
- Jangan hapus backup lama sebelum memverifikasi backup baru 