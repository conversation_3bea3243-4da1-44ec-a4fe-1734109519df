"use client"

import { motion } from "framer-motion"
import {
  TrendingUp,
  Users,
  Target,
  Wallet,
  ArrowUp,
  ArrowDown,
  DollarSign,
  Clock,
  MoreVertical,
  Briefcase,
  Receipt,
  AlertCircle,
  Calendar
} from "lucide-react"
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart,
  Legend
} from "recharts"
import { useState, useEffect } from "react"

// Custom Tooltip Component
const CustomTooltip = ({ active, payload, label }: any) => {
  if (active && payload && payload.length) {
    return (
      <div className="rounded-lg border bg-base-100 p-3 shadow-lg">
        <div className="grid grid-cols-2 gap-4">
          <div className="flex flex-col">
            <span className="text-[0.70rem] uppercase font-medium text-base-content/90">
              Omset
            </span>
            <span className="font-bold bg-gradient-to-r from-breaktime-primary to-breaktime-secondary bg-clip-text text-transparent">
              Rp {Number(payload[0]?.value || 0).toLocaleString('id-ID')}
            </span>
          </div>
          <div className="flex flex-col">
            <span className="text-[0.70rem] uppercase font-medium text-base-content/90">
              Total Gaji
            </span>
            <span className="font-bold text-success">
              Rp {Number(payload[1]?.value || 0).toLocaleString('id-ID')}
            </span>
          </div>
          <div className="flex flex-col">
            <span className="text-[0.70rem] uppercase font-medium text-base-content/90">
              Persentase Gaji
            </span>
            <span className="font-bold text-info">
              {payload[2]?.value || 0}%
            </span>
          </div>
        </div>
        <div className="mt-2 flex items-center">
          <span className="text-[0.70rem] font-medium text-base-content/90">{label}</span>
        </div>
      </div>
    )
  }
  return null
}

const containerVariants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0
  }
}

// Icon mapping untuk dynamic icon rendering
const IconMap: { [key: string]: any } = {
  TrendingUp,
  Users,
  Target,
  Wallet,
  DollarSign,
  Briefcase,
  Receipt,
  AlertCircle
}

export default function DashboardPage() {
  const [isLoading, setIsLoading] = useState(true)
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1)
  const [selectedYear, setSelectedYear] = useState(2025)
  const [dashboardData, setDashboardData] = useState<{
    statsCards: any[]
    quickStats: any[]
    weeklyData: any[]
    recentActivities: any[]
  }>({
    statsCards: [],
    quickStats: [],
    weeklyData: [],
    recentActivities: []
  })

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        const response = await fetch(`/api/dashboard?month=${selectedMonth}&year=${selectedYear}`)
        if (!response.ok) throw new Error('Failed to fetch dashboard data')
        const data = await response.json()
        setDashboardData(data)
      } catch (error) {
        console.error('Error fetching dashboard data:', error)
        // Tambahkan toast notification disini jika diperlukan
      } finally {
        setIsLoading(false)
      }
    }

    fetchDashboardData()
  }, [selectedMonth, selectedYear])

  // Fungsi untuk mendapatkan nama bulan
  const getMonthName = (month: number) => {
    return new Date(2024, month - 1, 1).toLocaleDateString('id-ID', { month: 'long' })
  }

  // Fungsi untuk mendapatkan array tahun
  const getYearRange = () => {
    const currentYear = new Date().getFullYear()
    const baseYear = 2025
    const yearsToAdd = Math.floor((currentYear - baseYear) / 5) * 5
    const startYear = baseYear + yearsToAdd
    return Array.from({ length: 5 }, (_, i) => startYear + i)
  }

  // Array untuk tahun
  const years = getYearRange()

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <span className="loading loading-spinner loading-lg"></span>
      </div>
    )
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      className="space-y-6"
    >
      {/* Stats Cards */}
      <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
        {dashboardData.statsCards.map((stat, index) => {
          const Icon = IconMap[stat.icon]
          return (
            <motion.div
              key={stat.title}
              variants={itemVariants}
              className="card bg-base-100 shadow-md hover:shadow-xl transition-shadow"
            >
              <div className="card-body">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-base-content/80 font-medium">{stat.title}</p>
                    <h3 className={`text-2xl font-bold mt-1 bg-gradient-to-r ${stat.gradient} bg-clip-text text-transparent`}>
                      {stat.value}
                    </h3>
                    <div className={`flex items-center gap-1 mt-2 ${
                      stat.isIncrease ? "text-success" : "text-error"
                    }`}>
                      {stat.isIncrease ? (
                        <ArrowUp className="w-4 h-4" />
                      ) : (
                        <ArrowDown className="w-4 h-4" />
                      )}
                      <span className="text-sm font-medium">{stat.change}</span>
                    </div>
                  </div>
                  <div className={`rounded-xl p-3 bg-gradient-to-r ${stat.gradient} text-white`}>
                    <Icon className="w-6 h-6" />
                  </div>
                </div>
              </div>
            </motion.div>
          )
        })}
      </div>

      {/* Quick Stats */}
      <motion.div variants={itemVariants} className="grid grid-cols-1 gap-4 sm:grid-cols-2 lg:grid-cols-4">
        {dashboardData.quickStats.map((stat) => {
          const Icon = IconMap[stat.icon]
          return (
            <div key={stat.title} className="card bg-base-100 shadow-sm hover:shadow-md transition-shadow">
              <div className="card-body p-4">
                <div className="flex items-center gap-3">
                  <div className={`rounded-lg p-2 bg-gradient-to-r ${stat.gradient} text-white`}>
                    <Icon className="w-5 h-5" />
                  </div>
                  <div>
                    <p className="text-sm text-base-content/70">{stat.title}</p>
                    <p className="text-lg font-bold text-base-content">{stat.value}</p>
                  </div>
                </div>
              </div>
            </div>
          )
        })}
      </motion.div>

      {/* Charts & Activities */}
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        {/* Omset Chart */}
        <motion.div
          variants={itemVariants}
          className="card bg-base-100 shadow-md hover:shadow-xl transition-shadow lg:col-span-2"
        >
          <div className="card-body">
            <div className="flex flex-col gap-4">
              <div className="flex items-center justify-between">
                <div className="flex flex-col">
                  <h3 className="card-title text-base-content">Grafik Bulanan</h3>
                  <div className="text-sm text-base-content/80 font-medium">
                    Perbandingan omset dan total gaji
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <div className="join">
                    <select
                      className="select select-bordered join-item text-base-content"
                      value={selectedMonth}
                      onChange={(e) => setSelectedMonth(Number(e.target.value))}
                    >
                      {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                        <option key={month} value={month}>
                          {getMonthName(month)}
                        </option>
                      ))}
                    </select>
                    <select
                      className="select select-bordered join-item text-base-content"
                      value={selectedYear}
                      onChange={(e) => setSelectedYear(Number(e.target.value))}
                    >
                      {years.map((year) => (
                        <option key={year} value={year}>
                          {year}
                        </option>
                      ))}
                    </select>
                  </div>
                  <div className="tooltip" data-tip="Filter berdasarkan bulan dan tahun">
                    <button className="btn btn-circle btn-ghost btn-sm">
                      <Calendar className="w-4 h-4 text-base-content/70" />
                    </button>
                  </div>
                </div>
              </div>
              <div className="flex gap-2">
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 rounded-full bg-breaktime-primary"></div>
                  <span className="text-xs text-base-content/70">Omset</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 rounded-full bg-success"></div>
                  <span className="text-xs text-base-content/70">Total Gaji</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 rounded-full bg-info"></div>
                  <span className="text-xs text-base-content/70">Persentase</span>
                </div>
              </div>
            </div>
            <div className="h-[300px] w-full mt-4">
              <ResponsiveContainer width="100%" height="100%">
                <AreaChart
                  data={dashboardData.weeklyData}
                  margin={{
                    top: 5,
                    right: 30,
                    left: 20,
                    bottom: 5,
                  }}
                >
                  <defs>
                    <linearGradient id="omset" x1="0" y1="0" x2="0" y2="1">
                      <stop
                        offset="0%"
                        stopColor="var(--breaktime-primary)"
                        stopOpacity={0.5}
                      />
                      <stop
                        offset="100%"
                        stopColor="var(--breaktime-primary)"
                        stopOpacity={0}
                      />
                    </linearGradient>
                    <linearGradient id="totalGaji" x1="0" y1="0" x2="0" y2="1">
                      <stop
                        offset="0%"
                        stopColor="hsl(var(--su))"
                        stopOpacity={0.5}
                      />
                      <stop
                        offset="100%"
                        stopColor="hsl(var(--su))"
                        stopOpacity={0}
                      />
                    </linearGradient>
                    <linearGradient id="persentase" x1="0" y1="0" x2="0" y2="1">
                      <stop
                        offset="0%"
                        stopColor="hsl(var(--in))"
                        stopOpacity={0.5}
                      />
                      <stop
                        offset="100%"
                        stopColor="hsl(var(--in))"
                        stopOpacity={0}
                      />
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" className="stroke-base-content/20" />
                  <XAxis
                    dataKey="name"
                    className="text-xs text-base-content/70"
                    tickFormatter={(value) => value.split(', ')[0]}
                  />
                  <YAxis
                    yAxisId="left"
                    className="text-xs text-base-content/70"
                    tickFormatter={(value) => `${value/1000000}M`}
                  />
                  <YAxis
                    yAxisId="right"
                    orientation="right"
                    className="text-xs text-base-content/70"
                    tickFormatter={(value) => `${value}%`}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Legend />
                  <Area
                    yAxisId="left"
                    type="monotone"
                    dataKey="omset"
                    name="Omset"
                    stroke="var(--breaktime-primary)"
                    fillOpacity={1}
                    fill="url(#omset)"
                    strokeWidth={2}
                  />
                  <Area
                    yAxisId="left"
                    type="monotone"
                    dataKey="totalGaji"
                    name="Total Gaji"
                    stroke="hsl(var(--su))"
                    fillOpacity={1}
                    fill="url(#totalGaji)"
                    strokeWidth={2}
                  />
                  <Area
                    yAxisId="right"
                    type="monotone"
                    dataKey="persentaseGaji"
                    name="Persentase"
                    stroke="hsl(var(--in))"
                    fillOpacity={1}
                    fill="url(#persentase)"
                    strokeWidth={2}
                  />
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </div>
        </motion.div>

        {/* Recent Activities */}
        <motion.div
          variants={itemVariants}
          className="card bg-base-100 shadow-md hover:shadow-xl transition-shadow"
        >
          <div className="card-body">
            <div className="flex items-center justify-between">
              <h3 className="card-title text-base-content">Slip Gaji Terbaru</h3>
              <button className="btn btn-ghost btn-sm btn-circle">
                <MoreVertical className="w-5 h-5" />
              </button>
            </div>
            <div className="mt-4 space-y-4">
              {dashboardData.recentActivities.map((activity) => {
                const Icon = IconMap[activity.icon]
                return (
                  <div
                    key={activity.id}
                    className="flex flex-col p-3 rounded-lg hover:bg-base-200/50 transition-colors"
                  >
                    <div className="flex items-start gap-3">
                      <div className={`rounded-lg p-2 bg-gradient-to-r ${activity.gradient} text-white mt-1`}>
                        <Icon className="w-4 h-4" />
                      </div>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <p className="text-sm font-medium text-base-content">{activity.user}</p>
                          <span className="badge badge-sm badge-secondary badge-outline text-xs">
                            {activity.category}
                          </span>
                        </div>
                        <p className="text-sm text-base-content/70">{activity.action}</p>
                        <div className="flex items-center gap-2 mt-1">
                          <Clock className="w-3 h-3 text-base-content/50" />
                          <span className="text-xs text-base-content/50">{activity.time}</span>
                        </div>
                      </div>
                      <div className="text-sm font-bold text-base-content">
                        {activity.amount}
                      </div>
                    </div>
                    <div className="mt-2 grid grid-cols-3 gap-2 text-xs">
                      <div className="flex flex-col items-center p-1 rounded bg-base-200/50">
                        <span className="text-base-content/60">Gaji Pokok</span>
                        <span className="font-medium text-base-content">{activity.details.gajiPokok}</span>
                      </div>
                      <div className="flex flex-col items-center p-1 rounded bg-base-200/50">
                        <span className="text-base-content/60">Komisi</span>
                        <span className="font-medium text-success">{activity.details.komisi}</span>
                      </div>
                      <div className="flex flex-col items-center p-1 rounded bg-base-200/50">
                        <span className="text-base-content/60">Tunjangan</span>
                        <span className="font-medium text-info">{activity.details.tunjangan}</span>
                      </div>
                    </div>
                  </div>
                )
              })}
            </div>
          </div>
        </motion.div>
      </div>
    </motion.div>
  )
} 