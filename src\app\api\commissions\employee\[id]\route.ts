import { NextRequest, NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"
import type { DailyCommissionResponse, DailyCommission, MonthlyCommissionSummary } from "@/types/commission"

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  }
})

type Props = {
  params: Promise<{ id: string }>
}

export async function GET(request: NextRequest, props: Props): Promise<NextResponse> {
  const params = await props.params;
  console.log(`API dipanggil untuk karyawan dengan ID: ${params.id}`)
  
  try {
    const { searchParams } = request.nextUrl
    const month = parseInt(searchParams.get("month") || "")
    const year = parseInt(searchParams.get("year") || "")
    
    console.log(`Parameter API: month=${month}, year=${year}`)

    if (!month || !year) {
      console.error("Parameter bulan atau tahun tidak valid")
      const response: DailyCommissionResponse = {
        success: false,
        message: "Bulan dan tahun harus diisi",
        error: "Month and year are required"
      }
      return NextResponse.json(response, { status: 400 })
    }

    // Tentukan rentang tanggal
    const startDate = new Date(Date.UTC(year, month - 1, 1))
    const endDate = new Date(Date.UTC(year, month, 0))
    
    console.log(`Mencari komisi dari ${startDate.toISOString()} hingga ${endDate.toISOString()}`)

    // Get daily commissions for employee in specified month
    const dailyCommissions = await prisma.dailyCommission.findMany({
      where: {
        employeeId: params.id,
        date: {
          gte: startDate,
          lte: endDate
        }
      },
      include: {
        employee: {
          include: {
            user: true,
            position: true
          }
        }
      },
      orderBy: {
        date: "desc"
      }
    })
    
    console.log(`Ditemukan ${dailyCommissions.length} komisi harian`)
    
    // Log data komisi yang ditemukan untuk debugging
    dailyCommissions.forEach((commission, index) => {
      console.log(`Komisi #${index + 1}:`, {
        tanggal: commission.date,
        omzet: commission.omzet,
        komisi: commission.commission
      })
    })

    // Calculate monthly summary
    const totalOmzet = dailyCommissions.reduce(
      (sum, commission) => sum + (commission.omzet || 0),
      0
    )
    const totalCommission = dailyCommissions.reduce(
      (sum, commission) => sum + (commission.commission || 0),
      0
    )
    
    console.log(`Total Omzet: ${totalOmzet}, Total Komisi: ${totalCommission}`)

    const monthlyData: MonthlyCommissionSummary = {
      totalOmzet,
      totalCommission,
      dailyCommissions: dailyCommissions as unknown as DailyCommission[],
      month,
      year
    }

    const response: DailyCommissionResponse = {
      success: true,
      message: "Data komisi berhasil diambil",
      data: monthlyData
    }
    
    console.log("Mengembalikan respons sukses dengan data", {
      totalOmzet,
      totalCommission,
      jumlahKomisiHarian: dailyCommissions.length
    })

    return NextResponse.json(response)
  } catch (error) {
    console.error("Error getting daily commissions:", error)
    const errorMessage = error instanceof Error ? error.message : "Unknown error"
    console.error(`Detail error: ${errorMessage}`)
    
    const response: DailyCommissionResponse = {
      success: false,
      message: "Gagal mengambil data komisi",
      error: errorMessage
    }
    return NextResponse.json(response, { status: 500 })
  }
} 