import { 
  <PERSON><PERSON><PERSON>, 
  <PERSON><PERSON>KasbonDTO, 
  UpdateKasbonDTO,
  KasbonType
} from "@/types/kasbon"
import { Role, EmployeeStatus } from "@/types/employee"
import * as XLSX from "xlsx"
import { getOutlets } from "@/services/outlet"

// Dummy data
let kasbons: Ka<PERSON><PERSON>[] = [
  {
    id: "1",
    employeeId: "1",
    employee: {
      id: "1",
      userId: "1",
      user: {
        id: "1",
        username: "joh<PERSON><PERSON>",
        name: "<PERSON>",
        role: Role.EMPLOYEE,
        department: "IT",
        position: "Staff",
        joinDate: new Date("2024-01-01")
      },
      positionId: "1",
      position: {
        id: "1",
        name: "Staff",
        omsetPercentage: 10,
        isKontrak: false,
        targetKontrak: 0,
        gajiPokok: 3000000
      },
      outletId: "1",
      nik: "1234567890",
      alamat: "Jl. Contoh No. 123",
      noTelp: "081234567890",
      status: EmployeeStatus.ACTIVE,
      salaries: [],
      createdAt: new Date(),
      updatedAt: new Date()
    },
    amount: 1000000,
    type: "KASBON",
    status: "PENDING",
    notes: "Kasbon untuk keperluan sekolah anak",
    createdAt: new Date(),
    updatedAt: new Date()
  }
]

// Get all kasbon
export async function getKasbon(params?: {
  status?: string
  type?: string
  search?: string
}): Promise<Kasbon[]> {
  try {
    const queryParams = new URLSearchParams()
    if (params?.status) queryParams.append('status', params.status)
    if (params?.type) queryParams.append('type', params.type)
    if (params?.search) queryParams.append('search', params.search)

    const response = await fetch(`/api/kasbon?${queryParams.toString()}`)
    if (!response.ok) throw new Error('Failed to fetch kasbon')
    return response.json()
  } catch (error) {
    console.error('Error fetching kasbon:', error)
    throw error
  }
}

// Create new kasbon
export async function createKasbon(data: CreateKasbonDTO): Promise<Kasbon> {
  try {
    const response = await fetch('/api/kasbon', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    })
    
    if (!response.ok) throw new Error('Failed to create kasbon')
    return response.json()
  } catch (error) {
    console.error('Error creating kasbon:', error)
    throw error
  }
}

// Update kasbon status
export async function updateKasbon(data: UpdateKasbonDTO): Promise<Kasbon> {
  try {
    const response = await fetch(`/api/kasbon/${data.id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data)
    })
    
    if (!response.ok) throw new Error('Failed to update kasbon')
    return response.json()
  } catch (error) {
    console.error('Error updating kasbon:', error)
    throw error
  }
}

// Delete kasbon
export async function deleteKasbon(id: string): Promise<void> {
  try {
    const response = await fetch(`/api/kasbon/${id}`, {
      method: 'DELETE'
    })
    
    if (!response.ok) throw new Error('Failed to delete kasbon')
  } catch (error) {
    console.error('Error deleting kasbon:', error)
    throw error
  }
}

// Format currency
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Format date
export function formatDate(date: Date | string): string {
  const d = new Date(date)
  return d.toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  })
}

// Get status badge color
export function getStatusColor(status: string): string {
  switch (status) {
    case "PENDING":
      return "badge-warning"
    case "APPROVED":
      return "badge-success"
    case "REJECTED":
      return "badge-error"
    case "PAID":
      return "badge-primary"
    default:
      return "badge-ghost"
  }
}

// Get status label
export function getStatusLabel(status: string): string {
  switch (status) {
    case "PENDING":
      return "Menunggu"
    case "APPROVED":
      return "Disetujui"
    case "REJECTED":
      return "Ditolak"
    case "PAID":
      return "Lunas"
    default:
      return status
  }
}

// Get type label
export function getTypeLabel(type: KasbonType): string {
  switch (type) {
    case "KASBON":
      return "Kasbon"
    case "PIUTANG":
      return "Piutang"
    default:
      return type
  }
}

// Generate Excel kasbon
export async function generateExcelKasbon(kasbons: Kasbon[], startDate: Date, endDate: Date): Promise<Blob> {
  try {
    // Ambil data outlet
    const outlets = await getOutlets()
    
    // Filter kasbon berdasarkan tanggal
    const filteredKasbons = kasbons.filter(kasbon => {
      const kasbonDate = new Date(kasbon.createdAt)
      return kasbonDate >= startDate && kasbonDate <= endDate
    })
    
    // Kelompokkan kasbon berdasarkan outlet
    const kasbonsByOutlet: Record<string, Kasbon[]> = {}
    filteredKasbons.forEach(kasbon => {
      const outletName = outlets.find(o => o.id === kasbon.employee?.outletId)?.name || 'Tidak Ada Outlet'
      if (!kasbonsByOutlet[outletName]) {
        kasbonsByOutlet[outletName] = []
      }
      kasbonsByOutlet[outletName].push(kasbon)
    })

    // Style untuk judul
    const titleStyle = {
      font: { 
        bold: true, 
        size: 14,
        color: { rgb: "FFFFFF" }
      },
      fill: { fgColor: { rgb: "4F46E5" } },
      alignment: { horizontal: "center", vertical: "center" }
    }

    // Style untuk header
    const headerStyle = {
      font: { bold: true },
      fill: { fgColor: { rgb: "E5E7EB" } },
      alignment: { horizontal: "center" },
      border: {
        top: { style: "thin" },
        bottom: { style: "thin" },
        left: { style: "thin" },
        right: { style: "thin" }
      }
    }

    // Style untuk total
    const totalStyle = {
      font: { bold: true },
      fill: { fgColor: { rgb: "E5E7EB" } },
      alignment: { horizontal: "right" },
      border: {
        top: { style: "thin" },
        bottom: { style: "thin" },
        left: { style: "thin" },
        right: { style: "thin" }
      }
    }

    // Format Rupiah
    const rupiahFormat = '_("Rp"* #,##0_);_("Rp"* (#,##0);_("Rp"* "-"_);_(@_)'

    const workbook = XLSX.utils.book_new()

    // Buat worksheet untuk setiap outlet
    Object.entries(kasbonsByOutlet).forEach(([outletName, outletKasbons]) => {
      const headers = [
        ['LAPORAN KASBON & PIUTANG'],
        ['Outlet', outletName],
        ['Periode', `${startDate.toLocaleDateString('id-ID', { month: 'long', year: 'numeric' })}`],
        [''],
        ['No', 'Tanggal', 'Nama Karyawan', 'Jabatan', 'Jenis', 'Nominal', 'Status', 'Keterangan']
      ]

      const data = outletKasbons.map((kasbon, index) => [
        index + 1,
        new Date(kasbon.createdAt).toLocaleDateString('id-ID'),
        kasbon.user?.name || 'Tidak Ada Nama',
        kasbon.user?.employee?.position?.name || 'Tidak Ada Jabatan',
        getTypeLabel(kasbon.type),
        kasbon.amount,
        getStatusLabel(kasbon.status),
        kasbon.description || ''
      ])

      // Hitung total
      const totals = data.reduce((acc, row) => {
        acc.total += row[5] as number
        return acc
      }, { total: 0 })

      // Tambahkan baris total
      const totalRow = [
        '',
        'TOTAL',
        '',
        '',
        '',
        totals.total,
        '',
        ''
      ]

      const ws = XLSX.utils.aoa_to_sheet([...headers, ...data, totalRow])

      // Set lebar kolom
      const colWidths = [
        { wch: 5 },  // No
        { wch: 15 }, // Tanggal
        { wch: 25 }, // Nama
        { wch: 20 }, // Jabatan
        { wch: 15 }, // Jenis
        { wch: 15 }, // Nominal
        { wch: 15 }, // Status
        { wch: 30 }  // Keterangan
      ]
      ws['!cols'] = colWidths

      // Merge cells untuk judul
      ws['!merges'] = [
        { s: { r: 0, c: 0 }, e: { r: 0, c: 7 } }, // Title
        { s: { r: 1, c: 1 }, e: { r: 1, c: 7 } }, // Outlet
        { s: { r: 2, c: 1 }, e: { r: 2, c: 7 } }  // Period
      ]

      // Apply styles
      for (let row = 1; row <= data.length + 5; row++) {
        for (let col = 0; col < 8; col++) {
          const cellRef = XLSX.utils.encode_cell({ r: row - 1, c: col })
          if (!ws[cellRef]) continue

          if (row === 1) {
            ws[cellRef].s = titleStyle
          } else if (row === 5) {
            ws[cellRef].s = headerStyle
          } else if (row === data.length + 5) {
            ws[cellRef].s = totalStyle
          } else if (row > 5) {
            // Format Rupiah untuk kolom nominal
            if (col === 5) {
              ws[cellRef].z = rupiahFormat
            }
          }
        }
      }

      XLSX.utils.book_append_sheet(workbook, ws, outletName)
    })

    // Buat summary sheet
    const summaryHeaders = [
      ['RINGKASAN KASBON & PIUTANG'],
      [''],
      ['Outlet', 'Total Kasbon', 'Total Piutang', 'Total Pending', 'Total Approved']
    ]

    const summaryData = Object.entries(kasbonsByOutlet).map(([outletName, outletKasbons]) => {
      const totalKasbon = outletKasbons
        .filter(k => k.type === 'KASBON')
        .reduce((sum, k) => sum + k.amount, 0)
      
      const totalPiutang = outletKasbons
        .filter(k => k.type === 'PIUTANG')
        .reduce((sum, k) => sum + k.amount, 0)
      
      const totalPending = outletKasbons
        .filter(k => k.status === 'PENDING')
        .reduce((sum, k) => sum + k.amount, 0)
      
      const totalApproved = outletKasbons
        .filter(k => k.status === 'APPROVED')
        .reduce((sum, k) => sum + k.amount, 0)

      return [outletName, totalKasbon, totalPiutang, totalPending, totalApproved]
    })

    // Hitung grand total
    const grandTotal = summaryData.reduce((acc, row) => {
      for (let i = 1; i < row.length; i++) {
        acc[i] = ((acc[i] || 0) as number) + (row[i] as number)
      }
      return acc
    }, [] as number[])

    const summaryWs = XLSX.utils.aoa_to_sheet([
      ...summaryHeaders,
      ...summaryData,
      ['GRAND TOTAL', ...grandTotal.slice(1)]
    ])

    // Set lebar kolom summary
    summaryWs['!cols'] = [
      { wch: 20 }, // Outlet
      { wch: 15 }, // Total Kasbon
      { wch: 15 }, // Total Piutang
      { wch: 15 }, // Total Pending
      { wch: 15 }  // Total Approved
    ]

    // Merge cells untuk judul summary
    summaryWs['!merges'] = [
      { s: { r: 0, c: 0 }, e: { r: 0, c: 4 } }
    ]

    // Apply styles untuk summary
    for (let row = 1; row <= summaryData.length + 3; row++) {
      for (let col = 0; col < 5; col++) {
        const cellRef = XLSX.utils.encode_cell({ r: row - 1, c: col })
        if (!summaryWs[cellRef]) continue

        if (row === 1) {
          summaryWs[cellRef].s = titleStyle
        } else if (row === 3) {
          summaryWs[cellRef].s = headerStyle
        } else if (row === summaryData.length + 3) {
          summaryWs[cellRef].s = {
            font: { 
              bold: true,
              color: { rgb: "FFFFFF" }
            },
            fill: { fgColor: { rgb: "4F46E5" } },
            alignment: { horizontal: col === 0 ? "left" : "right" },
            border: {
              top: { style: "thin" },
              bottom: { style: "thin" },
              left: { style: "thin" },
              right: { style: "thin" }
            }
          }
        } else if (row > 3 && col > 0) {
          summaryWs[cellRef].z = rupiahFormat
        }
      }
    }

    XLSX.utils.book_append_sheet(workbook, summaryWs, 'Ringkasan')

    // Convert workbook to blob
    const wbout = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array'
    })

    return new Blob([wbout], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
  } catch (error) {
    console.error('Error generating Excel:', error)
    throw error
  }
} 