{"name": "1sistemgaji", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "prisma generate && next build", "start": "next start", "lint": "next lint", "prisma:generate": "prisma generate", "prisma:migrate": "prisma migrate dev"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@prisma/client": "^6.3.1", "@react-pdf/renderer": "^4.1.6", "@types/bcryptjs": "^2.4.6", "@types/js-cookie": "^3.0.6", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "child_process": "^1.0.2", "date-fns": "^4.1.0", "framer-motion": "^11.16.0", "fs": "^0.0.1-security", "jose": "^5.9.6", "js-cookie": "^3.0.5", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.2", "lucide-react": "^0.469.0", "next": "^15.1.7", "path": "^0.12.7", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-hot-toast": "^2.5.1", "recharts": "^2.15.0", "sql-formatter": "^15.4.9", "xlsx": "^0.18.5", "zod": "^3.24.1"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/jsonwebtoken": "^9.0.5", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "daisyui": "^4.12.23", "eslint": "^8", "eslint-config-next": "14.2.3", "postcss": "^8", "prisma": "^6.3.1", "tailwindcss": "^3.4.1", "ts-node": "^10.9.2", "typescript": "^5"}}