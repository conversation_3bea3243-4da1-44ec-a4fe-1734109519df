const { PrismaClient } = require('../prisma/generated/client')
const bcrypt = require('bcryptjs')

const prisma = new PrismaClient()

// Helper function untuk menghitung gaji berdasarkan posisi
function getGajiByPosition(position) {
  switch (position) {
    case 'Staff Marketing':
      return {
        pokok: 2800000,
        tunjangan: {
          transport: 500000,
          pulsa: 100000,
          makan: 800000,
          bpjs: 200000,
          penyesuaian: 0
        }
      }
    case 'Terapis':
      return {
        pokok: 2500000,
        tunjangan: {
          transport: 400000,
          pulsa: 50000,
          makan: 800000,
          bpjs: 200000,
          penyesuaian: 0
        }
      }
    case 'Terapis Reguler Baru':
      return {
        pokok: 750000,
        tunjangan: {
          transport: 140000,
          pulsa: 25000,
          makan: 140000,
          bpjs: 0,
          penyesuaian: 0
        }
      }
    case 'Terapis Reguler Lama':
      return {
        pokok: 850000,
        tunjangan: {
          transport: 280000,
          pulsa: 50000,
          makan: 280000,
          bpjs: 0,
          penyesuaian: 0
        }
      }
    default:
      return {
        pokok: 0,
        tunjangan: {
          transport: 0,
          pulsa: 0,
          makan: 0,
          bpjs: 0,
          penyesuaian: 0
        }
      }
  }
}

async function main() {
  try {
    // Hapus data yang ada
    console.log('Menghapus data yang ada...')
    await prisma.slipGaji.deleteMany()
    await prisma.salary.deleteMany()
    await prisma.employee.deleteMany()
    await prisma.position.deleteMany()
    await prisma.user.deleteMany()

    console.log('Membuat data posisi...')
    const staffMarketing = await prisma.position.create({
      data: {
        name: 'Staff Marketing',
        omsetPercentage: 0.5,
        gajiPokok: 2800000,
        tunjangan: 1600000,
        isActive: true,
        isKontrak: false,
        targetKontrak: 0
      }
    })

    const terapis = await prisma.position.create({
      data: {
        name: 'Terapis',
        omsetPercentage: 0.5,
        gajiPokok: 2500000,
        tunjangan: 1450000,
        isActive: true,
        isKontrak: false,
        targetKontrak: 0
      }
    })

    const terapisRegulerBaru = await prisma.position.create({
      data: {
        name: 'Terapis Reguler Baru',
        omsetPercentage: 0.5,
        gajiPokok: 750000,
        tunjangan: 305000,
        isActive: true,
        isKontrak: true,
        targetKontrak: 3000000
      }
    })

    const terapisRegulerLama = await prisma.position.create({
      data: {
        name: 'Terapis Reguler Lama',
        omsetPercentage: 0.5,
        gajiPokok: 850000,
        tunjangan: 610000,
        isActive: true,
        isKontrak: true,
        targetKontrak: 3000000
      }
    })

    console.log('Membuat data user dan karyawan...')
    // Create Admin
    const adminPassword = await bcrypt.hash('admin123', 10)
    const admin = await prisma.user.create({
      data: {
        username: 'admin',
        password: adminPassword,
        name: 'Administrator',
        department: 'Management',
        position: 'Admin',
        role: 'ADMIN',
        joinDate: new Date('2024-01-01')
      }
    })

    // Create Staff Marketing
    const marketingPassword = await bcrypt.hash('marketing123', 10)
    const marketing = await prisma.user.create({
      data: {
        username: 'marketing',
        password: marketingPassword,
        name: 'Staff Marketing',
        department: 'Marketing',
        position: 'Staff Marketing',
        role: 'EMPLOYEE',
        joinDate: new Date('2024-01-01'),
        employee: {
          create: {
            positionId: staffMarketing.id,
            status: 'ACTIVE',
            nik: '1234567890',
            alamat: 'Jl. Marketing No. 1',
            noTelp: '081234567890'
          }
        }
      }
    })

    // Create Terapis
    const terapisPassword = await bcrypt.hash('terapis123', 10)
    const terapisUser = await prisma.user.create({
      data: {
        username: 'terapis',
        password: terapisPassword,
        name: 'Terapis',
        department: 'Terapis',
        position: 'Terapis',
        role: 'EMPLOYEE',
        joinDate: new Date('2024-01-01'),
        employee: {
          create: {
            positionId: terapis.id,
            status: 'ACTIVE',
            nik: '0987654321',
            alamat: 'Jl. Terapis No. 1',
            noTelp: '089876543210'
          }
        }
      }
    })

    // Create Terapis Reguler Baru
    const terapisBaruPassword = await bcrypt.hash('terapisbaru123', 10)
    const terapisBaruUser = await prisma.user.create({
      data: {
        username: 'terapisbaru',
        password: terapisBaruPassword,
        name: 'Terapis Reguler Baru',
        department: 'Terapis',
        position: 'Terapis Reguler Baru',
        role: 'EMPLOYEE',
        joinDate: new Date('2024-01-01'),
        employee: {
          create: {
            positionId: terapisRegulerBaru.id,
            status: 'ACTIVE',
            nik: '1111111111',
            alamat: 'Jl. Terapis Baru No. 1',
            noTelp: '081111111111'
          }
        }
      }
    })

    // Create Terapis Reguler Lama
    const terapisLamaPassword = await bcrypt.hash('terapislama123', 10)
    const terapisLamaUser = await prisma.user.create({
      data: {
        username: 'terapislama',
        password: terapisLamaPassword,
        name: 'Terapis Reguler Lama',
        department: 'Terapis',
        position: 'Terapis Reguler Lama',
        role: 'EMPLOYEE',
        joinDate: new Date('2024-01-01'),
        employee: {
          create: {
            positionId: terapisRegulerLama.id,
            status: 'ACTIVE',
            nik: '2222222222',
            alamat: 'Jl. Terapis Lama No. 1',
            noTelp: '082222222222'
          }
        }
      }
    })

    console.log('Membuat data slip gaji...')
    // Create Slip Gaji untuk setiap karyawan
    const employees = await prisma.employee.findMany({
      include: {
        position: true
      }
    })

    for (const employee of employees) {
      const gaji = getGajiByPosition(employee.position.name)
      
      // Create slip gaji
      await prisma.slipGaji.create({
        data: {
          employeeId: employee.id,
          periode: {
            bulan: 1,
            tahun: 2024
          },
          gaji: {
            pokok: gaji.pokok,
            tunjangan: gaji.tunjangan,
            lembur: 0,
            bonus: 0,
            komisi: 0,
            omzet: 0,
            rasioOmzet: employee.position.omsetPercentage,
            hariMasuk: 22,
            potongan: {
              kasbon: 0,
              piutang: 0,
              bpjsTP: 0,
              bpjsTK: 0,
              lainnya: 0,
              keterangan: ""
            },
            total: gaji.pokok + Object.values(gaji.tunjangan).reduce((a, b) => a + b, 0)
          },
          status: 'DRAFT'
        }
      })
    }

    console.log('Seeding selesai!')
  } catch (error) {
    console.error('Error during seeding:', error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

main()
