import { NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"
import { Slip<PERSON>aji, SlipGajiResponse } from "@/types/slip-gaji"
import { SlipStatus } from '@/types/slip-gaji'
import prisma from "@/lib/prisma"
import { authMiddleware } from "@/lib/auth"

const prismaClient = new PrismaClient()

// GET /api/slip-gaji
export async function GET(request: Request) {
  try {
    const auth = await authMiddleware(request)
    if (!auth) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    // Ambil query parameters
    const url = new URL(request.url)
    const month = url.searchParams.get('month')
    const year = url.searchParams.get('year')

    // Buat query filter
    const where: any = {}
    
    if (month && year) {
      where.periode = {
        path: ['bulan'],
        equals: parseInt(month)
      }
      where.AND = {
        periode: {
          path: ['tahun'],
          equals: parseInt(year)
        }
      }
    }

    console.log("Query filter:", where)

    const slipGaji = await prisma.slipGaji.findMany({
      where,
      include: {
        employee: {
          include: {
            user: true,
            position: true,
            outlet: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Sort manually by periode
    const sortedSlipGaji = [...slipGaji].sort((a, b) => {
      const periodeA = a.periode as { tahun: number; bulan: number }
      const periodeB = b.periode as { tahun: number; bulan: number }
      
      if (periodeA.tahun !== periodeB.tahun) {
        return periodeB.tahun - periodeA.tahun
      }
      return periodeB.bulan - periodeA.bulan
    })

    console.log("Slip gaji fetched:", sortedSlipGaji)

    return NextResponse.json({
      success: true,
      message: "Data slip gaji berhasil diambil",
      data: sortedSlipGaji
    })
  } catch (error) {
    console.error("Error fetching slip gaji:", error)
    return NextResponse.json(
      { 
        success: false,
        message: "Gagal mengambil data slip gaji",
        error: error instanceof Error ? error.message : "Unknown error"
      },
      { status: 500 }
    )
  }
}

// POST /api/slip-gaji
export async function POST(req: Request) {
  try {
    const body = await req.json()

    const slipGaji = await prismaClient.slipGaji.create({
      data: {
        employeeId: body.employeeId,
        periode: body.periode,
        gaji: body.gaji,
        status: SlipStatus.DRAFT
      }
    })

    return NextResponse.json(slipGaji)
  } catch (error) {
    console.error('Error creating slip gaji:', error)
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    )
  }
} 