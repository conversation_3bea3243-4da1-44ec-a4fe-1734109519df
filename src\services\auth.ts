import { LoginCredentials, AuthResponse } from "@/types/auth"
import { set<PERSON><PERSON>ie, getCookie, deleteCookie } from "@/lib/cookies"
import jwt from "jsonwebtoken"

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key"

export async function login(credentials: LoginCredentials): Promise<AuthResponse> {
  try {
    const response = await fetch("/api/auth/login", {
      method: "POST",
      headers: {
        "Content-Type": "application/json"
      },
      body: JSON.stringify(credentials)
    })

    const data = await response.json()
    
    if (!data.success) {
      throw new Error(data.message || "Login gagal")
    }

    // Cookie sudah di-set oleh API, tidak perlu set lagi di client
    return data
  } catch (error) {
    console.error("Login error:", error)
    throw error
  }
}

export function saveAuthToken(token: string) {
  // Cookie sudah di-set oleh API, fungsi ini hanya untuk kompatibilitas
  return Promise.resolve()
}

export function getAuthToken(): string | null {
  return getCookie("auth_token")
}

export function removeAuthToken() {
  deleteCookie("auth_token")
}

export function isAuthenticated(): boolean {
  const token = getAuthToken()
  if (!token) return false

  try {
    jwt.verify(token, JWT_SECRET)
    return true
  } catch {
    removeAuthToken()
    return false
  }
}

export async function getCurrentUser() {
  const token = getAuthToken()
  if (!token) return null

  try {
    const response = await fetch("/api/auth/me", {
      headers: {
        Authorization: `Bearer ${token}`
      }
    })
    
    const data = await response.json()
    if (!data.success) {
      throw new Error(data.message)
    }

    return data.user
  } catch {
    removeAuthToken()
    return null
  }
}

export async function logout() {
  try {
    const response = await fetch("/api/auth/logout", {
      method: "POST",
      credentials: "include" // Untuk mengirim cookie
    })

    if (!response.ok) {
      throw new Error("Logout failed")
    }

    removeAuthToken()
    return true
  } catch (error) {
    console.error("Logout error:", error)
    throw error
  }
} 