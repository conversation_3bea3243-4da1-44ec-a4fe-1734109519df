import { NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"
import { SlipGajiResponse } from "@/types/slip-gaji"

const prisma = new PrismaClient()

// GET /api/slip-gaji/:id
export async function GET(request: Request, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const slipGaji = await prisma.slipGaji.findUnique({
      where: { id: params.id },
      include: {
        employee: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                username: true,
                department: true,
                position: true,
                role: true,
                joinDate: true
              }
            },
            position: {
              select: {
                id: true,
                name: true,
                omsetPercentage: true
              }
            }
          }
        }
      }
    })

    if (!slipGaji) {
      const response: SlipGajiResponse = {
        success: false,
        message: "Data slip gaji tidak ditemukan",
        error: "Not found"
      }
      return NextResponse.json(response, { status: 404 })
    }

    const response: SlipGajiResponse = {
      success: true,
      message: "Data slip gaji berhasil diambil",
      data: slipGaji as any
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error("Error fetching slip gaji:", error)
    const response: SlipGajiResponse = {
      success: false,
      message: "Terjadi kesalahan saat mengambil data slip gaji",
      error: error instanceof Error ? error.message : "Unknown error"
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// PUT /api/slip-gaji/:id
export async function PUT(request: Request, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const body = await request.json()
    const slipGaji = await prisma.slipGaji.update({
      where: { id: params.id },
      data: {
        gaji: body.gaji as Record<string, any>,
        status: body.status
      },
      include: {
        employee: {
          include: {
            user: {
              select: {
                id: true,
                name: true,
                username: true,
                department: true,
                position: true,
                role: true,
                joinDate: true
              }
            },
            position: {
              select: {
                id: true,
                name: true,
                omsetPercentage: true
              }
            }
          }
        }
      }
    })

    const response: SlipGajiResponse = {
      success: true,
      message: "Data slip gaji berhasil diupdate",
      data: slipGaji as any
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error("Error updating slip gaji:", error)
    const response: SlipGajiResponse = {
      success: false,
      message: "Terjadi kesalahan saat mengupdate data slip gaji",
      error: error instanceof Error ? error.message : "Unknown error"
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// DELETE /api/slip-gaji/:id
export async function DELETE(request: Request, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    await prisma.slipGaji.delete({
      where: { id: params.id }
    })

    const response: SlipGajiResponse = {
      success: true,
      message: "Data slip gaji berhasil dihapus"
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error("Error deleting slip gaji:", error)
    const response: SlipGajiResponse = {
      success: false,
      message: "Terjadi kesalahan saat menghapus data slip gaji",
      error: error instanceof Error ? error.message : "Unknown error"
    }
    return NextResponse.json(response, { status: 500 })
  }
} 