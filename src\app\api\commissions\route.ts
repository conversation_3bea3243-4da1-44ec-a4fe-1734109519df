import { NextResponse } from "next/server"
import { PrismaClient } from '@prisma/client'
import type { DailyCommissionResponse, DailyCommission } from "@/types/commission"

const prismaClient = new PrismaClient()

// GET /api/commissions
export async function GET() {
  try {
    const commissions = await prismaClient.dailyCommission.findMany({
      include: {
        employee: {
          include: {
            user: true,
            position: true
          }
        }
      },
      orderBy: {
        date: "desc"
      }
    })

    const response: DailyCommissionResponse = {
      success: true,
      message: "Data komisi berhasil diambil",
      data: commissions as unknown as DailyCommission[]
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error("Error getting commissions:", error)
    const response: DailyCommissionResponse = {
      success: false,
      message: "Gagal mengambil data komisi",
      error: error instanceof Error ? error.message : "Unknown error"
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// POST /api/commissions
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const { employeeId, date, omzet, commission, notes } = body

    // Validasi data
    if (!employeeId || !date || omzet <= 0 || commission <= 0) {
      const response: DailyCommissionResponse = {
        success: false,
        message: "Data tidak lengkap",
        error: "Mohon lengkapi semua data yang diperlukan"
      }
      return NextResponse.json(response, { status: 400 })
    }

    // Simpan data komisi
    const dailyCommission = await prismaClient.dailyCommission.create({
      data: {
        employeeId,
        date: new Date(date),
        omzet,
        commission,
        notes: notes || null
      },
      include: {
        employee: {
          include: {
            user: true,
            position: true
          }
        }
      }
    })

    const response: DailyCommissionResponse = {
      success: true,
      message: "Data komisi berhasil ditambahkan",
      data: dailyCommission as unknown as DailyCommission
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error("Error creating commission:", error)
    const response: DailyCommissionResponse = {
      success: false,
      message: "Gagal menambahkan data komisi",
      error: error instanceof Error ? error.message : "Unknown error"
    }
    return NextResponse.json(response, { status: 500 })
  }
}

export async function PUT(request: Request) {
  try {
    const body = await request.json()
    const { id, omzet, commission, notes } = body

    // Validasi data
    if (!id || omzet <= 0 || commission <= 0) {
      const response: DailyCommissionResponse = {
        success: false,
        message: "Data tidak lengkap",
        error: "Mohon lengkapi semua data yang diperlukan"
      }
      return NextResponse.json(response, { status: 400 })
    }

    // Update data komisi
    const dailyCommission = await prismaClient.dailyCommission.update({
      where: { id },
      data: {
        omzet,
        commission,
        notes: notes || null
      },
      include: {
        employee: {
          include: {
            user: true,
            position: true
          }
        }
      }
    })

    const response: DailyCommissionResponse = {
      success: true,
      message: "Data komisi berhasil diupdate",
      data: dailyCommission as unknown as DailyCommission
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error("Error updating commission:", error)
    const response: DailyCommissionResponse = {
      success: false,
      message: "Gagal mengupdate data komisi",
      error: error instanceof Error ? error.message : "Unknown error"
    }
    return NextResponse.json(response, { status: 500 })
  }
}

export async function DELETE(request: Request) {
  try {
    const { searchParams } = new URL(request.url)
    const id = searchParams.get("id")

    if (!id) {
      const response: DailyCommissionResponse = {
        success: false,
        message: "ID tidak ditemukan",
        error: "Mohon sertakan ID komisi yang akan dihapus"
      }
      return NextResponse.json(response, { status: 400 })
    }

    await prismaClient.dailyCommission.delete({
      where: { id }
    })

    const response: DailyCommissionResponse = {
      success: true,
      message: "Data komisi berhasil dihapus"
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error("Error deleting commission:", error)
    const response: DailyCommissionResponse = {
      success: false,
      message: "Gagal menghapus data komisi",
      error: error instanceof Error ? error.message : "Unknown error"
    }
    return NextResponse.json(response, { status: 500 })
  }
} 