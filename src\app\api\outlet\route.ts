import { NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"

const prisma = new PrismaClient()

export async function GET() {
  try {
    const outlets = await prisma.outlet.findMany()
    return NextResponse.json({
      success: true,
      message: "Berhasil mengambil data outlet",
      data: outlets
    })
  } catch (error) {
    console.error("Error fetching outlets:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Gagal mengambil data outlet",
        error: error instanceof Error ? error.message : "Unknown error occurred"
      },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    const body = await request.json()
    const outlet = await prisma.outlet.create({
      data: {
        name: body.name,
        code: body.code
      }
    })
    return NextResponse.json({
      success: true,
      message: "Berhasil menambahkan outlet",
      data: outlet
    })
  } catch (error) {
    console.error("Error creating outlet:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Gagal menambahkan outlet",
        error: error instanceof Error ? error.message : "Unknown error occurred"
      },
      { status: 500 }
    )
  }
} 