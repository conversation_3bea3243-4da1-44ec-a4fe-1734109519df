import { NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"
import jwt from "jsonwebtoken"

const prisma = new PrismaClient()
const JWT_SECRET = process.env.JWT_SECRET!

export async function GET(request: Request) {
  try {
    const authorization = request.headers.get("Authorization")
    if (!authorization || !authorization.startsWith("Bearer ")) {
      return NextResponse.json(
        { success: false, message: "Token tidak valid" },
        { status: 401 }
      )
    }

    const token = authorization.split(" ")[1]
    const decoded = jwt.verify(token, JWT_SECRET) as { userId: string }

    const user = await prisma.user.findUnique({
      where: { id: decoded.userId },
      include: {
        employee: {
          include: {
            position: true,
            salaries: {
              orderBy: {
                createdAt: 'desc'
              },
              take: 1
            }
          }
        }
      }
    })

    if (!user) {
      return NextResponse.json(
        { success: false, message: "User tidak ditemukan" },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      user: {
        id: user.id,
        username: user.username,
        name: user.name,
        role: user.role,
        department: user.department,
        position: user.position,
        joinDate: user.joinDate.toISOString(),
        employee: user.employee ? {
          id: user.employee.id,
          position: user.employee.position,
          latestSalary: user.employee.salaries[0] || null
        } : null
      }
    })
  } catch (error) {
    console.error("Get current user error:", error)
    return NextResponse.json(
      { success: false, message: "Terjadi kesalahan saat mengambil data user" },
      { status: 500 }
    )
  }
} 