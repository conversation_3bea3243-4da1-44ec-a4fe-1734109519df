"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { 
  LayoutDashboard, 
  Users, 
  Calculator,
  Target,
  Wallet,
  Receipt,
  MessageSquare,
  LogOut,
  ChevronLeft,
  Bell,
  Sun,
  Moon,
  Settings,
  User as UserIcon,
  Menu,
  DollarSign
} from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { logout } from "@/services/auth"
import Image from "next/image"
import { toast } from "react-hot-toast"
import { useRouter } from "next/navigation"

const menuItems = [
  {
    title: "Dashboard",
    icon: LayoutDashboard,
    href: "/dashboard"
  },
  {
    title: "<PERSON><PERSON><PERSON>",
    icon: Users,
    href: "/dashboard/karyawan"
  },
  {
    title: "Jabatan",
    icon: Target,
    href: "/dashboard/jabatan"
  },
  {
    title: "Penggajian",
    icon: Calculator,
    href: "/dashboard/penggajian"
  },
  {
    title: "Ko<PERSON><PERSON>",
    icon: DollarSign,
    href: "/dashboard/komisi"
  },
  {
    title: "Target & Omset",
    icon: Target,
    href: "/dashboard/target-omset"
  },
  {
    title: "Kasbon & Piutang",
    icon: Wallet,
    href: "/dashboard/kasbon"
  },
  {
    title: "Slip Gaji",
    icon: Receipt,
    href: "/dashboard/slip-gaji"
  },
  {
    title: "Pengaturan",
    icon: Settings,
    href: "/dashboard/pengaturan"
  }
]

const notifications = [
  {
    id: 1,
    title: "Target Tercapai",
    message: "Ahmad Fauzi mencapai target bulan ini",
    time: "5 menit yang lalu",
    isRead: false,
  },
  {
    id: 2,
    title: "Pengajuan Kasbon",
    message: "Siti Aminah mengajukan kasbon",
    time: "10 menit yang lalu",
    isRead: false,
  }
]

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false)
  const [isMobileView, setIsMobileView] = useState(false)
  const [theme, setTheme] = useState<"light" | "dark">("light")
  const [isNotifOpen, setIsNotifOpen] = useState(false)
  const [isProfileOpen, setIsProfileOpen] = useState(false)
  const pathname = usePathname()
  const router = useRouter()

  // Deteksi mobile view
  useEffect(() => {
    const handleResize = () => {
      setIsMobileView(window.innerWidth < 768)
      if (window.innerWidth >= 768) {
        setIsSidebarOpen(true)
      } else {
        setIsSidebarOpen(false)
      }
    }

    handleResize()
    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  useEffect(() => {
    const savedTheme = localStorage.getItem("theme") as "light" | "dark" || "light"
    setTheme(savedTheme)
    document.documentElement.setAttribute("data-theme", savedTheme)
  }, [])

  const toggleTheme = () => {
    const newTheme = theme === "light" ? "dark" : "light"
    setTheme(newTheme)
    localStorage.setItem("theme", newTheme)
    document.documentElement.setAttribute("data-theme", newTheme)
  }

  const toggleSidebar = () => setIsSidebarOpen(!isSidebarOpen)

  const handleLogout = async () => {
    try {
      await logout()
      router.push('/auth/login')
    } catch (error) {
      console.error('Error logging out:', error)
      toast.error('Terjadi kesalahan saat logout')
    }
  }

  return (
    <div className="min-h-screen bg-base-200">
      {/* Overlay untuk mobile */}
      {isMobileView && isSidebarOpen && (
        <div 
          className="fixed inset-0 bg-black/50 z-30"
          onClick={() => setIsSidebarOpen(false)}
        />
      )}

      {/* Sidebar */}
      <motion.aside
        initial={false}
        animate={{
          x: isSidebarOpen ? 0 : -320,
          transition: { duration: 0.3 }
        }}
        className={`fixed left-0 top-0 z-40 h-screen bg-base-100 shadow-xl w-64 md:w-64`}
      >
        <div className="flex h-full flex-col justify-between">
          {/* Logo & Toggle */}
          <div>
            <div className="flex h-16 md:h-20 items-center justify-between px-4 border-b border-base-200">
              {isSidebarOpen ? (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="flex items-center gap-2"
                >
                  <div className="w-6 h-6 md:w-8 md:h-8 relative">
                    <Image 
                      src="/logo.png" 
                      alt="Breaktime Logo" 
                      fill
                      sizes="(max-width: 768px) 24px, (max-width: 1200px) 32px, 32px"
                      className="object-contain"
                    />
                  </div>
                  <div className="flex flex-col">
                    <span className="text-base md:text-lg font-bold bg-gradient-to-r from-breaktime-primary to-breaktime-secondary bg-clip-text text-transparent">
                      Breaktime
                    </span>
                    <span className="text-[8px] md:text-[10px] text-base-content/60">
                      Badan Segar Urusan Lancar
                    </span>
                  </div>
                </motion.div>
              ) : (
                <div className="w-6 h-6 md:w-8 md:h-8 relative mx-auto">
                  <Image 
                    src="/logo.png" 
                    alt="Breaktime Logo" 
                    fill
                    sizes="(max-width: 768px) 24px, (max-width: 1200px) 32px, 32px"
                    className="object-contain"
                  />
                </div>
              )}
              <button
                onClick={toggleSidebar}
                className="btn btn-circle btn-sm md:btn-md bg-base-200 hover:bg-base-300"
              >
                <motion.div
                  animate={{ rotate: isSidebarOpen ? 0 : 180 }}
                  transition={{ duration: 0.3 }}
                >
                  <ChevronLeft className="h-4 w-4 md:h-5 md:w-5 text-base-content" />
                </motion.div>
              </button>
            </div>

            {/* Menu Items */}
            <div className="px-2 md:px-3 py-3 md:py-4">
              {menuItems.map((item) => (
                <Link
                  key={item.href}
                  href={item.href}
                  className={`mt-1 md:mt-2 flex items-center gap-2 md:gap-3 rounded-xl px-2 md:px-3 py-2 md:py-2.5 transition-all hover:bg-base-200 ${
                    pathname === item.href 
                      ? "bg-gradient-to-r from-breaktime-primary to-breaktime-secondary text-white hover:from-breaktime-primary/90 hover:to-breaktime-secondary/90" 
                      : "text-base-content hover:text-primary"
                  }`}
                >
                  <item.icon className="h-4 w-4 md:h-5 md:w-5" />
                  {isSidebarOpen && (
                    <motion.span
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      className="text-sm md:text-base font-medium whitespace-nowrap"
                    >
                      {item.title}
                    </motion.span>
                  )}
                </Link>
              ))}
            </div>
          </div>

          {/* Logout Button */}
          <div className="p-2 md:p-3 border-t border-base-200">
            <button 
              onClick={handleLogout}
              className="flex w-full items-center gap-2 md:gap-3 rounded-xl px-2 md:px-3 py-2 md:py-2.5 text-error hover:bg-error/10 transition-colors"
            >
              <LogOut className="h-4 w-4 md:h-5 md:w-5" />
              {isSidebarOpen && (
                <motion.span
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                  className="text-sm md:text-base font-medium whitespace-nowrap"
                >
                  Logout
                </motion.span>
              )}
            </button>
          </div>
        </div>
      </motion.aside>

      {/* Main Content */}
      <div className={`transition-all duration-300 ${isSidebarOpen ? 'md:ml-64' : ''}`}>
        {/* Navbar */}
        <nav className="sticky top-0 z-30 flex h-14 md:h-16 items-center justify-between bg-base-100/80 backdrop-blur-md px-3 md:px-4 shadow-sm">
          <div className="flex items-center gap-3 md:gap-4">
            {/* Toggle untuk mobile */}
            <button
              onClick={toggleSidebar}
              className="btn btn-circle btn-ghost btn-sm md:hidden"
            >
              <Menu className="h-5 w-5 text-base-content" />
            </button>

            <h1 className="text-lg md:text-2xl font-bold bg-gradient-to-r from-breaktime-primary to-breaktime-secondary bg-clip-text text-transparent truncate">
              {menuItems.find(item => item.href === pathname)?.title || "Dashboard"}
            </h1>
          </div>
          <div className="flex items-center gap-2 md:gap-4">
            {/* Theme Toggle */}
            <button 
              onClick={toggleTheme}
              className="btn btn-circle btn-ghost btn-sm"
            >
              {theme === "light" ? (
                <Sun className="h-4 w-4 md:h-5 md:w-5 text-base-content/80" />
              ) : (
                <Moon className="h-4 w-4 md:h-5 md:w-5 text-base-content/80" />
              )}
            </button>

            {/* Notifications */}
            <div className="dropdown dropdown-end">
              <button 
                className="btn btn-circle btn-ghost btn-sm"
                onClick={() => setIsNotifOpen(!isNotifOpen)}
              >
                <div className="indicator">
                  <Bell className="h-4 w-4 md:h-5 md:w-5 text-base-content/80" />
                  <span className="badge badge-sm badge-primary badge-xs indicator-item">
                    {notifications.filter(n => !n.isRead).length}
                  </span>
                </div>
              </button>
              {isNotifOpen && (
                <div className="dropdown-content z-[1] menu shadow-lg bg-base-100 rounded-box w-72 md:w-80 mt-4 border border-base-300">
                  <div className="flex items-center justify-between p-3 md:p-4 border-b border-base-200">
                    <div className="flex items-center gap-2">
                      <Bell className="h-4 w-4 text-primary" />
                      <h3 className="font-semibold text-sm md:text-base text-base-content">Notifikasi</h3>
                    </div>
                    <button className="btn btn-ghost btn-xs text-primary hover:bg-primary/10 text-xs md:text-sm">
                      Tandai Sudah Dibaca
                    </button>
                  </div>
                  <div className="overflow-y-auto max-h-[240px] md:max-h-[280px] py-2">
                    {notifications.map((notif) => (
                      <div 
                        key={notif.id}
                        className={`hover:bg-base-200 cursor-pointer transition-colors ${
                          !notif.isRead ? "bg-primary/5 border-l-4 border-l-primary" : ""
                        }`}
                      >
                        <div className="p-3 md:p-4">
                          <div className="flex justify-between items-start gap-3 md:gap-4">
                            <div className="flex-1">
                              <h4 className="font-medium text-sm md:text-base text-base-content">
                                {notif.title}
                              </h4>
                              <p className="text-xs md:text-sm text-base-content/70 mt-1 line-clamp-2">
                                {notif.message}
                              </p>
                            </div>
                            <span className="text-[10px] md:text-xs text-base-content/50 whitespace-nowrap">
                              {notif.time}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                  <div className="p-2 md:p-3 border-t border-base-200 bg-base-200/30">
                    <button className="btn btn-ghost btn-sm w-full text-primary hover:bg-primary/10 font-medium text-xs md:text-sm">
                      Lihat Semua Notifikasi
                    </button>
                  </div>
                </div>
              )}
            </div>

            {/* Profile */}
            <div className="dropdown dropdown-end">
              <div 
                role="button"
                onClick={() => setIsProfileOpen(!isProfileOpen)}
                className="avatar placeholder cursor-pointer"
              >
                <div className="w-8 h-8 md:w-10 md:h-10 rounded-xl bg-gradient-to-r from-breaktime-primary/20 to-breaktime-secondary/20">
                  <span className="text-sm md:text-base font-bold bg-gradient-to-r from-breaktime-primary to-breaktime-secondary bg-clip-text text-transparent">A</span>
                </div>
              </div>
              {isProfileOpen && (
                <div className="dropdown-content z-[1] menu p-2 shadow-lg bg-base-100 rounded-box w-48 md:w-52 mt-4">
                  <div className="p-3 md:p-4 border-b border-base-200">
                    <p className="font-medium text-sm md:text-base">Admin</p>
                    <p className="text-xs md:text-sm text-base-content/60"><EMAIL></p>
                  </div>
                  <ul className="menu menu-sm">
                    <li>
                      <a className="py-2 text-sm md:text-base">
                        <UserIcon className="w-4 h-4" />
                        Profile
                      </a>
                    </li>
                    <li>
                      <a className="py-2 text-sm md:text-base">
                        <Settings className="w-4 h-4" />
                        Settings
                      </a>
                    </li>
                    <li>
                      <a 
                        onClick={handleLogout}
                        className="text-error py-2 text-sm md:text-base"
                      >
                        <LogOut className="w-4 h-4" />
                        Logout
                      </a>
                    </li>
                  </ul>
                </div>
              )}
            </div>
          </div>
        </nav>

        {/* Page Content */}
        <main 
          className="p-4 md:p-6" 
          onClick={() => {
            if (isMobileView) setIsSidebarOpen(false)
            setIsNotifOpen(false)
            setIsProfileOpen(false)
          }}
        >
          {children}
        </main>
      </div>
    </div>
  )
} 