import { PrismaClient, Role, EmployeeStatus, SlipStatus } from './generated/client'
import { hash } from 'bcryptjs'

const prisma = new PrismaClient()

// Helper function untuk menghitung gaji berdasarkan posisi
function getGajiByPosition(position: string) {
  switch (position) {
    case 'Staff Marketing':
      return {
        pokok: 2800000,
        tunjangan: {
          transport: 500000,
          pulsa: 100000,
          makan: 800000,
          bpjs: 200000,
          penyesuaian: 0
        }
      }
    case 'Terapis':
      return {
        pokok: 2500000,
        tunjangan: {
          transport: 400000,
          pulsa: 50000,
          makan: 800000,
          bpjs: 200000,
          penyesuaian: 0
        }
      }
    case 'Terapis Reguler Baru':
      return {
        pokok: 750000,
        tunjangan: {
          transport: 140000,
          pulsa: 25000,
          makan: 140000,
          bpjs: 0,
          penyesuaian: 0
        }
      }
    case 'Terapis Reguler Lama':
      return {
        pokok: 850000,
        tunjangan: {
          transport: 280000,
          pulsa: 50000,
          makan: 280000,
          bpjs: 0,
          penyesuaian: 0
        }
      }
    case 'Kapten':
      return {
        pokok: 3500000,
        tunjangan: {
          transport: 700000,
          pulsa: 150000,
          makan: 800000,
          bpjs: 200000,
          penyesuaian: 0
        }
      }
    default:
      return {
        pokok: 0,
        tunjangan: {
          transport: 0,
          pulsa: 0,
          makan: 0,
          bpjs: 0,
          penyesuaian: 0
        }
      }
  }
}

async function main() {
  // Buat outlet terlebih dahulu
  const outlet = await prisma.outlet.create({
    data: {
      name: 'Outlet Utama',
      code: `OUT-${Date.now()}`
    }
  })

  // Buat posisi staff marketing
  const staffMarketing = await prisma.position.create({
    data: {
      name: 'Staff Marketing',
      omsetPercentage: 2.5,
      isActive: true,
      gajiPokok: 2800000,
      tunjangan: {
        transport: 500000,
        pulsa: 100000,
        makan: 800000
      }
    }
  })

  // Buat posisi terapis
  const staffTerapis = await prisma.position.create({
    data: {
      name: 'Terapis',
      omsetPercentage: 2.0,
      isActive: true,
      gajiPokok: 2500000,
      tunjangan: {
        transport: 400000,
        pulsa: 50000,
        makan: 800000
      }
    }
  })

  // Buat posisi terapis reguler baru
  const staffTerapisRegulerBaru = await prisma.position.create({
    data: {
      name: 'Terapis Reguler Baru',
      omsetPercentage: 25.0,
      isActive: true,
      gajiPokok: 750000,
      tunjangan: {
        transport: 140000,
        pulsa: 25000,
        makan: 140000
      },
      isKontrak: true,
      targetKontrak: 10185000
    }
  })

  // Buat posisi terapis reguler lama
  const staffTerapisRegulerLama = await prisma.position.create({
    data: {
      name: 'Terapis Reguler Lama',
      omsetPercentage: 29.0,
      isActive: true,
      gajiPokok: 850000,
      tunjangan: {
        transport: 280000,
        pulsa: 50000,
        makan: 280000
      },
      isKontrak: true,
      targetKontrak: 10185000
    }
  })

  // Buat posisi kapten
  const staffKapten = await prisma.position.create({
    data: {
      name: 'Kapten',
      omsetPercentage: 3.0,
      isActive: true,
      gajiPokok: 3500000,
      tunjangan: {
        transport: 700000,
        pulsa: 150000,
        makan: 800000
      }
    }
  })

  // Buat admin user
  const adminUser = await prisma.user.create({
    data: {
      username: `admin_${Date.now()}`,
      password: await hash('admin123', 12),
      name: 'Admin',
      role: Role.ADMIN,
      department: 'Management',
      position: 'Admin',
      joinDate: new Date()
    }
  })

  // Data karyawan marketing
  const marketingStaff = [
    { name: 'Riski alfian', phone: '082293525786' },
    { name: 'Aulia', phone: '082262699605' },
    { name: 'Iki DGM', phone: '085237158220' },
    { name: 'Vivi', phone: '085298073241' },
    { name: 'Rana', phone: '087735417989' },
    { name: 'Andini', phone: '082349858102' },
    { name: 'Ndari', phone: '085656855513' },
    { name: 'Ana', phone: '082259672611' },
    { name: 'Ridha', phone: '0895352302097' },
    { name: 'Salwa', phone: '088744558660' },
    { name: 'Nurul', phone: '0887435667032' }
  ]

  // Data karyawan terapis
  const terapisStaff = [
    { name: 'Eka', phone: '085242009164' },
    { name: 'Dadang', phone: '085216524207' },
    { name: 'Ranidiby', phone: '083824079526' },
    { name: 'Emy', phone: '085394487621' },
    { name: 'Diva', phone: '085796143007' },
    { name: 'Vinda', phone: '081543477062' },
    { name: 'Anas', phone: '085336867834' },
    { name: 'Andini', phone: '082349858102' },
    { name: 'Syaril', phone: '089520450053' },
    { name: 'Vito', phone: '085656729654' },
    { name: 'Raja', phone: '081524911217' },
    { name: 'Joko', phone: '085824229174' },
    { name: 'Sarfan', phone: '081311437736' },
    { name: 'Alam', phone: '0815253428500' },
    { name: 'Asma', phone: '082296615600' },
    { name: 'Wahyuni', phone: '085143710031' },
    { name: 'Aisya', phone: '089514522337' },
    { name: 'Rida', phone: '089535230209' },
    { name: 'Fadel', phone: '082192035427' },
    { name: 'Nurul', phone: '088743566703' },
    { name: 'Lutfi', phone: '082291548447' },
    { name: 'Putra', phone: '082189234157' },
    { name: 'Marwan', phone: '089532024041' },
    { name: 'Andi', phone: '085242500989' },
    { name: 'Angga', phone: '082146623695' },
    { name: 'Mariani', phone: '085298015372' },
    { name: 'Salmiah', phone: '082188827578' },
    { name: 'Fahriani', phone: '088744972026' },
    { name: 'Anita', phone: '082394911187' },
    { name: 'Febriani', phone: '085231606432' },
    { name: 'Andris', phone: '085142958911' },
    { name: 'Bayu', phone: '083113767189' },
    { name: 'Firsan', phone: '085398645850' },
    { name: 'Adi', phone: '082195024249' },
    { name: 'Maharani', phone: '083848642948' },
    { name: 'Isra', phone: '085280203962' },
    { name: 'Retno', phone: '081915885706' },
    { name: 'Aprilia', phone: '082296269169' },
    { name: 'Taufan', phone: '085342473265' },
    { name: 'Apriana', phone: '082259672611' },
    { name: 'Deby', phone: '089539980330' },
    { name: 'Delisa', phone: '085244950604' },
    { name: 'Richa', phone: '089538364466' },
    { name: 'Windah', phone: '085254797283' },
    { name: 'Salwa', phone: '088744558660' },
    { name: 'Nazir', phone: '082191170694' },
    { name: 'Syarif', phone: '085756993787' },
    { name: 'Saiful', phone: '085757194201' },
    { name: 'Salam', phone: '085823053056' },
    { name: 'Bertho', phone: '089580372268' },
    { name: 'Ndari', phone: '085656855513' },
    { name: 'Haikal', phone: '082131872263' },
    { name: 'Yurni', phone: '089533695107' },
    { name: 'Fitry', phone: '082350594815' },
    { name: 'Riono', phone: '082251253454' },
    { name: 'Rifal', phone: '082121757936' },
    { name: 'Iin', phone: '087734923114' },
    { name: 'Dimas', phone: '083843646246' }
  ]

  // Data karyawan kapten
  const kaptenStaff = [
    { name: 'Amin', phone: '085398651927' },
    { name: 'Fiqri', phone: '089529142802' },
    { name: 'Taufan', phone: '085342473265' },
    { name: 'Yusril', phone: '082227249618' },
    { name: 'Amad', phone: '085397611102' },
    { name: 'Matto', phone: '082195102294' }
  ]

  // Buat employee untuk setiap marketing staff
  for (let i = 0; i < marketingStaff.length; i++) {
    const staff = marketingStaff[i]
    const timestamp = Date.now() + i
    const user = await prisma.user.create({
      data: {
        username: `marketing_${staff.name.toLowerCase().replace(/\s+/g, '_')}_${timestamp}`,
        password: await hash('password123', 12),
        name: staff.name,
        role: Role.EMPLOYEE,
        department: 'Marketing',
        position: 'Staff Marketing',
        joinDate: new Date(),
        employee: {
          create: {
            nik: `MKT${Math.random().toString().slice(2, 8)}`,
            alamat: 'Alamat default',
            noTelp: staff.phone,
            positionId: staffMarketing.id,
            status: EmployeeStatus.ACTIVE,
            outletId: outlet.id
          }
        }
      }
    })
  }

  // Buat employee untuk setiap terapis staff
  for (let i = 0; i < terapisStaff.length; i++) {
    const staff = terapisStaff[i]
    const timestamp = Date.now() + i
    const user = await prisma.user.create({
      data: {
        username: `terapis_${staff.name.toLowerCase().replace(/\s+/g, '_')}_${timestamp}`,
        password: await hash('password123', 12),
        name: staff.name,
        role: Role.EMPLOYEE,
        department: 'Terapis',
        position: 'Terapis',
        joinDate: new Date(),
        employee: {
          create: {
            nik: `TRP${Math.random().toString().slice(2, 8)}`,
            alamat: 'Alamat default',
            noTelp: staff.phone,
            positionId: staffTerapis.id,
            status: EmployeeStatus.ACTIVE,
            outletId: outlet.id
          }
        }
      }
    })
  }

  // Buat employee untuk setiap kapten staff
  for (let i = 0; i < kaptenStaff.length; i++) {
    const staff = kaptenStaff[i]
    const timestamp = Date.now() + i
    const user = await prisma.user.create({
      data: {
        username: `kapten_${staff.name.toLowerCase().replace(/\s+/g, '_')}_${timestamp}`,
        password: await hash('password123', 12),
        name: staff.name,
        role: Role.EMPLOYEE,
        department: 'Kapten',
        position: 'Kapten',
        joinDate: new Date(),
        employee: {
          create: {
            nik: `KPT${Math.random().toString().slice(2, 8)}`,
            alamat: 'Alamat default',
            noTelp: staff.phone,
            positionId: staffKapten.id,
            status: EmployeeStatus.ACTIVE,
            outletId: outlet.id
          }
        }
      }
    })
  }

  // Setelah semua karyawan dibuat, buat slip gaji untuk 3 bulan terakhir
  const employees = await prisma.employee.findMany({
    include: {
      position: true
    }
  })

  // Buat data komisi harian untuk 3 bulan terakhir
  for (const employee of employees) {
    // Hanya buat komisi untuk terapis dan marketing
    if (["Staff Marketing", "Terapis", "Terapis Reguler Baru", "Terapis Reguler Lama"].includes(employee.position.name)) {
      // Generate komisi untuk 3 bulan terakhir
      for (let i = 0; i < 3; i++) {
        const currentDate = new Date()
        currentDate.setMonth(currentDate.getMonth() - i)
        const daysInMonth = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0).getDate()
        
        // Generate 15-25 transaksi per bulan
        const transactionsCount = Math.floor(Math.random() * (25 - 15 + 1)) + 15
        
        for (let j = 0; j < transactionsCount; j++) {
          const day = Math.floor(Math.random() * daysInMonth) + 1
          const date = new Date(currentDate.getFullYear(), currentDate.getMonth(), day)
          
          // Generate omzet berdasarkan posisi
          let baseOmzet = 0
          switch (employee.position.name) {
            case "Staff Marketing":
              baseOmzet = Math.floor(Math.random() * (5000000 - 2000000 + 1)) + 2000000
              break
            case "Terapis":
              baseOmzet = Math.floor(Math.random() * (3000000 - 1000000 + 1)) + 1000000
              break
            case "Terapis Reguler Baru":
            case "Terapis Reguler Lama":
              baseOmzet = Math.floor(Math.random() * (2000000 - 500000 + 1)) + 500000
              break
          }
          
          // Hitung komisi berdasarkan persentase posisi
          const commission = (baseOmzet * employee.position.omsetPercentage) / 100

          await prisma.dailyCommission.create({
            data: {
              employeeId: employee.id,
              date,
              omzet: baseOmzet,
              commission,
              notes: `Transaksi tanggal ${date.getDate()}/${date.getMonth() + 1}/${date.getFullYear()}`
            }
          })
        }
      }
    }
  }

  // Buat slip gaji untuk 3 bulan terakhir
  for (const employee of employees) {
    for (let i = 0; i < 3; i++) {
      const date = new Date()
      date.setMonth(date.getMonth() - i)
      
      // Ambil total komisi bulan ini
      const startDate = new Date(date.getFullYear(), date.getMonth(), 1)
      const endDate = new Date(date.getFullYear(), date.getMonth() + 1, 0)
      
      const monthlyCommissions = await prisma.dailyCommission.findMany({
        where: {
          employeeId: employee.id,
          date: {
            gte: startDate,
            lte: endDate
          }
        }
      })

      const totalKomisi = monthlyCommissions.reduce((sum, commission) => sum + commission.commission, 0)
      const totalOmzet = monthlyCommissions.reduce((sum, commission) => sum + commission.omzet, 0)
      
      const gajiTemplate = getGajiByPosition(employee.position.name)
      
      // Generate random bonus dan lembur
      const bonus = Math.floor(Math.random() * 1000000) // 0-1jt
      const lembur = Math.floor(Math.random() * 500000) // 0-500rb

      // Generate random potongan
      const potongan = {
        kasbon: Math.floor(Math.random() * 300000), // 0-300rb
        piutang: 0,
        bpjsTP: 100000,
        bpjsTK: 100000,
        lainnya: 0,
        keterangan: ""
      }

      // Hitung total gaji
      const totalTunjangan = 
        gajiTemplate.tunjangan.transport +
        gajiTemplate.tunjangan.pulsa +
        gajiTemplate.tunjangan.makan +
        gajiTemplate.tunjangan.bpjs +
        gajiTemplate.tunjangan.penyesuaian

      const totalPotongan =
        potongan.kasbon +
        potongan.piutang +
        potongan.bpjsTP +
        potongan.bpjsTK +
        potongan.lainnya

      const total = 
        gajiTemplate.pokok + 
        totalTunjangan + 
        bonus + 
        lembur + 
        totalKomisi - 
        totalPotongan

      await prisma.slipGaji.create({
        data: {
          employeeId: employee.id,
          periode: {
            bulan: date.getMonth() + 1,
            tahun: date.getFullYear()
          },
          gaji: {
            pokok: gajiTemplate.pokok,
            tunjangan: gajiTemplate.tunjangan,
            lembur,
            bonus,
            komisi: totalKomisi,
            omzet: totalOmzet,
            rasioOmzet: employee.position.omsetPercentage,
            hariMasuk: Math.floor(Math.random() * (28 - 20 + 1)) + 20, // 20-28 hari
            potongan,
            total
          },
          status: i === 0 ? SlipStatus.DRAFT : SlipStatus.PAID
        }
      })
    }
  }

  console.log('Seeding completed!')
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  }) 