import { NextResponse } from "next/server"
import { 
  getCompanySettings, 
  updateCompanySettings, 
  getUserSettings,
  updateUserSettings,
  uploadCompanyLogo
} from "@/services/settings"

// GET /api/settings
export async function GET() {
  try {
    const [company, user] = await Promise.all([
      getCompanySettings(),
      getUserSettings()
    ])

    return NextResponse.json({
      success: true,
      data: {
        company,
        user
      }
    })
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "Terjadi kesalahan"
      },
      { status: 500 }
    )
  }
}

// PUT /api/settings
export async function PUT(request: Request) {
  try {
    const body = await request.json()
    const { type, data } = body

    if (!type || !data) {
      throw new Error("Data tidak lengkap")
    }

    let result

    switch (type) {
      case "company":
        result = await updateCompanySettings(data)
        break
      case "user":
        result = await updateUserSettings(data)
        break
      default:
        throw new Error("Tipe pengaturan tidak valid")
    }

    return NextResponse.json({
      success: true,
      data: result
    })
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "Terjadi kesalahan"
      },
      { status: 500 }
    )
  }
}

// POST /api/settings/logo
export async function POST(request: Request) {
  try {
    const formData = await request.formData()
    const file = formData.get("logo") as File

    if (!file) {
      throw new Error("File tidak ditemukan")
    }

    const logoUrl = await uploadCompanyLogo(file)

    return NextResponse.json({
      success: true,
      data: { logoUrl }
    })
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        message: error instanceof Error ? error.message : "Terjadi kesalahan"
      },
      { status: 500 }
    )
  }
} 