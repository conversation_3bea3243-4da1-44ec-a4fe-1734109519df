import { cookies } from "next/headers"
import { NextResponse } from "next/server"

export async function POST() {
  try {
    // Hapus cookie auth_token
    (await cookies()).delete("auth_token")

    return NextResponse.json({ 
      success: true,
      message: "Berhasil logout"
    })
  } catch (error) {
    console.error("Logout error:", error)
    return NextResponse.json({ 
      success: false,
      message: "Gagal logout"
    }, { status: 500 })
  }
} 