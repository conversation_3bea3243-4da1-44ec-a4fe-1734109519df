# 🧪 Manual Testing Guide - Custom Date Picker Auto-Fill Payroll

## 📋 Testing Checklist Lengkap

### 🎯 Pre-Requirements
- ✅ Server development berjalan (`npm run dev`)
- ✅ Database tersedia dan terkoneksi  
- ✅ Akses ke halaman `/dashboard/penggajian`
- ✅ Data karyawan terapis tersedia (contoh: "Isra")
- ✅ API Key BreakTime sudah dikonfigurasi

---

## 🧪 Test Case 1: UI Component Rendering

### 📍 Langkah Testing:
1. 🌐 **Buka** halaman `/dashboard/penggajian`
2. 👤 **Pilih karyawan** yang merupakan terapis 
3. 🔍 **Verifikasi** section Auto-Fill muncul

### ✅ Expected Results:
- [ ] Section "Auto-Fill Data Payroll" tampil
- [ ] Checkbox "Gunakan Tanggal Custom" tampil
- [ ] Info text "🤖 Sistem akan otomatis menghitung..." tampil
- [ ] Tombol "Auto-Fill" tersedia dan enabled

### 📸 Screenshot Areas:
- Auto-Fill section dengan checkbox
- Responsive design mobile/desktop

---

## 🧪 Test Case 2: Checkbox Toggle Animation

### 📍 Langkah Testing:
1. 🔲 **Pastikan** checkbox dalam keadaan unchecked
2. ✅ **Klik** checkbox "Gunakan Tanggal Custom"
3. 👀 **Amati** animasi expand
4. ☑️ **Klik lagi** untuk uncheck
5. 👀 **Amati** animasi collapse

### ✅ Expected Results:
- [ ] Animation smooth expand saat dicentang
- [ ] Date inputs muncul dengan grid layout responsive
- [ ] Labels "Tanggal Mulai" dan "Tanggal Akhir" tampil
- [ ] Animation smooth collapse saat uncheck
- [ ] Input values ter-reset saat uncheck

---

## 🧪 Test Case 3: Date Input Functionality

### 📍 Langkah Testing:
1. ✅ **Centang** checkbox custom date
2. 📅 **Klik** input "Tanggal Mulai"
3. 🗓️ **Pilih** tanggal (contoh: 2025-01-15)
4. 📅 **Klik** input "Tanggal Akhir" 
5. 🗓️ **Pilih** tanggal (contoh: 2025-02-15)

### ✅ Expected Results:
- [ ] Date picker native browser muncul
- [ ] Input menerima format YYYY-MM-DD
- [ ] Visual feedback saat input change
- [ ] Values tersimpan di state component

---

## 🧪 Test Case 4: Validation Error Handling

### 📍 Test 4a: Empty Start Date
1. ✅ **Centang** checkbox custom date
2. 📅 **Isi** tanggal akhir: `2025-02-15`
3. ⬜ **Biarkan** tanggal mulai kosong
4. 🚀 **Klik** tombol "Auto-Fill"

**Expected:** ❌ Toast error: "Silakan pilih tanggal mulai dan akhir"

### 📍 Test 4b: Empty End Date  
1. ✅ **Centang** checkbox custom date
2. 📅 **Isi** tanggal mulai: `2025-01-15`
3. ⬜ **Biarkan** tanggal akhir kosong
4. 🚀 **Klik** tombol "Auto-Fill"

**Expected:** ❌ Toast error: "Silakan pilih tanggal mulai dan akhir"

### 📍 Test 4c: Invalid Date Range
1. ✅ **Centang** checkbox custom date
2. 📅 **Isi** tanggal mulai: `2025-02-15` 
3. 📅 **Isi** tanggal akhir: `2025-01-15`
4. 🚀 **Klik** tombol "Auto-Fill"

**Expected:** ❌ Toast error: "Tanggal mulai tidak boleh lebih besar dari tanggal akhir"

---

## 🧪 Test Case 5: Default Mode Auto-Fill

### 📍 Langkah Testing:
1. ☑️ **Pastikan** checkbox custom date **TIDAK** dicentang
2. 📅 **Set** periode form ke Januari 2025
3. 👤 **Pilih** karyawan terapis (contoh: "Isra")
4. 🚀 **Klik** tombol "Auto-Fill"

### ✅ Expected Results:
- [ ] Loading spinner muncul di tombol
- [ ] Console log menunjukkan date range: 31 Dec 2024 - 29 Jan 2025
- [ ] API call ke BreakTime dengan parameter bulan=1, tahun=2025
- [ ] Data omzet, komisi, lembur ter-fill otomatis
- [ ] Success message tampil dengan info periode

---

## 🧪 Test Case 6: Custom Mode Auto-Fill

### 📍 Langkah Testing:
1. ✅ **Centang** checkbox "Gunakan Tanggal Custom"
2. 📅 **Set** tanggal mulai: `2025-01-15`
3. 📅 **Set** tanggal akhir: `2025-02-15`
4. 👤 **Pilih** karyawan terapis (contoh: "Isra")  
5. 🚀 **Klik** tombol "Auto-Fill"

### ✅ Expected Results:
- [ ] Loading spinner muncul di tombol
- [ ] Console log menunjukkan custom date range: 2025-01-15 - 2025-02-15
- [ ] API call ke BreakTime dengan parameter startDate & endDate
- [ ] Data omzet, komisi, lembur ter-fill sesuai periode custom
- [ ] Success message tampil dengan info periode custom

---

## 🧪 Test Case 7: Reset Form Functionality

### 📍 Langkah Testing:
1. ✅ **Centang** checkbox custom date
2. 📅 **Isi** kedua tanggal
3. 🚀 **Lakukan** auto-fill sampai success
4. 🔄 **Klik** tombol "Reset" atau buat slip gaji baru
5. 🔍 **Verifikasi** semua state ter-reset

### ✅ Expected Results:
- [ ] Checkbox custom date kembali unchecked
- [ ] Input tanggal kembali kosong
- [ ] Info text kembali ke mode default
- [ ] Auto-fill message dibersihkan
- [ ] Form data kembali ke nilai default

---

## 🧪 Test Case 8: Responsive Design

### 📍 Langkah Testing:
1. 💻 **Test** di desktop (1920x1080)
2. 📱 **Test** di tablet (768px width)
3. 📱 **Test** di mobile (375px width)
4. ✅ **Centang** checkbox di setiap breakpoint

### ✅ Expected Results:
- [ ] **Desktop**: Date inputs dalam 2 kolom (sm:grid-cols-2)
- [ ] **Mobile**: Date inputs dalam 1 kolom (grid-cols-1) 
- [ ] **Tablet**: Layout responsive sesuai ukuran
- [ ] **Semua**: Text readable, checkbox clickable, spacing adequate

---

## 🧪 Test Case 9: API Integration Test

### 📍 Langkah Testing (Dengan Dev Tools):
1. 🔧 **Buka** Developer Tools → Network tab
2. ✅ **Setup** custom date mode dengan valid dates
3. 🚀 **Trigger** auto-fill 
4. 🔍 **Monitor** network requests

### ✅ Expected Network Calls:
- [ ] **URL**: `https://www.mybreaktime.co.id/api/payroll/therapist`
- [ ] **Method**: GET
- [ ] **Headers**: `x-api-key: [API_KEY]`
- [ ] **Query Params**: `therapistName=Isra&startDate=2025-01-15&endDate=2025-02-15`
- [ ] **NO** bulan/tahun params dalam custom mode

---

## 🧪 Test Case 10: Console Logging

### 📍 Langkah Testing:
1. 🔧 **Buka** Developer Tools → Console tab
2. 🧹 **Clear** console log
3. 🚀 **Trigger** auto-fill dalam kedua mode
4. 🔍 **Verifikasi** log messages

### ✅ Expected Console Output:

**Default Mode:**
```
🔍 autoFillTherapistData called with date range: {
  therapistName: "Isra",
  bulan: 1,
  tahun: 2025,
  customStartDate: undefined,
  customEndDate: undefined,
  startDate: "2024-12-31",
  endDate: "2025-01-29",
  periode: "31 Desember 2024 - 29 Januari 2025"
}
```

**Custom Mode:**
```
📅 Using custom date range: {
  customStartDate: "2025-01-15", 
  customEndDate: "2025-02-15"
}
🔍 autoFillTherapistData called with date range: {
  therapistName: "Isra",
  customStartDate: "2025-01-15",
  customEndDate: "2025-02-15",
  startDate: "2025-01-15",
  endDate: "2025-02-15",
  periode: "2025-01-15 - 2025-02-15"
}
```

---

## 🎯 Acceptance Criteria Checklist

### ✅ Functional Requirements
- [ ] Checkbox toggle animation works smoothly
- [ ] Date inputs appear/disappear correctly  
- [ ] Validation prevents invalid inputs
- [ ] Default mode uses 30-30/31-29 logic
- [ ] Custom mode uses exact user dates
- [ ] API integration sends correct parameters
- [ ] Reset functionality clears all states
- [ ] Success/error messages display properly

### ✅ Non-Functional Requirements  
- [ ] UI responsive across all screen sizes
- [ ] Animations smooth and performant
- [ ] Accessibility labels present
- [ ] Text readable in light/dark themes
- [ ] No console errors during operation
- [ ] No memory leaks from unused listeners

### ✅ Integration Requirements
- [ ] Backward compatible with existing auto-fill
- [ ] Does not break existing form validation
- [ ] Plays well with existing error handling
- [ ] Toast notifications work correctly
- [ ] Loading states function properly

---

## 🚀 Final Validation

### 🏁 End-to-End Workflow Test
1. **Start** → Pilih karyawan terapis
2. **Mode 1** → Test default auto-fill (checkbox unchecked)
3. **Mode 2** → Test custom auto-fill (checkbox checked)
4. **Validation** → Test semua error scenarios
5. **Reset** → Test form reset functionality
6. **Mobile** → Test di mobile device
7. **Finish** → Konfirmasi semua test PASS

### 🎉 Success Metrics
- ✅ **Zero errors** di console
- ✅ **All validation** works correctly  
- ✅ **API integration** successful
- ✅ **UI responsive** di semua breakpoint
- ✅ **User experience** smooth dan intuitive

---

## 📞 Reporting Issues

Jika ada issues selama testing:

1. 📸 **Screenshot** dari issue
2. 🖥️ **Device/Browser** information  
3. 📝 **Steps to reproduce**
4. ❌ **Expected vs Actual** behavior
5. 🔍 **Console errors** (jika ada)

**Tim keuangan siap testing fitur custom date picker! 🎯✨** 