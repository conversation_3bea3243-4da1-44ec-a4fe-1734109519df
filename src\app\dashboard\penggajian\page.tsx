"use client"

import { useState, useEffect, useRef, useMemo } from "react"
import { motion } from "framer-motion"
import {
  Plus,
  Pencil,
  Trash2,
  Search,
  Calendar,
  DollarSign,
  ArrowDown,
  ArrowUp,
  Send,
  CheckCircle,
  XCircle,
  AlertCircle,
  AlertTriangle,
  CreditCard,
  MinusCircle,
  X,
  ChevronDown
} from "lucide-react"
import { SlipGaji, CreateSlipGajiDTO } from "@/types/slip-gaji"
import { Employee } from "@/types/employee"
import { Position } from "@/types/position"
import {
  getSlipGaji,
  createSlipGaji,
  formatCurrency,
  formatPeriode,
  calculateTotalPendapatan,
  calculateTotalPotongan,
  updateSlipGaji
} from "@/services/slip-gaji"
import { getEmployees } from "@/services/employee"
import { getPositions } from "@/services/position"
import { toast } from "react-hot-toast"
import { useRouter } from "next/navigation"
import { getDailyCommissionsByEmployee } from "@/services/commission"
import { ApiResponse } from "@/types/api"
import { autoFillTherapistData, searchTherapists, PayrollTherapistSummary } from "@/services/payroll-api"

// Deklarasi tipe di luar komponen
type FormDataPath = {
  gaji: {
    pokok: number;
    tunjangan: {
      transport: number;
      pulsa: number;
      makan: number;
      bpjs: number;
      penyesuaian: number;
    };
    lembur: number;
    bonus: number;
    komisi: number;
    omzet: number;
    rasioOmzet: number;
    hariMasuk: number;
    potongan: {
      kasbon: number;
      piutang: number;
      bpjsTP: number;
      bpjsTK: number;
      lainnya: number;
      penyesuaian: number;
      keterangan: string;
    };
    total: number;
  };
};

type PathKey = keyof FormDataPath | keyof FormDataPath['gaji'] | keyof FormDataPath['gaji']['tunjangan'] | keyof FormDataPath['gaji']['potongan'];

export default function SalaryPage() {
  const router = useRouter()
  const [slipGaji, setSlipGaji] = useState<SlipGaji[]>([])
  const [employees, setEmployees] = useState<Employee[]>([])
  const [positions, setPositions] = useState<Position[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 30
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [deletingSlip, setDeletingSlip] = useState<SlipGaji | null>(null)
  const [editingSlip, setEditingSlip] = useState<SlipGaji | null>(null)
  const [formData, setFormData] = useState<CreateSlipGajiDTO>({
    employeeId: "",
    periode: {
      bulan: new Date().getMonth() + 1,
      tahun: new Date().getFullYear()
    },
    gaji: {
      pokok: 0,
      tunjangan: {
        transport: 0,
        pulsa: 0,
        makan: 0,
        bpjs: 0,
        penyesuaian: 0
      },
      lembur: 0,
      bonus: 0,
      komisi: 0,
      omzet: 0,
      rasioOmzet: 0,
      hariMasuk: 0,
      potongan: {
        kasbon: 0,
        piutang: 0,
      bpjsTP: 0,
      bpjsTK: 0,
        lainnya: 0,
        penyesuaian: 0,
        keterangan: ""
    },
      total: 0
    }
  })

  // Tambahkan state untuk error
  const [formErrors, setFormErrors] = useState<{
    employeeId?: string;
    periode?: string;
    gaji?: {
      pokok?: string;
      tunjangan?: {
        transport?: string;
        pulsa?: string;
        makan?: string;
        bpjs?: string;
      };
      lembur?: string;
      bonus?: string;
      komisi?: string;
      potongan?: {
        kasbon?: string;
        piutang?: string;
        bpjsTP?: string;
        bpjsTK?: string;
        lainnya?: string;
      };
    };
  }>({})

  const [employeeSearch, setEmployeeSearch] = useState("")
  const [isEmployeeDropdownOpen, setIsEmployeeDropdownOpen] = useState(false)
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null)
  
  // State untuk auto-fill payroll
  const [isAutoFilling, setIsAutoFilling] = useState(false)
  const [autoFillMessage, setAutoFillMessage] = useState("")
  const [showAutoFillButton, setShowAutoFillButton] = useState(false)
  const [isAutoFilled, setIsAutoFilled] = useState(false)
  const [isCalculatingAdjustment, setIsCalculatingAdjustment] = useState(false) // Flag untuk mencegah override setelah auto-fill
  
  // State untuk custom date range picker
  const [useCustomDateRange, setUseCustomDateRange] = useState(false)
  const [customStartDate, setCustomStartDate] = useState("")
  const [customEndDate, setCustomEndDate] = useState("")

  // Ref untuk dropdown container
  const dropdownRef = useRef<HTMLDivElement>(null)

  // Event listener untuk menutup dropdown saat klik di luar
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsEmployeeDropdownOpen(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [])

  // Filter karyawan berdasarkan pencarian
  const filteredEmployees = useMemo(() =>
    employees.filter(employee =>
      employee.user?.name.toLowerCase().includes(employeeSearch.toLowerCase()) ||
      employee.position?.name.toLowerCase().includes(employeeSearch.toLowerCase())
    ), [employees, employeeSearch]
  )

  // Fetch data
  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      const [slipGajiData, employeesData, positionsData] = await Promise.all([
        getSlipGaji(),
        getEmployees(),
        getPositions()
      ])
      setSlipGaji(slipGajiData)
      setEmployees(employeesData)
      setPositions(positionsData)
    } catch (error) {
      toast.error("Gagal mengambil data")
    } finally {
      setIsLoading(false)
    }
  }

  // Filter slip gaji based on search query
  const filteredSlipGaji = useMemo(() =>
    slipGaji.filter(slip =>
    slip.employee.user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    slip.employee.position.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    formatPeriode(slip.periode).toLowerCase().includes(searchQuery.toLowerCase())
    ), [slipGaji, searchQuery]
  )

  // Pagination logic
  const { totalPages, startIndex, endIndex, currentData } = useMemo(() => ({
    totalPages: Math.ceil(filteredSlipGaji.length / itemsPerPage),
    startIndex: (currentPage - 1) * itemsPerPage,
    endIndex: ((currentPage - 1) * itemsPerPage) + itemsPerPage,
    currentData: filteredSlipGaji.slice(
      (currentPage - 1) * itemsPerPage,
      ((currentPage - 1) * itemsPerPage) + itemsPerPage
    )
  }), [filteredSlipGaji, currentPage, itemsPerPage])

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  // Validasi form
  const validateForm = () => {
    // Hanya jalankan validasi jika form sudah pernah disubmit
    if (!isSubmitting) return true

    const errors: typeof formErrors = {}

    // Validasi employee
    if (!formData.employeeId) {
      errors.employeeId = "Karyawan harus dipilih"
    }

    // Validasi periode
    if (!formData.periode.bulan || !formData.periode.tahun) {
      errors.periode = "Periode harus diisi"
    }

    // Validasi gaji
    if (!formData.gaji.pokok) {
      if (!errors.gaji) errors.gaji = {}
      errors.gaji.pokok = "Gaji pokok harus diisi"
    }

    // Validasi tunjangan
    if (!formData.gaji.tunjangan.transport && !formData.gaji.tunjangan.pulsa &&
        !formData.gaji.tunjangan.makan && !formData.gaji.tunjangan.bpjs) {
      if (!errors.gaji) errors.gaji = {}
      if (!errors.gaji.tunjangan) errors.gaji.tunjangan = {}
      errors.gaji.tunjangan.transport = "Minimal satu tunjangan harus diisi"
    }

    // Validasi potongan BPJS
    if (!formData.gaji.potongan.bpjsTP || !formData.gaji.potongan.bpjsTK) {
      if (!errors.gaji) errors.gaji = {}
      if (!errors.gaji.potongan) errors.gaji.potongan = {}
      if (!formData.gaji.potongan.bpjsTP) {
        errors.gaji.potongan.bpjsTP = "BPJS Tenaga Pensiun harus diisi"
      }
      if (!formData.gaji.potongan.bpjsTK) {
        errors.gaji.potongan.bpjsTK = "BPJS Tenaga Kerja harus diisi"
      }
    }

    setFormErrors(errors)
    return Object.keys(errors).length === 0
  }

  // Fungsi untuk menghitung total pendapatan
  const calculateTotalPendapatan = (data: CreateSlipGajiDTO) => {
    // Pendapatan Tetap
    const gajiPokok = data.gaji.pokok || 0
    const tunjanganTransport = data.gaji.tunjangan.transport || 0
    const tunjanganPulsa = data.gaji.tunjangan.pulsa || 0
    const tunjanganMakan = data.gaji.tunjangan.makan || 0
    const tunjanganBPJS = data.gaji.tunjangan.bpjs || 0
    const komisi = data.gaji.komisi || 0
    const lembur = data.gaji.lembur || 0
    const bonus = data.gaji.bonus || 0
    const penyesuaian = data.gaji.tunjangan.penyesuaian || 0

    // Total sebelum penyesuaian
    const totalSebelumPenyesuaian = gajiPokok + tunjanganTransport + tunjanganPulsa +
      tunjanganMakan + tunjanganBPJS + komisi + lembur + bonus

    // Total akhir termasuk penyesuaian
    const total = (totalSebelumPenyesuaian || 0) + (penyesuaian || 0)

    return {
      total: total || 0,
      totalSebelumPenyesuaian: totalSebelumPenyesuaian || 0,
      penyesuaian: penyesuaian || 0
    }
  }

  // Modifikasi fungsi formatNumber untuk menangani desimal
  function formatNumber(value: number | string): string {
    if (!value || isNaN(Number(value))) return "Rp 0"

    const number = typeof value === 'string' ?
      parseFloat(value.replace(/[^\d.-]/g, '')) :
      value

    const formatted = Math.round(number).toLocaleString('id-ID')

    return `Rp ${formatted}`
  }

  // Modifikasi fungsi parseNumber
  function parseNumber(value: string): number {
    if (!value) return 0

    const cleaned = value.replace(/[Rp\s.]/g, '').replace(',', '.')
    const parsed = parseFloat(cleaned)

    return isNaN(parsed) ? 0 : Math.round(parsed)
  }

  // Handle perubahan input (omzet dan hari masuk)
  const handleInputChange = (value: string, type: 'omzet' | 'hariMasuk') => {
    const numValue = parseNumber(value) || 0

    setFormData(prev => {
      const updatedFormData = {
        ...prev,
        gaji: {
          ...prev.gaji,
          [type]: numValue
        }
      }

      const selectedEmployee = employees.find(emp => emp.id === updatedFormData.employeeId)
      const position = selectedEmployee?.position

      if (!position) return updatedFormData

      if (position.isKontrak) {
        const targetKontrak = position.targetKontrak || 0
        if (targetKontrak > 0) {
          const gajiPokokBaru = Math.round(
            (((updatedFormData.gaji.omzet * 100) / targetKontrak) * 0.01) * position.gajiPokok
          )
          updatedFormData.gaji.pokok = gajiPokokBaru

          // Tambahkan logika untuk menghilangkan potongan penyesuaian jika mencapai/melebihi target
          if (updatedFormData.gaji.omzet >= targetKontrak) {
            updatedFormData.gaji.potongan.penyesuaian = 0
          }
        }
      } else {
        // Hitung penyesuaian dengan state management
        setIsCalculatingAdjustment(true)
        const { tunjanganPenyesuaian, potonganPenyesuaian } = calculatePenyesuaian(updatedFormData, position)
        updatedFormData.gaji.tunjangan.penyesuaian = tunjanganPenyesuaian
        updatedFormData.gaji.potongan.penyesuaian = potonganPenyesuaian
        setIsCalculatingAdjustment(false)
      }

      return updatedFormData
    })

    // callback(numericValue)
  }

  // Handle submit form
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsSubmitting(true)

    // Validasi employeeId
    if (!formData.employeeId || !selectedEmployee) {
      toast.error("Mohon pilih karyawan terlebih dahulu")
      setIsSubmitting(false)
      return
    }

    // Cek apakah employee ada
    const employeeExists = employees.some(emp => emp.id === formData.employeeId)
    if (!employeeExists) {
      toast.error("Data karyawan tidak valid")
      setIsSubmitting(false)
      return
    }

    try {
      // Hitung pendapatan dan potongan
      const { total: totalPendapatan, penyesuaian } = calculateTotalPendapatan(formData)
      const totalPotongan = calculateTotalPotongan(formData)

      // Update formData dengan nilai yang sudah dihitung
      const updatedFormData = {
        ...formData,
        gaji: {
          ...formData.gaji,
          tunjangan: {
            ...formData.gaji.tunjangan,
            transport: formData.gaji.tunjangan.transport || 0,
            pulsa: formData.gaji.tunjangan.pulsa || 0,
            makan: formData.gaji.tunjangan.makan || 0,
            bpjs: formData.gaji.tunjangan.bpjs || 0,
            penyesuaian: penyesuaian || 0
          },
          potongan: {
            ...formData.gaji.potongan,
            kasbon: formData.gaji.potongan.kasbon || 0,
            piutang: formData.gaji.potongan.piutang || 0,
            bpjsTP: formData.gaji.potongan.bpjsTP || 0,
            bpjsTK: formData.gaji.potongan.bpjsTK || 0,
            lainnya: formData.gaji.potongan.lainnya || 0,
            penyesuaian: formData.gaji.potongan.penyesuaian || 0,
            keterangan: formData.gaji.potongan.keterangan || ""
          },
          total: (totalPendapatan || 0) - (totalPotongan || 0)
        }
      }

      let response
      if (editingSlip) {
        response = await updateSlipGaji(editingSlip.id, updatedFormData)
      } else {
        response = await createSlipGaji(updatedFormData)
      }

      if (response.error) {
        throw new Error(response.error)
      }

      toast.success(editingSlip ? "Slip gaji berhasil diupdate" : "Slip gaji berhasil dibuat")
      setIsModalOpen(false)
      setEditingSlip(null)
      resetForm()
      fetchData()
    } catch (error) {
      console.error("Error submitting form:", error)
      // Tambahkan type guard untuk error
      const errorMessage = error instanceof Error ? error.message : "Gagal menyimpan slip gaji. Silakan coba lagi."
      toast.error(errorMessage)
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle slip deletion
  const handleDelete = async () => {
    if (!deletingSlip) return

    try {
      await fetch(`/api/slip-gaji/${deletingSlip.id}`, {
        method: 'DELETE'
      })
      toast.success("Data slip gaji berhasil dihapus")
      setIsDeleteModalOpen(false)
      setDeletingSlip(null)
      fetchData()
    } catch (error) {
      toast.error("Gagal menghapus data slip gaji")
    }
  }

  // Handle delete button click
  const handleDeleteClick = (slip: SlipGaji) => {
    setDeletingSlip(slip)
    setIsDeleteModalOpen(true)
  }

  // Handle edit button click
  const handleEdit = (slip: SlipGaji) => {
    setEditingSlip(slip)
    setFormData({
      employeeId: slip.employeeId,
      periode: slip.periode,
      gaji: {
        ...slip.gaji,
        potongan: {
          ...slip.gaji.potongan,
          keterangan: slip.gaji.potongan.keterangan || ""
        }
      }
    })

    // Tambahkan ini untuk set employee yang dipilih
    const selectedEmployee = employees.find(emp => emp.id === slip.employeeId)
    if (selectedEmployee) {
      setSelectedEmployee(selectedEmployee)
      setEmployeeSearch(selectedEmployee.user.name)
    }

    setIsModalOpen(true)
  }

  // Calculate total for current period
  const periodTotal = filteredSlipGaji.reduce((acc, slip) => acc + slip.gaji.total, 0)

  // Reset form dan error
  const resetForm = () => {
    setFormData({
      employeeId: "",
      periode: {
        bulan: new Date().getMonth() + 1,
        tahun: new Date().getFullYear()
      },
      gaji: {
        pokok: 0,
        tunjangan: {
          transport: 0,
          pulsa: 0,
          makan: 0,
          bpjs: 0,
          penyesuaian: 0
        },
        lembur: 0,
        bonus: 0,
        komisi: 0,
        omzet: 0,
        rasioOmzet: 0,
        hariMasuk: 0,
        potongan: {
          kasbon: 0,
          piutang: 0,
        bpjsTP: 0,
        bpjsTK: 0,
          lainnya: 0,
          penyesuaian: 0,
          keterangan: ""
        },
        total: 0
      }
    })
    setFormErrors({})
    resetEmployeeSearch()
    setIsAutoFilled(false) // Reset flag auto-fill
    setAutoFillMessage("") // Reset message auto-fill
    setIsCalculatingAdjustment(false) // Reset flag calculation
    // Reset custom date range state
    setUseCustomDateRange(false)
    setCustomStartDate("")
    setCustomEndDate("")
    console.log("🔄 Form, flag auto-fill, calculation, dan custom date range telah direset")
  }

  // Tambahkan fungsi untuk handle kirim ke WhatsApp
  const handleSendToWhatsApp = async (slip: SlipGaji) => {
    try {
      // Cek apakah slip gaji sudah ada
      const existingSlip = slipGaji.find(s =>
        s.employeeId === slip.employeeId &&
        s.periode.bulan === slip.periode.bulan &&
        s.periode.tahun === slip.periode.tahun
      )

      // Tentukan method HTTP berdasarkan apakah data sudah ada
      const method = existingSlip ? 'PUT' : 'POST'
      const url = existingSlip ? `/api/slip-gaji/${existingSlip.id}` : '/api/slip-gaji'

      // Tampilkan loading
      const loadingToast = toast.loading("Mengirim data ke slip gaji...")

      // Hitung total gaji
      const totalTunjangan =
        slip.gaji.tunjangan.transport +
        slip.gaji.tunjangan.pulsa +
        slip.gaji.tunjangan.makan +
        slip.gaji.tunjangan.bpjs +
        slip.gaji.tunjangan.penyesuaian +
        slip.gaji.lembur +
        slip.gaji.bonus +
        slip.gaji.komisi

      const totalPotongan =
        slip.gaji.potongan.kasbon +
        slip.gaji.potongan.piutang +
        slip.gaji.potongan.bpjsTP +
        slip.gaji.potongan.bpjsTK +
        slip.gaji.potongan.lainnya

      const totalGaji = slip.gaji.pokok + totalTunjangan - totalPotongan

      // Validasi data sebelum dikirim
      if (totalGaji < 0) {
        toast.dismiss(loadingToast)
        toast.error("Total gaji tidak boleh kurang dari 0")
        return
      }

      // Kirim data ke database slip gaji
      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          employeeId: slip.employeeId,
          periode: slip.periode,
          gaji: {
            ...slip.gaji,
            total: totalGaji
          }
        })
      });

      const responseData = await response.json();

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      if (response.ok) {
        // Update state tanpa perlu fetch ulang
        setSlipGaji((prev: any[]) => {
          if (existingSlip) {
            // Update existing slip
            return prev.map((s: any) =>
              s.id === existingSlip.id ? { ...s, ...responseData } : s
            );
          } else {
            // Add new slip
            return [...prev, responseData];
          }
        });
        toast.success("Data berhasil dikirim ke slip gaji");
      } else {
        console.error("Error response:", responseData)
        toast.error(responseData.error || "Gagal mengirim data")
      }
    } catch (error) {
      console.error("Error sending data:", error)
      toast.error("Terjadi kesalahan saat mengirim data")
    }
  }

  // Fungsi untuk mengambil total kasbon dan piutang yang disetujui
  const fetchApprovedKasbonPiutang = async (employeeId: string, startDate: Date, endDate: Date) => {
    try {
      console.log(`Mengambil data kasbon untuk karyawan ${employeeId}`, {
        startDate: startDate.toISOString(),
        endDate: endDate.toISOString()
      });

      // Menggunakan Date.UTC untuk konsistensi timezone
      const utcStartDate = new Date(Date.UTC(startDate.getFullYear(), startDate.getMonth(), startDate.getDate()));
      const utcEndDate = new Date(Date.UTC(endDate.getFullYear(), endDate.getMonth(), endDate.getDate(), 23, 59, 59));

      console.log(`Tanggal yang digunakan:`, {
        utcStartDate: utcStartDate.toISOString(),
        utcEndDate: utcEndDate.toISOString()
      });

      const response = await fetch(`/api/kasbon?employeeId=${employeeId}&status=APPROVED&startDate=${utcStartDate.toISOString()}&endDate=${utcEndDate.toISOString()}`)

      console.log(`Status respons API kasbon: ${response.status}`);

      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Error API kasbon: ${errorText}`);
        throw new Error(`Failed to fetch kasbon data: ${response.status} ${errorText}`);
      }

      const kasbonData = await response.json();
      console.log(`Data kasbon yang diterima:`, kasbonData);

      // Pastikan kasbonData adalah array
      if (!Array.isArray(kasbonData)) {
        console.error('Respons API kasbon bukan array:', kasbonData);
        return { totalKasbon: 0, totalPiutang: 0 };
      }

      // Hitung total kasbon dan piutang yang disetujui
      const totalKasbon = kasbonData
        .filter((k: any) => k.type === 'KASBON' && k.status === 'APPROVED')
        .reduce((sum: number, k: any) => sum + k.amount, 0);

      const totalPiutang = kasbonData
        .filter((k: any) => k.type === 'PIUTANG' && k.status === 'APPROVED')
        .reduce((sum: number, k: any) => sum + k.amount, 0);

      console.log(`Total kasbon: ${totalKasbon}, Total piutang: ${totalPiutang}`);

      return { totalKasbon, totalPiutang }
    } catch (error) {
      console.error('Error fetching kasbon data:', error);
      toast.error('Gagal mengambil data kasbon dan piutang');
      return { totalKasbon: 0, totalPiutang: 0 }
    }
  }

  // Modifikasi handleEmployeeSelect untuk mengisi field kasbon dan piutang
  const handleEmployeeSelect = async (employee: Employee) => {
    try {
      // Reset flag auto-fill ketika employee baru dipilih
      setIsAutoFilled(false)
      console.log("🔄 Reset flag isAutoFilled karena employee baru dipilih")
      
      const position = positions.find(pos => pos.id === employee.positionId)

      // Set data dasar
      const initialFormData = {
        employeeId: employee.id,
        periode: {
          bulan: new Date().getMonth() + 1,
          tahun: new Date().getFullYear()
        },
        gaji: {
          pokok: position?.gajiPokok || 0,
          tunjangan: {
            transport: position?.tunjangan?.transport || 0,
            pulsa: position?.tunjangan?.pulsa || 0,
            makan: position?.tunjangan?.makan || 0,
            bpjs: 0,
            penyesuaian: 0
          },
          lembur: 0,
          bonus: 0,
          komisi: 0,
          omzet: 0,
          rasioOmzet: position?.omsetPercentage || 0,
          hariMasuk: 0, // Default kosong
          potongan: {
            kasbon: 0,
            piutang: 0,
            bpjsTP: 0,
            bpjsTK: 0,
            lainnya: 0,
            penyesuaian: 0,
            keterangan: ""
          },
          total: 0
        }
      }

      // Set selected employee
      setSelectedEmployee(employee)
      setEmployeeSearch(employee.user.name)
      setIsEmployeeDropdownOpen(false)

      console.log(`Mengambil data komisi untuk karyawan ${employee.id}, bulan ${initialFormData.periode.bulan}, tahun ${initialFormData.periode.tahun}`)

      // Ambil data komisi hanya jika belum auto-fill
      if (!isAutoFilled) {
        try {
        const commissionData = await getDailyCommissionsByEmployee(
          employee.id,
          initialFormData.periode.bulan,
          initialFormData.periode.tahun
        )

          console.log('Data komisi yang diterima:', commissionData)

          if (commissionData) {
          // Update omzet dan komisi
          initialFormData.gaji.omzet = commissionData.totalOmzet || 0
            initialFormData.gaji.komisi = commissionData.totalCommission || 0

            console.log(`Omzet ditetapkan ke: ${initialFormData.gaji.omzet}`)
            console.log(`Komisi ditetapkan ke: ${initialFormData.gaji.komisi}`)

        // Hitung penyesuaian jika jabatan adalah Terapis Kontrak
        if (position?.isKontrak) {
          const targetKontrak = position.targetKontrak || 0
            console.log(`Target kontrak: ${targetKontrak}`)

          if (targetKontrak > 0) {
            const gajiPokokBaru = (((initialFormData.gaji.omzet * 100) / targetKontrak) * 0.01) * position.gajiPokok
            initialFormData.gaji.pokok = Math.round(gajiPokokBaru)
              console.log(`Gaji pokok baru dihitung: ${initialFormData.gaji.pokok}`)

              // Tambahkan logika untuk menghilangkan potongan penyesuaian jika mencapai/melebihi target
              if (initialFormData.gaji.omzet >= targetKontrak) {
                initialFormData.gaji.potongan.penyesuaian = 0
                console.log('Potongan penyesuaian ditetapkan ke 0 karena mencapai target')
              }
          }
        } else {
          // Hitung penyesuaian untuk jabatan lain
          const totalSebelumPenyesuaian =
            initialFormData.gaji.pokok +
            initialFormData.gaji.tunjangan.transport +
            initialFormData.gaji.tunjangan.pulsa +
            initialFormData.gaji.tunjangan.makan +
            initialFormData.gaji.komisi +
            initialFormData.gaji.bonus +
            initialFormData.gaji.lembur

          const rasioOmzet = position?.omsetPercentage || 0
          const targetGaji = ((initialFormData.gaji.omzet * rasioOmzet) / 100) || 0

            console.log(`Total sebelum penyesuaian: ${totalSebelumPenyesuaian}`)
            console.log(`Rasio omzet: ${rasioOmzet}`)
            console.log(`Target gaji: ${targetGaji}`)

          if (initialFormData.gaji.omzet === 0) {
            initialFormData.gaji.tunjangan.penyesuaian = 0
            initialFormData.gaji.potongan.penyesuaian = 0
              console.log('Omzet 0, kedua penyesuaian ditetapkan ke 0')
          } else if (targetGaji > totalSebelumPenyesuaian) {
            initialFormData.gaji.tunjangan.penyesuaian = targetGaji - totalSebelumPenyesuaian
              initialFormData.gaji.potongan.penyesuaian = 0
              console.log(`Tunjangan penyesuaian ditetapkan ke: ${initialFormData.gaji.tunjangan.penyesuaian}`)
          } else if (totalSebelumPenyesuaian > targetGaji) {
              initialFormData.gaji.tunjangan.penyesuaian = 0
            initialFormData.gaji.potongan.penyesuaian = totalSebelumPenyesuaian - targetGaji
              console.log(`Potongan penyesuaian ditetapkan ke: ${initialFormData.gaji.potongan.penyesuaian}`)
            } else {
              initialFormData.gaji.tunjangan.penyesuaian = 0
              initialFormData.gaji.potongan.penyesuaian = 0
              console.log('Target sama dengan total, kedua penyesuaian ditetapkan ke 0')
            }
          }
        } else {
          console.warn('Tidak ada data komisi yang diterima')
        }
      } catch (commissionError) {
        console.error('Error saat mengambil data komisi:', commissionError)
        toast.error('Gagal mengambil data komisi. Data omzet dan komisi akan ditetapkan ke 0.')
      }
      } else {
        console.log("🔒 Skip fetch komisi - data sudah auto-filled")
        // Tetap jalankan perhitungan penyesuaian dengan nilai yang sudah ada dari auto-fill
        if (formData.gaji.omzet > 0) {
          const position = positions.find(pos => pos.id === employee.positionId)
          if (position && !position.isKontrak) {
            // Update data dengan nilai auto-fill terlebih dahulu
            initialFormData.gaji.omzet = formData.gaji.omzet
            initialFormData.gaji.komisi = formData.gaji.komisi
            initialFormData.gaji.lembur = formData.gaji.lembur
            
            // Hitung penyesuaian dengan state management
             setIsCalculatingAdjustment(true)
             const { tunjanganPenyesuaian, potonganPenyesuaian } = calculatePenyesuaian(initialFormData, position)
             initialFormData.gaji.tunjangan.penyesuaian = tunjanganPenyesuaian
             initialFormData.gaji.potongan.penyesuaian = potonganPenyesuaian
             setIsCalculatingAdjustment(false)
            
            console.log("✅ Perhitungan penyesuaian dengan nilai auto-fill selesai")
          }
        }
      }

      // Ambil data kasbon dan piutang yang disetujui
      try {
        // Tampilkan loading toast
        const kasbonLoadingToast = toast.loading("Mengambil data kasbon dan piutang...");

        // Buat tanggal awal dan akhir dengan format yang benar untuk periode
        const startPeriode = new Date(Date.UTC(initialFormData.periode.tahun, initialFormData.periode.bulan - 1, 1, 0, 0, 0));
        const endPeriode = new Date(Date.UTC(initialFormData.periode.tahun, initialFormData.periode.bulan, 0, 23, 59, 59));

        console.log(`Mengambil kasbon untuk periode:`, {
          startPeriode: startPeriode.toISOString(),
          endPeriode: endPeriode.toISOString()
        });

      const { totalKasbon, totalPiutang } = await fetchApprovedKasbonPiutang(
        employee.id,
          startPeriode,
          endPeriode
        );

        // Dismiss loading toast
        toast.dismiss(kasbonLoadingToast);

      // Update form data dengan total kasbon dan piutang
        initialFormData.gaji.potongan.kasbon = totalKasbon;
        initialFormData.gaji.potongan.piutang = totalPiutang;

        console.log(`Kasbon dan piutang berhasil diambil:`, {
          totalKasbon,
          totalPiutang
        });

        if (totalKasbon > 0 || totalPiutang > 0) {
          toast.success(`Data kasbon dan piutang berhasil dimuat`);
        }
      } catch (kasbonError) {
        console.error('Error saat mengambil data kasbon dan piutang:', kasbonError);
        toast.error('Gagal mengambil data kasbon dan piutang');
      }

      // Sebelum mengatur formData, terapkan logika untuk semua jenis Admin dan Kasir
      const positionName = position?.name || ''
      const isAdmin = positionName.toLowerCase().includes('admin')
      const isKasir = positionName.toLowerCase().includes('kasir')

      console.log(`Posisi: ${positionName}, isAdmin: ${isAdmin}, isKasir: ${isKasir}`)

      // Jika Admin atau Kasir (termasuk semua variasinya), hitung tunjangan pulsa berdasarkan hari masuk
      if ((isAdmin || isKasir) && position?.tunjangan?.pulsa) {
        const hariMasuk = initialFormData.gaji.hariMasuk
        const pulsaPenuh = position.tunjangan.pulsa

        // Jika hari masuk penuh 28 hari, berikan pulsa penuh
        if (hariMasuk >= 28) {
          initialFormData.gaji.tunjangan.pulsa = pulsaPenuh
        } else {
          // Jika tidak penuh, berikan 50% dari nilai penuh
          initialFormData.gaji.tunjangan.pulsa = pulsaPenuh / 2
        }

        console.log(`Tunjangan pulsa untuk ${position.name} dengan ${hariMasuk} hari masuk: ${initialFormData.gaji.tunjangan.pulsa}`)
        console.log(`Perhitungan: Hari masuk < 28, tunjangan pulsa = ${pulsaPenuh} / 2 = ${initialFormData.gaji.tunjangan.pulsa}`)
      }

      setFormData(initialFormData)
    } catch (error) {
      console.error("Error in handleEmployeeSelect:", error)
      toast.error("Gagal memuat data karyawan. Silakan coba lagi.")
    }
  }

  // Modifikasi handlePeriodeChange untuk update komisi
  const handlePeriodeChange = async (field: "bulan" | "tahun", value: number) => {
    console.log(`Periode diubah: ${field} = ${value}`)

    setFormData(prev => ({
      ...prev,
      periode: {
        ...prev.periode,
        [field]: value
      }
    }))

    // Update komisi jika ada employee yang dipilih
    if (selectedEmployee) {
      console.log(`Mengupdate data komisi untuk karyawan: ${selectedEmployee.id}`)
      try {
        // Tampilkan loading toast
        const loadingToast = toast.loading("Memuat data komisi...")

        const commissionData = await getDailyCommissionsByEmployee(
          selectedEmployee.id,
          field === "bulan" ? value : formData.periode.bulan,
          field === "tahun" ? value : formData.periode.tahun
        )

        // Dismiss loading toast
        toast.dismiss(loadingToast)

        console.log('Data komisi yang diterima setelah perubahan periode:', commissionData)

        if (commissionData) {
          setFormData(prev => {
            // Salin formData sebelumnya
            const updatedFormData = { ...prev }

            // Update omzet dan komisi
            updatedFormData.gaji.omzet = commissionData.totalOmzet || 0
            updatedFormData.gaji.komisi = commissionData.totalCommission || 0

            console.log(`Omzet diperbarui ke: ${updatedFormData.gaji.omzet}`)
            console.log(`Komisi diperbarui ke: ${updatedFormData.gaji.komisi}`)

            // Dapatkan posisi karyawan
            const position = positions.find(pos => pos.id === selectedEmployee.positionId)

            if (position) {
              // Hitung ulang penyesuaian berdasarkan posisi
              if (position.isKontrak) {
                const targetKontrak = position.targetKontrak || 0
                console.log(`Target kontrak: ${targetKontrak}`)

                if (targetKontrak > 0 && updatedFormData.gaji.omzet > 0) {
                  const gajiPokokBaru = (((updatedFormData.gaji.omzet * 100) / targetKontrak) * 0.01) * position.gajiPokok
                  updatedFormData.gaji.pokok = Math.round(gajiPokokBaru)
                  console.log(`Gaji pokok baru dihitung: ${updatedFormData.gaji.pokok}`)

                  // Tambahkan logika untuk menghilangkan potongan penyesuaian jika mencapai/melebihi target
                  if (updatedFormData.gaji.omzet >= targetKontrak) {
                    updatedFormData.gaji.potongan.penyesuaian = 0
                    console.log('Potongan penyesuaian ditetapkan ke 0 karena mencapai target')
                  }
                }
              } else {
                // Hitung penyesuaian dengan state management
                setIsCalculatingAdjustment(true)
                const { tunjanganPenyesuaian, potonganPenyesuaian } = calculatePenyesuaian(updatedFormData, position)
                updatedFormData.gaji.tunjangan.penyesuaian = tunjanganPenyesuaian
                updatedFormData.gaji.potongan.penyesuaian = potonganPenyesuaian
                setIsCalculatingAdjustment(false)
              }
            }

            return updatedFormData
          })
        } else {
          console.warn('Tidak ada data komisi yang diterima setelah perubahan periode')
          toast.error('Tidak dapat memperoleh data komisi untuk periode yang dipilih')
        }
      } catch (error) {
        console.error('Error saat mengambil data komisi setelah perubahan periode:', error)
        toast.error('Gagal mengambil data komisi. Silakan coba lagi.')
      }

      // Update kasbon dan piutang juga saat periode berubah
      try {
        // Buat tanggal awal dan akhir dengan format yang benar untuk periode baru
        const newMonth = field === "bulan" ? value : formData.periode.bulan;
        const newYear = field === "tahun" ? value : formData.periode.tahun;

        const startPeriode = new Date(Date.UTC(newYear, newMonth - 1, 1, 0, 0, 0));
        const endPeriode = new Date(Date.UTC(newYear, newMonth, 0, 23, 59, 59));

        console.log(`Mengupdate kasbon dan piutang untuk periode baru:`, {
          period: `${newMonth}/${newYear}`,
          startPeriode: startPeriode.toISOString(),
          endPeriode: endPeriode.toISOString()
        });

        // Tampilkan loading toast
        const kasbonLoadingToast = toast.loading("Memuat data kasbon dan piutang...");

        const { totalKasbon, totalPiutang } = await fetchApprovedKasbonPiutang(
          selectedEmployee.id,
          startPeriode,
          endPeriode
        );

        // Dismiss loading toast
        toast.dismiss(kasbonLoadingToast);

        console.log(`Data kasbon dan piutang periode baru:`, {
          totalKasbon,
          totalPiutang
        });

        // Update form data dengan total kasbon dan piutang baru
          setFormData(prev => ({
            ...prev,
            gaji: {
              ...prev.gaji,
            potongan: {
              ...prev.gaji.potongan,
              kasbon: totalKasbon,
              piutang: totalPiutang
            }
          }
        }));

        if (totalKasbon > 0 || totalPiutang > 0) {
          toast.success(`Data kasbon dan piutang berhasil diperbarui`);
        }
      } catch (kasbonError) {
        console.error('Error saat mengambil data kasbon dan piutang setelah perubahan periode:', kasbonError);
        toast.error('Gagal mengambil data kasbon dan piutang untuk periode yang dipilih');
      }
    }
  }

  // Update handler untuk input number dengan perhitungan ulang penyesuaian
  const handleNumberInput = (
    value: string,
    path: PathKey[],
    callback: (value: number) => void
  ) => {
    const formattedValue = formatNumber(value)
    const numericValue = parseNumber(value)

    setFormData(prev => {
      const updatedFormData = { ...prev }
      let current: any = updatedFormData

      // Traverse path sampai ke parent dari target
      for (let i = 0; i < path.length - 1; i++) {
        current = current[path[i] as keyof typeof current]
      }

      // Update nilai
      const lastKey = path[path.length - 1]
      if (typeof lastKey === 'string') {
        current[lastKey] = numericValue

        // Jika yang diupdate adalah hari masuk atau bonus
        if (lastKey === 'hariMasuk' || lastKey === 'bonus') {
          const employee = employees.find(emp => emp.id === formData.employeeId)
          const position = positions.find(pos => pos.id === employee?.positionId)

          // Hitung gaji pokok berdasarkan hari masuk (hanya jika yang diubah adalah hari masuk)
          if (lastKey === 'hariMasuk') {
            const gajiPokok = position?.gajiPokok || 0
            const gajiPerHari = gajiPokok / 28
            const gajiBerdasarkanHariMasuk = Math.round(gajiPerHari * numericValue)

            updatedFormData.gaji.pokok = gajiBerdasarkanHariMasuk
          }

          // Hitung tunjangan transport, pulsa, dan makan berdasarkan hari masuk (hanya jika yang diubah adalah hari masuk)
          if (lastKey === 'hariMasuk') {
            const isRegulerLama = position?.name === 'Terapis Reguler Lama'
            const isRegulerBaru = position?.name === 'Terapis Reguler Baru'

            let transportPerHari = 0
            let pulsa = 0
            let makanPerHari = 0

            if (isRegulerLama) {
              transportPerHari = 10000
              // Untuk Reguler Lama: Jika hari masuk < 28, pulsa = 50% dari 50.000
              pulsa = numericValue >= 28 ? 50000 : 25000
              makanPerHari = 10000
            } else if (isRegulerBaru) {
              transportPerHari = 5000
              // Untuk Reguler Baru: Jika hari masuk < 28, pulsa = 50% dari 25.000
              pulsa = numericValue >= 28 ? 25000 : 12500
              makanPerHari = 5000
            } else {
              // Logika baru: Kalikan dengan hari masuk jika nilai transport dan makan antara 5000-10000
              // Ambil nilai tunjangan dari posisi karyawan
              transportPerHari = position?.tunjangan?.transport || 0
              makanPerHari = position?.tunjangan?.makan || 0

              // Logika untuk semua jenis Admin dan Kasir: pulsa dipotong berdasarkan hari masuk kecuali jika full 28 hari
              const positionName = position?.name || ''
              const isAdmin = positionName.toLowerCase().includes('admin')
              const isKasir = positionName.toLowerCase().includes('kasir')

              console.log(`Posisi: ${positionName}, isAdmin: ${isAdmin}, isKasir: ${isKasir}`)

              if ((isAdmin || isKasir) && position?.tunjangan?.pulsa) {
                const pulsaPenuh = position.tunjangan.pulsa

                // Jika hari masuk penuh 28 hari, berikan pulsa penuh
                if (numericValue >= 28) {
                  pulsa = pulsaPenuh
                } else {
                  // Jika tidak penuh, berikan 50% dari nilai penuh
                  pulsa = pulsaPenuh / 2
                }

                // Tambahkan log khusus untuk Admin dan Kasir
                console.log(`Tunjangan pulsa untuk ${position.name} dengan ${numericValue} hari masuk: ${pulsa}`)
                console.log(`Perhitungan: Hari masuk < 28, tunjangan pulsa = ${pulsaPenuh} / 2 = ${pulsa}`)
              } else {
                // Untuk jabatan lain, tetap gunakan nilai pulsa yang ada
                pulsa = updatedFormData.gaji.tunjangan.pulsa || position?.tunjangan?.pulsa || 0
              }
            }

            // Log untuk debugging
            console.log('DEBUG transportPerHari:', transportPerHari, typeof transportPerHari)
            console.log('DEBUG makanPerHari:', makanPerHari, typeof makanPerHari)
            console.log('DEBUG pulsa:', pulsa, typeof pulsa)
            console.log('DEBUG position:', position?.name)
            console.log('DEBUG hari masuk:', numericValue)
            console.log('DEBUG isAdmin/isKasir:', position?.name === 'Admin' || position?.name === 'Kasir')

            // Perhitungan tunjangan transport berdasarkan nilai
            // Gunakan Number() untuk memastikan nilai dibandingkan secara numerik
            if (Number(transportPerHari) === 5000 || Number(transportPerHari) === 10000) {
              updatedFormData.gaji.tunjangan.transport = Math.round(transportPerHari * numericValue)
            } else {
              updatedFormData.gaji.tunjangan.transport = transportPerHari
            }

            // Perhitungan tunjangan makan berdasarkan nilai
            if (Number(makanPerHari) === 5000 || Number(makanPerHari) === 10000) {
              updatedFormData.gaji.tunjangan.makan = Math.round(makanPerHari * numericValue)
            } else {
              updatedFormData.gaji.tunjangan.makan = makanPerHari
            }

            updatedFormData.gaji.tunjangan.pulsa = pulsa
          }

          // Jika jabatan adalah Terapis Kontrak
          if (position?.isKontrak) {
            const targetKontrak = position.targetKontrak || 0
            if (targetKontrak > 0) {
              // Hitung gaji pokok baru sesuai rumus (hanya jika yang diubah adalah hari masuk)
              if (lastKey === 'hariMasuk') {
                const gajiPokokBaru = (((updatedFormData.gaji.omzet * 100) / targetKontrak) * 0.01) * position.gajiPokok
                updatedFormData.gaji.pokok = Math.round(gajiPokokBaru)
              }

              // Tambahkan logika untuk menghilangkan potongan penyesuaian jika mencapai/melebihi target
              if (updatedFormData.gaji.omzet >= targetKontrak) {
                updatedFormData.gaji.potongan.penyesuaian = 0
              }
            }
          }

          // Hitung penyesuaian dengan state management
          setIsCalculatingAdjustment(true)
          const { tunjanganPenyesuaian, potonganPenyesuaian } = calculatePenyesuaian(updatedFormData, position)
          updatedFormData.gaji.tunjangan.penyesuaian = tunjanganPenyesuaian
          updatedFormData.gaji.potongan.penyesuaian = potonganPenyesuaian
          setIsCalculatingAdjustment(false)
        }
      }

      return updatedFormData
    })

    callback(numericValue)
  }

  // Fungsi untuk menghitung penyesuaian gaji dengan state management
  const calculatePenyesuaian = (formData: CreateSlipGajiDTO, position: Position | undefined) => {
    if (!position || position.isKontrak) {
      return { tunjanganPenyesuaian: 0, potonganPenyesuaian: 0 }
    }

    // Mencegah perhitungan bersamaan
    if (isCalculatingAdjustment) {
      console.log('⏳ Perhitungan penyesuaian sedang berlangsung, skip...')
      return { 
        tunjanganPenyesuaian: formData.gaji.tunjangan.penyesuaian || 0, 
        potonganPenyesuaian: formData.gaji.potongan.penyesuaian || 0 
      }
    }

    // Notifikasi mulai perhitungan
    console.log('🔄 Memulai perhitungan penyesuaian gaji...')

    const totalSebelumPenyesuaian =
      formData.gaji.pokok +
      formData.gaji.tunjangan.transport +
      formData.gaji.tunjangan.pulsa +
      formData.gaji.tunjangan.makan +
      formData.gaji.komisi +
      formData.gaji.bonus +
      formData.gaji.lembur

    const rasioOmzet = position.omsetPercentage || 0
    const targetGaji = Math.round((formData.gaji.omzet * rasioOmzet) / 100) || 0

    console.log(`🧮 Perhitungan penyesuaian untuk ${position.name}:`, {
      totalSebelumPenyesuaian,
      rasioOmzet,
      targetGaji,
      omzet: formData.gaji.omzet,
      isAutoFilled
    })

    if (formData.gaji.omzet === 0) {
      console.log('⚠️ Omzet 0, penyesuaian ditetapkan ke 0')
      return { tunjanganPenyesuaian: 0, potonganPenyesuaian: 0 }
    } else if (targetGaji > totalSebelumPenyesuaian) {
      const tunjanganPenyesuaian = targetGaji - totalSebelumPenyesuaian
      console.log(`✅ Tunjangan penyesuaian: ${formatNumber(tunjanganPenyesuaian)}`)
      console.log('✅ Perhitungan penyesuaian selesai')
      return { tunjanganPenyesuaian, potonganPenyesuaian: 0 }
    } else if (totalSebelumPenyesuaian > targetGaji) {
      const potonganPenyesuaian = totalSebelumPenyesuaian - targetGaji
      console.log(`✅ Potongan penyesuaian: ${formatNumber(potonganPenyesuaian)}`)
      console.log('✅ Perhitungan penyesuaian selesai')
      return { tunjanganPenyesuaian: 0, potonganPenyesuaian }
    } else {
      console.log(`✅ Tidak ada penyesuaian (target = total)`)
      console.log('✅ Perhitungan penyesuaian selesai')
      return { tunjanganPenyesuaian: 0, potonganPenyesuaian: 0 }
    }
  }

  // Fungsi untuk menghitung total potongan
  const calculateTotalPotongan = (data: CreateSlipGajiDTO) => {
    const kasbon = data.gaji.potongan.kasbon || 0
    const piutang = data.gaji.potongan.piutang || 0
    const bpjsTP = data.gaji.potongan.bpjsTP || 0
    const bpjsTK = data.gaji.potongan.bpjsTK || 0
    const lainnya = data.gaji.potongan.lainnya || 0
    const penyesuaian = data.gaji.potongan.penyesuaian || 0

    return kasbon + piutang + bpjsTP + bpjsTK + lainnya + penyesuaian
  }

  const resetEmployeeSearch = () => {
    setEmployeeSearch("")
    setSelectedEmployee(null)
    setIsEmployeeDropdownOpen(false)
  }

  // Handler untuk auto-fill data payroll berdasarkan nama terapis
  const handleAutoFillPayroll = async () => {
    if (!selectedEmployee) {
      toast.error("Silakan pilih karyawan terlebih dahulu")
      return
    }

    // Validasi custom date range
    if (useCustomDateRange && (!customStartDate || !customEndDate)) {
      toast.error("Silakan pilih tanggal mulai dan akhir")
      return
    }

    if (useCustomDateRange && customStartDate > customEndDate) {
      toast.error("Tanggal mulai tidak boleh lebih besar dari tanggal akhir")
      return
    }

    setIsAutoFilling(true)
    setAutoFillMessage("")

    try {
      console.log(`🚀 Mengambil data payroll untuk terapis: ${selectedEmployee.user.name}`)
      console.log(`📅 Periode: ${formData.periode.bulan}/${formData.periode.tahun}`)

      // Kirim custom date jika dipilih, atau gunakan periode bulan/tahun
      const autoFillResult = await autoFillTherapistData(
        selectedEmployee.user.name,
        useCustomDateRange ? undefined : formData.periode.bulan,
        useCustomDateRange ? undefined : formData.periode.tahun,
        useCustomDateRange ? customStartDate : undefined,
        useCustomDateRange ? customEndDate : undefined
      )

      console.log("📊 Data payroll yang diterima:", autoFillResult)

      if (autoFillResult.isFound) {
        // Set flag auto-fill aktif untuk mencegah override
        setIsAutoFilled(true)
        
        // Update form data dengan data dari API payroll
        setFormData(prev => ({
          ...prev,
          gaji: {
            ...prev.gaji,
            omzet: autoFillResult.totalOmzet,
            komisi: autoFillResult.totalKomisi,
            lembur: autoFillResult.totalLembur
          }
        }))

        setAutoFillMessage(`✅ ${autoFillResult.message}`)
        toast.success(`Data payroll berhasil diisi otomatis! 
        • Omzet: ${formatNumber(autoFillResult.totalOmzet)}
        • Komisi: ${formatNumber(autoFillResult.totalKomisi)}
        • Lembur: ${formatNumber(autoFillResult.totalLembur)}`)

        console.log("✅ Auto-fill berhasil:")
        console.log(`   • Omzet: ${formatNumber(autoFillResult.totalOmzet)}`)
        console.log(`   • Komisi: ${formatNumber(autoFillResult.totalKomisi)}`)
        console.log(`   • Lembur: ${formatNumber(autoFillResult.totalLembur)}`)
        console.log(`   • Total Transaksi: ${autoFillResult.totalTransaksi}`)
        console.log("🔒 Flag isAutoFilled set to true - mencegah override dari sistem komisi")
      } else {
        setAutoFillMessage(`⚠️ ${autoFillResult.message}`)
        toast(`⚠️ Data terapis tidak ditemukan di sistem payroll untuk periode ${formData.periode.bulan}/${formData.periode.tahun}`, {
          icon: '⚠️',
          style: {
            background: '#f59e0b',
            color: '#ffffff',
          }
        })
        
        console.log("⚠️ Data tidak ditemukan:")
        console.log(`   • Terapis: ${selectedEmployee.user.name}`)
        console.log(`   • Periode: ${formData.periode.bulan}/${formData.periode.tahun}`)
      }
    } catch (error) {
      console.error("❌ Error saat auto-fill:", error)
      const errorMessage = error instanceof Error ? error.message : "Terjadi kesalahan saat mengambil data payroll"
      setAutoFillMessage(`❌ ${errorMessage}`)
      toast.error(`Gagal mengambil data payroll: ${errorMessage}`)
    } finally {
      setIsAutoFilling(false)
    }
  }

  // Update showAutoFillButton saat employee atau periode berubah
  useEffect(() => {
    const shouldShow = !!(selectedEmployee && formData.periode.bulan && formData.periode.tahun)
    setShowAutoFillButton(shouldShow)
    
    if (shouldShow) {
      console.log(`🔄 Auto-fill tersedia untuk: ${selectedEmployee?.user.name} (${formData.periode.bulan}/${formData.periode.tahun})`)
    }
  }, [selectedEmployee, formData.periode.bulan, formData.periode.tahun])

  return (
    <div className="flex flex-col h-screen space-y-4 p-4 md:p-8">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h1 className="text-xl md:text-2xl font-bold text-base-content">Data Penggajian</h1>
          <p className="text-sm text-base-content/70">Kelola data gaji karyawan</p>
        </div>
        <button
          onClick={() => {
            setIsModalOpen(true)
            resetEmployeeSearch()
          }}
          className="btn btn-sm md:btn-md bg-gradient-to-r from-breaktime-primary to-breaktime-secondary text-primary-content hover:from-breaktime-primary/90 hover:to-breaktime-secondary/90 border-0"
        >
          <Plus className="h-4 w-4 md:h-5 md:w-5" />
          <span className="hidden md:inline">Tambah Data</span>
          <span className="md:hidden">Tambah</span>
        </button>
      </div>

      {/* Search */}
      <div className="w-full md:w-96">
        <div className="relative">
          <input
            type="text"
            placeholder="Cari karyawan atau jabatan..."
            className="input input-bordered w-full pr-12 text-sm md:text-base placeholder:text-base-content/50 text-base-content"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <button className="btn btn-ghost btn-sm absolute right-0 top-0 h-full px-3 hover:bg-transparent">
            <Search className="h-4 w-4 md:h-5 md:w-5 text-base-content/60" />
          </button>
        </div>
      </div>

      {/* Tabel dengan Container */}
      <div className="flex-1 overflow-hidden bg-base-100 rounded-lg shadow-sm">
        <div className="h-full overflow-auto">
          <table className="table table-zebra table-pin-rows">
            <thead className="text-base-content bg-base-200/50">
              <tr>
                <th className="text-base-content">No</th>
                <th className="text-base-content min-w-[200px]">Nama Karyawan</th>
                <th className="text-base-content min-w-[150px]">Jabatan</th>
                <th className="text-base-content min-w-[120px]">Periode</th>
                <th className="text-base-content min-w-[150px]">Gaji Pokok</th>
                <th className="text-base-content min-w-[180px]">Total Tunjangan</th>
                <th className="text-base-content min-w-[180px]">Total Potongan</th>
                <th className="text-base-content min-w-[150px]">Total Gaji</th>
                <th className="text-base-content min-w-[120px]">Status</th>
                <th className="text-base-content min-w-[120px]">Aksi</th>
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              <tr>
                  <td colSpan={10} className="text-center py-4">
                  <span className="loading loading-spinner loading-md text-primary"></span>
                </td>
              </tr>
              ) : currentData.length === 0 ? (
              <tr>
                  <td colSpan={10} className="text-center py-4 text-base-content">
                    Tidak ada data
                </td>
              </tr>
            ) : (
                currentData.map((slip, index) => (
                  <tr key={slip.id} className="text-base-content hover:bg-base-200">
                    <td>{index + 1}</td>
                    <td>{slip.employee?.user?.name}</td>
                    <td>{slip.employee?.position?.name}</td>
                    <td>{formatPeriode(slip.periode)}</td>
                    <td>{formatCurrency(slip.gaji.pokok)}</td>
                    <td>
                      <div className="flex items-center gap-1">
                        <span>{formatCurrency(
                          slip.gaji.tunjangan.transport +
                          slip.gaji.tunjangan.pulsa +
                          slip.gaji.tunjangan.makan +
                          slip.gaji.tunjangan.bpjs +
                          slip.gaji.tunjangan.penyesuaian +
                          slip.gaji.lembur +
                          slip.gaji.bonus +
                          slip.gaji.komisi
                        )}</span>
                        <div className="dropdown dropdown-hover dropdown-end">
                          <label tabIndex={0} className="btn btn-xs btn-circle btn-ghost text-info">
                            <AlertCircle className="h-4 w-4" />
                          </label>
                          <div tabIndex={0} className="card compact dropdown-content z-[100] shadow-lg bg-base-100 rounded-box w-64">
                            <div className="card-body p-4 text-sm space-y-1 text-base-content">
                              <p className="flex justify-between">
                                <span>Transport:</span>
                                <span>{formatCurrency(slip.gaji.tunjangan.transport)}</span>
                              </p>
                              <p className="flex justify-between">
                                <span>Pulsa:</span>
                                <span>{formatCurrency(slip.gaji.tunjangan.pulsa)}</span>
                              </p>
                              <p className="flex justify-between">
                                <span>Makan:</span>
                                <span>{formatCurrency(slip.gaji.tunjangan.makan)}</span>
                              </p>
                              <p className="flex justify-between">
                                <span>BPJS:</span>
                                <span>{formatCurrency(slip.gaji.tunjangan.bpjs)}</span>
                              </p>
                              <p className="flex justify-between">
                                <span>Penyesuaian:</span>
                                <span>{formatCurrency(slip.gaji.tunjangan.penyesuaian)}</span>
                              </p>
                              <p className="flex justify-between">
                                <span>Lembur:</span>
                                <span>{formatCurrency(slip.gaji.lembur)}</span>
                              </p>
                              <p className="flex justify-between">
                                <span>Bonus:</span>
                                <span>{formatCurrency(slip.gaji.bonus)}</span>
                              </p>
                    </div>
                    </div>
                      </div>
                    </div>
                  </td>
                  <td>
                      <div className="flex items-center gap-1">
                        <span>{formatCurrency(
                          slip.gaji.potongan.kasbon +
                          slip.gaji.potongan.piutang +
                          slip.gaji.potongan.bpjsTP +
                          slip.gaji.potongan.bpjsTK +
                        slip.gaji.potongan.lainnya +
                        slip.gaji.potongan.penyesuaian
                        )}</span>
                        <div className="dropdown dropdown-hover dropdown-end">
                          <label tabIndex={0} className="btn btn-xs btn-circle btn-ghost text-error">
                            <AlertCircle className="h-4 w-4" />
                          </label>
                          <div tabIndex={0} className="card compact dropdown-content z-[100] shadow-lg bg-base-100 rounded-box w-64">
                            <div className="card-body p-4 text-sm space-y-1 text-base-content">
                              <p className="flex justify-between">
                                <span>Kasbon:</span>
                                <span>{formatCurrency(slip.gaji.potongan.kasbon)}</span>
                              </p>
                              <p className="flex justify-between">
                                <span>Hutang:</span>
                                <span>{formatCurrency(slip.gaji.potongan.piutang)}</span>
                              </p>
                              <p className="flex justify-between">
                                <span>BPJS TP:</span>
                                <span>{formatCurrency(slip.gaji.potongan.bpjsTP)}</span>
                              </p>
                              <p className="flex justify-between">
                                <span>BPJS TK:</span>
                                <span>{formatCurrency(slip.gaji.potongan.bpjsTK)}</span>
                              </p>
                              <p className="flex justify-between">
                                <span>Lainnya:</span>
                                <span>{formatCurrency(slip.gaji.potongan.lainnya)}</span>
                              </p>
                            <p className="flex justify-between">
                              <span>Penyesuaian:</span>
                              <span>{formatCurrency(slip.gaji.potongan.penyesuaian)}</span>
                              </p>
                              {slip.gaji.potongan.keterangan && (
                                <p className="text-xs mt-2 text-base-content/80">
                                  Keterangan: {slip.gaji.potongan.keterangan}
                                </p>
                      )}
                    </div>
                          </div>
                        </div>
                    </div>
                  </td>
                    <td>{formatCurrency(slip.gaji.total)}</td>
                    <td>
                      <div className="badge badge-sm gap-1 font-medium"
                        style={{
                          backgroundColor: slip.status === "PAID" ? "rgb(var(--success) / 0.2)" :
                                        slip.status === "DRAFT" ? "rgb(var(--warning) / 0.2)" :
                                        "rgb(var(--error) / 0.2)",
                          color: slip.status === "PAID" ? "rgb(var(--success))" :
                                slip.status === "DRAFT" ? "rgb(var(--warning))" :
                                "rgb(var(--error))"
                        }}
                      >
                        {slip.status === "PAID" ? (
                          <CheckCircle className="h-3 w-3" />
                        ) : slip.status === "DRAFT" ? (
                          <AlertTriangle className="h-3 w-3" />
                        ) : (
                          <XCircle className="h-3 w-3" />
                        )}
                        {slip.status === "PAID" ? "Dibayar" :
                         slip.status === "DRAFT" ? "Draft" :
                         "Dibatalkan"}
                      </div>
                  </td>
                  <td>
                      <div className="flex gap-1">
                      <button
                          onClick={() => handleEdit(slip)}
                          className="btn btn-square btn-sm btn-ghost text-warning hover:bg-warning/20"
                      >
                        <Pencil className="h-4 w-4" />
                      </button>
                      <button
                          onClick={() => handleDeleteClick(slip)}
                          className="btn btn-square btn-sm btn-ghost text-error hover:bg-error/20"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                        {slip.status !== "PAID" && (
                        <button
                            onClick={() => handleSendToWhatsApp(slip)}
                            className="btn btn-square btn-sm btn-ghost text-success hover:bg-success/20"
                        >
                          <Send className="h-4 w-4" />
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
        </div>
      </div>

      {/* Pagination */}
      {!isLoading && filteredSlipGaji.length > 0 && (
        <div className="flex justify-center items-center gap-2 py-4">
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="btn btn-sm btn-ghost text-base-content"
          >
            «
          </button>

          {Array.from({ length: totalPages }, (_, i) => i + 1).map(page => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`btn btn-sm ${
                currentPage === page
                ? 'bg-gradient-to-r from-breaktime-primary to-breaktime-secondary text-primary-content hover:from-breaktime-primary/90 hover:to-breaktime-secondary/90 border-0'
                : 'btn-ghost text-base-content'
              }`}
            >
              {page}
            </button>
          ))}

          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === totalPages}
            className="btn btn-sm btn-ghost text-base-content"
          >
            »
          </button>
        </div>
      )}

      {/* Modal Form */}
      {isModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4">
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="modal-box w-full max-w-5xl bg-base-100 p-4 md:p-6 shadow-2xl max-h-[90vh] overflow-y-auto"
          >
            <div className="flex items-center justify-between mb-8">
              <h2 className="text-2xl font-bold bg-gradient-to-r from-breaktime-primary to-breaktime-secondary bg-clip-text text-transparent">
                {editingSlip ? "Edit Data Gaji" : "Tambah Data Gaji"}
              </h2>
              <button
                type="button"
                onClick={() => {
                  setIsModalOpen(false)
                  setEditingSlip(null)
                  resetForm()
                  resetEmployeeSearch()
                }}
                className="btn btn-circle btn-ghost btn-sm text-base-content"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <form onSubmit={handleSubmit} className="space-y-8">
              {/* Informasi Dasar */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div className="form-control w-full">
                  <label className="label">
                    <span className="label-text text-base-content font-medium">Karyawan</span>
                  </label>
                  <div className="relative" ref={dropdownRef}>
                    <input
                      type="text"
                      className="input input-bordered w-full text-base-content pr-10"
                      placeholder="Cari karyawan..."
                      value={employeeSearch}
                      onChange={(e) => {
                        setEmployeeSearch(e.target.value)
                        setIsEmployeeDropdownOpen(true)
                      }}
                      onFocus={() => setIsEmployeeDropdownOpen(true)}
                      readOnly={!!editingSlip}
                    />
                    {!editingSlip && (
                      <button
                        className="absolute right-3 top-1/2 -translate-y-1/2"
                        onClick={() => setIsEmployeeDropdownOpen(!isEmployeeDropdownOpen)}
                      >
                        <ChevronDown className="h-4 w-4 text-base-content/60" />
                      </button>
                    )}
                    {isEmployeeDropdownOpen && !editingSlip && (
                      <div className="absolute z-50 w-full mt-1 bg-base-100 border border-base-300 rounded-lg shadow-lg max-h-60 overflow-auto">
                        {filteredEmployees.length === 0 ? (
                          <div className="px-4 py-2 text-sm text-base-content/70">
                            Tidak ada karyawan ditemukan
                          </div>
                        ) : (
                          filteredEmployees.map(employee => (
                            <button
                              key={employee.id}
                              className="w-full px-4 py-2 text-left hover:bg-base-200 text-base-content"
                              onClick={() => handleEmployeeSelect(employee)}
                            >
                              {employee.user?.name} - {employee.position?.name}
                            </button>
                          ))
                        )}
                      </div>
                    )}
                  </div>
                </div>

                <div className="form-control w-full">
                  <label className="label">
                    <span className="label-text text-base-content font-medium">Periode</span>
                  </label>
                  <div className="join w-full">
                    <select
                      className="select select-bordered join-item w-1/2 text-base-content"
                      value={formData.periode.bulan}
                      onChange={(e) => handlePeriodeChange("bulan", parseInt(e.target.value))}
                    required
                    >
                      <option value="" className="text-base-content">Bulan</option>
                      {Array.from({ length: 12 }, (_, i) => (
                        <option key={i + 1} value={i + 1} className="text-base-content">
                          {new Date(0, i).toLocaleString('id-ID', { month: 'long' })}
                        </option>
                      ))}
                    </select>
                    <select
                      className="select select-bordered join-item w-1/2 text-base-content"
                      value={formData.periode.tahun}
                      onChange={(e) => handlePeriodeChange("tahun", parseInt(e.target.value))}
                    required
                    >
                      <option value="" className="text-base-content">Tahun</option>
                      {(() => {
                        const startYear = 2025;
                        const endYear = startYear + 5;
                        return Array.from(
                          { length: endYear - startYear + 1 },
                          (_, index) => startYear + index
                        ).map(year => (
                          <option key={year} value={year} className="text-base-content">
                            {year}
                          </option>
                        ));
                      })()}
                    </select>
                  </div>
                </div>

                <div className="form-control w-full">
                  <label className="label">
                    <span className="label-text text-base-content font-medium">Omzet</span>
                  </label>
                  <input
                    type="text"
                    className="input input-bordered w-full text-base-content"
                    value={formatNumber(formData.gaji.omzet)}
                    onChange={(e) => handleInputChange(e.target.value, 'omzet')}
                  />
                </div>

                <div className="form-control w-full">
                  <label className="label">
                    <span className="label-text text-base-content font-medium">
                      Rasio Omzet ({(employees.find(emp => emp.id === formData.employeeId)?.position?.omsetPercentage || 0).toFixed(2)}%)
                    </span>
                  </label>
                  <input
                    type="text"
                    className="input input-bordered w-full text-base-content bg-base-200"
                    value={formatNumber(
                      Math.round(
                        (formData.gaji.omzet *
                        (employees.find(emp => emp.id === formData.employeeId)?.position?.omsetPercentage || 0)) /
                        100
                      )
                    )}
                    readOnly
                  />
                </div>
              </div>

              {/* Auto-fill Payroll Section */}
              {showAutoFillButton && (
                <div className="bg-gradient-to-r from-primary/20 to-secondary/20 dark:from-primary/5 dark:to-secondary/5 p-4 rounded-lg border border-primary/30 dark:border-primary/30 shadow-sm dark:shadow-lg bg-base-100/50 dark:bg-base-200/20">
                  <div className="flex flex-col sm:flex-row items-start sm:items-center gap-3">
                    <div className="flex-1">
                      <h4 className="text-sm font-semibold text-gray-800 dark:text-base-content mb-1 flex items-center gap-2">
                        <span className="text-lg">🚀</span>
                        Auto-Fill Data Payroll
                      </h4>
                      <p className="text-xs text-gray-700 dark:text-base-content/80">
                        Isi otomatis data Omzet, Komisi, dan Lembur dari sistem Breaktime Dashboard
                      </p>

                      {/* Custom Date Range Section */}
                      <div className="mt-3">
                        <label className="flex items-center gap-2 mb-2">
                          <input
                            type="checkbox"
                            checked={useCustomDateRange}
                            onChange={(e) => {
                              setUseCustomDateRange(e.target.checked)
                              if (!e.target.checked) {
                                setCustomStartDate("")
                                setCustomEndDate("")
                              }
                            }}
                            className="checkbox checkbox-primary checkbox-sm"
                            aria-label="Gunakan tanggal custom untuk auto-fill"
                          />
                          <span className="text-xs font-medium text-gray-800 dark:text-base-content">
                            Gunakan Tanggal Custom
                          </span>
                        </label>

                        {useCustomDateRange && (
                          <motion.div
                            initial={{ opacity: 0, height: 0 }}
                            animate={{ opacity: 1, height: "auto" }}
                            exit={{ opacity: 0, height: 0 }}
                            className="grid grid-cols-1 sm:grid-cols-2 gap-2 mb-2"
                          >
                            <div>
                              <label className="block text-xs font-medium text-gray-800 dark:text-base-content mb-1">
                                Tanggal Mulai
                              </label>
                              <input
                                type="date"
                                value={customStartDate}
                                onChange={(e) => setCustomStartDate(e.target.value)}
                                className="input input-bordered input-xs w-full text-xs text-gray-900 dark:text-base-content bg-white dark:bg-base-300 border-gray-300 dark:border-base-content/20 focus:border-primary dark:focus:border-primary"
                                placeholder="YYYY-MM-DD"
                                aria-label="Pilih tanggal mulai periode"
                              />
                            </div>
                            <div>
                              <label className="block text-xs font-medium text-gray-800 dark:text-base-content mb-1">
                                Tanggal Akhir
                              </label>
                              <input
                                type="date"
                                value={customEndDate}
                                onChange={(e) => setCustomEndDate(e.target.value)}
                                className="input input-bordered input-xs w-full text-xs text-gray-900 dark:text-base-content bg-white dark:bg-base-300 border-gray-300 dark:border-base-content/20 focus:border-primary dark:focus:border-primary"
                                placeholder="YYYY-MM-DD"
                                aria-label="Pilih tanggal akhir periode"
                              />
                            </div>
                          </motion.div>
                        )}

                        <div className="text-xs text-gray-600 dark:text-base-content/70">
                          {useCustomDateRange
                            ? "📅 Sistem akan mengambil data dari tanggal yang Anda pilih"
                            : "🤖 Sistem akan otomatis menghitung periode 30-30 atau 31-29 berdasarkan bulan terpilih"
                          }
                        </div>
                      </div>

                      {autoFillMessage && (
                        <div className="mt-2 px-3 py-2 rounded-md bg-gray-100 dark:bg-base-300/20 border border-gray-300 dark:border-base-content/10">
                          <div className="text-xs text-gray-800 dark:text-base-content/90 font-medium">
                            {autoFillMessage.includes('✅') && (
                              <span className="text-success">
                                {autoFillMessage}
                              </span>
                            )}
                            {autoFillMessage.includes('⚠️') && (
                              <span className="text-warning">
                                {autoFillMessage}
                              </span>
                            )}
                            {autoFillMessage.includes('❌') && (
                              <span className="text-error">
                                {autoFillMessage}
                              </span>
                            )}
                            {!autoFillMessage.includes('✅') && !autoFillMessage.includes('⚠️') && !autoFillMessage.includes('❌') && (
                              <span className="text-info">
                                {autoFillMessage}
                              </span>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                    <button
                      type="button"
                      onClick={handleAutoFillPayroll}
                      disabled={isAutoFilling || !selectedEmployee}
                      className={`btn btn-sm min-w-[100px] ${
                        isAutoFilling 
                          ? 'bg-base-300 dark:bg-base-300/50 text-base-content/50 cursor-not-allowed border-0' 
                          : 'bg-gradient-to-r from-primary to-secondary hover:from-primary/90 hover:to-secondary/90 text-primary-content border-0 shadow-md hover:shadow-lg'
                      } transition-all duration-200`}
                    >
                      {isAutoFilling ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-2 border-current border-t-transparent"></div>
                          <span>Mengambil...</span>
                        </>
                      ) : (
                        <>
                          <DollarSign className="h-4 w-4" />
                          <span>Auto-Fill</span>
                        </>
                      )}
                    </button>
                  </div>
                </div>
              )}

              {/* Pendapatan */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 text-lg font-semibold text-base-content">
                  <CreditCard className="h-5 w-5" />
                  <h3>Pendapatan</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text text-base-content">Gaji Pokok</span>
                    </label>
                    <div className="join">
                      <span className="btn join-item bg-base-200 border-base-300 text-base-content hover:bg-base-300">Rp</span>
                    <input
                        type="text"
                        className="input input-bordered join-item w-full text-base-content placeholder:text-base-content/50"
                        value={formatNumber(formData.gaji.pokok)}
                        onChange={(e) => handleNumberInput(e.target.value, ['gaji', 'pokok'], (value) => {
                          setFormData(prev => ({
                        ...prev,
                        gaji: {
                          ...prev.gaji,
                              pokok: value
                        }
                          }))
                        })}
                        placeholder="Masukkan gaji pokok"
                      required
                    />
                  </div>
                  </div>

                  <div className="form-control">
                    <label className="label">
                      <span className="label-text font-medium text-base-content/70">Hari Masuk</span>
                    </label>
                    <div className="join">
                    <input
                      type="number"
                        className="input input-bordered join-item w-full text-base-content placeholder:text-base-content/50"
                        value={formData.gaji.hariMasuk === 0 ? "" : formData.gaji.hariMasuk}
                        onChange={(e) => handleNumberInput(e.target.value, ['gaji', 'hariMasuk'], (value) => {
                          const hariMasuk = Math.max(0, Math.min(31, value))
                          setFormData(prev => ({
                            ...prev,
                            gaji: {
                              ...prev.gaji,
                              hariMasuk
                            }
                          }))
                        })}
                      min="0"
                        max="31"
                        placeholder="Masukkan jumlah hari masuk"
                      />
                      <span className="btn join-item bg-base-200 border-base-300 text-base-content hover:bg-base-300">Hari</span>
                    </div>
                  </div>

                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text text-base-content">Transport</span>
                    </label>
                    <input
                      type="text"
                      className="input input-bordered w-full text-base-content"
                      value={formatNumber(formData.gaji.tunjangan.transport)}
                      onChange={(e) => handleNumberInput(
                        e.target.value,
                        ["gaji", "tunjangan", "transport"],
                        (value) => setFormData(prev => ({
                        ...prev,
                        gaji: {
                          ...prev.gaji,
                          tunjangan: {
                            ...prev.gaji.tunjangan,
                              transport: value
                          }
                        }
                        }))
                      )}
                      required
                    />
                  </div>
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text text-base-content">Tunjangan Pulsa</span>
                    </label>
                    <input
                      type="text"
                      className="input input-bordered w-full text-base-content"
                      value={formatNumber(formData.gaji.tunjangan.pulsa)}
                      onChange={(e) => handleNumberInput(
                        e.target.value,
                        ["gaji", "tunjangan", "pulsa"],
                        (value) => setFormData(prev => ({
                        ...prev,
                        gaji: {
                          ...prev.gaji,
                          tunjangan: {
                            ...prev.gaji.tunjangan,
                              pulsa: value
                          }
                        }
                        }))
                      )}
                      required
                    />
                  </div>
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text text-base-content">Tunjangan Makan</span>
                    </label>
                    <input
                      type="text"
                      className="input input-bordered w-full text-base-content"
                      value={formatNumber(formData.gaji.tunjangan.makan)}
                      onChange={(e) => handleNumberInput(
                        e.target.value,
                        ["gaji", "tunjangan", "makan"],
                        (value) => setFormData(prev => ({
                        ...prev,
                        gaji: {
                          ...prev.gaji,
                          tunjangan: {
                            ...prev.gaji.tunjangan,
                              makan: value
                          }
                        }
                        }))
                      )}
                      required
                    />
                  </div>
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text text-base-content">Komisi</span>
                    </label>
                    <input
                      type="text"
                      className="input input-bordered w-full text-base-content"
                      value={formatNumber(formData.gaji.komisi)}
                      onChange={(e) => handleNumberInput(
                        e.target.value,
                        ["gaji", "komisi"],
                        (value) => setFormData(prev => ({
                        ...prev,
                        gaji: {
                          ...prev.gaji,
                            komisi: value
                        }
                        }))
                      )}
                      required
                    />
                  </div>
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text text-base-content">Lembur</span>
                    </label>
                    <input
                      type="text"
                      className="input input-bordered w-full text-base-content"
                      value={formatNumber(formData.gaji.lembur)}
                      onChange={(e) => handleNumberInput(
                        e.target.value,
                        ["gaji", "lembur"],
                        (value) => setFormData(prev => ({
                        ...prev,
                        gaji: {
                          ...prev.gaji,
                            lembur: value
                        }
                        }))
                      )}
                      required
                    />
                  </div>
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text text-base-content">Bonus</span>
                    </label>
                    <input
                      type="text"
                      className="input input-bordered w-full text-base-content"
                      value={formatNumber(formData.gaji.bonus)}
                      onChange={(e) => handleNumberInput(
                        e.target.value,
                        ["gaji", "bonus"],
                        (value) => setFormData(prev => ({
                        ...prev,
                        gaji: {
                          ...prev.gaji,
                            bonus: value
                        }
                        }))
                      )}
                      required
                    />
                  </div>
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text text-base-content">BPJS</span>
                    </label>
                    <input
                      type="text"
                      className="input input-bordered w-full text-base-content"
                      value={formatNumber(formData.gaji.tunjangan.bpjs)}
                      onChange={(e) => handleNumberInput(
                        e.target.value,
                        ["gaji", "tunjangan", "bpjs"],
                        (value) => setFormData(prev => ({
                        ...prev,
                        gaji: {
                          ...prev.gaji,
                          tunjangan: {
                            ...prev.gaji.tunjangan,
                              bpjs: value
                          }
                        }
                        }))
                      )}
                      required
                    />
                  </div>
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text text-base-content flex items-center gap-2">
                        Penyesuaian
                        {isCalculatingAdjustment && (
                          <span className="loading loading-spinner loading-xs"></span>
                        )}
                      </span>
                    </label>
                    <input
                      type="text"
                      className={`input input-bordered w-full text-base-content ${
                        isCalculatingAdjustment ? 'bg-base-200 cursor-not-allowed' : ''
                      }`}
                      value={formatNumber(formData.gaji.tunjangan.penyesuaian)}
                      onChange={(e) => handleNumberInput(
                        e.target.value,
                        ["gaji", "tunjangan", "penyesuaian"],
                        (value) => setFormData(prev => ({
                        ...prev,
                        gaji: {
                          ...prev.gaji,
                          tunjangan: {
                            ...prev.gaji.tunjangan,
                              penyesuaian: value
                          }
                        }
                        }))
                      )}
                      disabled={isCalculatingAdjustment}
                      required
                    />
                  </div>
                </div>
              </div>

              {/* Potongan */}
              <div className="space-y-4">
                <div className="flex items-center gap-2 text-lg font-semibold text-base-content">
                  <MinusCircle className="h-5 w-5" />
                  <h3>Potongan</h3>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text text-base-content">Kasbon</span>
                    </label>
                    <input
                      type="text"
                      className="input input-bordered w-full text-base-content"
                      value={formatNumber(formData.gaji.potongan.kasbon)}
                      onChange={(e) => handleNumberInput(
                        e.target.value,
                        ["gaji", "potongan", "kasbon"],
                        (value) => setFormData(prev => ({
                        ...prev,
                        gaji: {
                          ...prev.gaji,
                          potongan: {
                            ...prev.gaji.potongan,
                              kasbon: value
                          }
                        }
                        }))
                      )}
                      required
                    />
                  </div>
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text text-base-content">Hutang</span>
                    </label>
                    <input
                      type="text"
                      className="input input-bordered w-full text-base-content"
                      value={formatNumber(formData.gaji.potongan.piutang)}
                      onChange={(e) => handleNumberInput(
                        e.target.value,
                        ["gaji", "potongan", "piutang"],
                        (value) => setFormData(prev => ({
                        ...prev,
                        gaji: {
                          ...prev.gaji,
                          potongan: {
                            ...prev.gaji.potongan,
                              piutang: value
                          }
                        }
                        }))
                      )}
                      required
                    />
                  </div>
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text text-base-content">BPJS TP</span>
                    </label>
                    <input
                      type="text"
                      className="input input-bordered w-full text-base-content"
                      value={formatNumber(formData.gaji.potongan.bpjsTP)}
                      onChange={(e) => handleNumberInput(
                        e.target.value,
                        ["gaji", "potongan", "bpjsTP"],
                        (value) => setFormData(prev => ({
                        ...prev,
                        gaji: {
                          ...prev.gaji,
                          potongan: {
                            ...prev.gaji.potongan,
                              bpjsTP: value
                          }
                        }
                        }))
                      )}
                      required
                    />
                  </div>
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text text-base-content">BPJS TK</span>
                    </label>
                    <input
                      type="text"
                      className="input input-bordered w-full text-base-content"
                      value={formatNumber(formData.gaji.potongan.bpjsTK)}
                      onChange={(e) => handleNumberInput(
                        e.target.value,
                        ["gaji", "potongan", "bpjsTK"],
                        (value) => setFormData(prev => ({
                        ...prev,
                        gaji: {
                          ...prev.gaji,
                          potongan: {
                            ...prev.gaji.potongan,
                              bpjsTK: value
                          }
                        }
                        }))
                      )}
                      required
                    />
                  </div>
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text text-base-content">Lainnya</span>
                    </label>
                    <input
                      type="text"
                      className="input input-bordered w-full text-base-content"
                      value={formatNumber(formData.gaji.potongan.lainnya)}
                      onChange={(e) => handleNumberInput(
                        e.target.value,
                        ["gaji", "potongan", "lainnya"],
                        (value) => setFormData(prev => ({
                        ...prev,
                        gaji: {
                          ...prev.gaji,
                          potongan: {
                            ...prev.gaji.potongan,
                              lainnya: value
                          }
                        }
                        }))
                      )}
                      required
                    />
                  </div>
                  <div className="form-control w-full">
                    <label className="label">
                      <span className="label-text text-base-content flex items-center gap-2">
                        Penyesuaian
                        {isCalculatingAdjustment && (
                          <span className="loading loading-spinner loading-xs"></span>
                        )}
                      </span>
                    </label>
                    <input
                      type="text"
                      className={`input input-bordered w-full text-base-content bg-base-200 ${
                        isCalculatingAdjustment ? 'animate-pulse' : ''
                      }`}
                      value={formatNumber(formData.gaji.potongan.penyesuaian)}
                      readOnly
                    />
                  </div>
                  <div className="form-control w-full md:col-span-2">
                    <label className="label">
                      <span className="label-text text-base-content">Keterangan Potongan</span>
                    </label>
                    <input
                      type="text"
                      className="input input-bordered w-full text-base-content"
                      value={formData.gaji.potongan.keterangan || ""}
                      onChange={(e) => setFormData(prev => ({
                        ...prev,
                        gaji: {
                          ...prev.gaji,
                          potongan: {
                            ...prev.gaji.potongan,
                            keterangan: e.target.value
                          }
                        }
                      }))}
                    />
                  </div>
                </div>
              </div>

              {/* Tombol Aksi */}
              <div className="modal-action pt-4 border-t border-base-200">
                <button
                  type="button"
                  onClick={() => {
                    setIsModalOpen(false)
                    setEditingSlip(null)
                    resetForm()
                    resetEmployeeSearch()
                  }}
                  className="btn btn-ghost text-base-content"
                >
                  Batal
                </button>
                <button
                  type="submit"
                  className="btn bg-gradient-to-r from-breaktime-primary to-breaktime-secondary text-primary-content hover:from-breaktime-primary/90 hover:to-breaktime-secondary/90 border-0"
                >
                  {editingSlip ? "Update" : "Simpan"}
                </button>
              </div>
            </form>
          </motion.div>
        </div>
      )}

      {/* Modal Konfirmasi Hapus */}
      {isDeleteModalOpen && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50 backdrop-blur-sm p-4">
          <motion.div
            initial={{ scale: 0.95, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            className="modal-box bg-base-100 w-full max-w-sm"
          >
            <h2 className="text-xl font-bold mb-4 text-base-content">Konfirmasi Hapus</h2>
            <p className="mb-4 text-base-content">Apakah Anda yakin ingin menghapus data gaji ini?</p>
            <div className="modal-action">
              <button
                onClick={() => {
                  setIsDeleteModalOpen(false)
                  setDeletingSlip(null)
                }}
                className="btn btn-ghost text-base-content"
              >
                Batal
              </button>
              <button
                onClick={handleDelete}
                className="btn bg-error text-error-content hover:bg-error/90 border-0"
              >
                Hapus
              </button>
            </div>
          </motion.div>
        </div>
      )}

      {/* Tambahkan error message di form */}
      {formErrors.employeeId && (
        <p className="text-red-500 text-sm mt-1">{formErrors.employeeId}</p>
      )}
      {formErrors.periode && (
        <p className="text-red-500 text-sm mt-1">{formErrors.periode}</p>
      )}
      {formErrors.gaji?.pokok && (
        <p className="text-red-500 text-sm mt-1">{formErrors.gaji.pokok}</p>
      )}
      {formErrors.gaji?.tunjangan?.transport && (
        <p className="text-red-500 text-sm mt-1">{formErrors.gaji.tunjangan.transport}</p>
      )}
      {formErrors.gaji?.potongan?.bpjsTP && (
        <p className="text-red-500 text-sm mt-1">{formErrors.gaji.potongan.bpjsTP}</p>
      )}
      {formErrors.gaji?.potongan?.bpjsTK && (
        <p className="text-red-500 text-sm mt-1">{formErrors.gaji.potongan.bpjsTK}</p>
      )}
    </div>
  )
}