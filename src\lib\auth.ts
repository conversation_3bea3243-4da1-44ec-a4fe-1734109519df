import { NextResponse } from "next/server"
import { jwtVerify } from "jose"
import { cookies } from "next/headers"

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key"

export async function verifyAuth() {
  try {
    const cookieStore = cookies()
    const token = cookieStore.get("auth_token")

    if (!token) {
      return {
        success: false,
        message: "Missing authentication token",
      }
    }

    const verified = await jwtVerify(
      token.value,
      new TextEncoder().encode(JWT_SECRET)
    )
    
    return {
      success: true,
      payload: verified.payload,
    }
  } catch (err) {
    return {
      success: false,
      message: "Invalid token",
    }
  }
}

export async function authMiddleware(request: Request) {
  const auth = await verifyAuth()

  if (!auth.success) {
    return NextResponse.json(
      { message: auth.message },
      { status: 401 }
    )
  }

  return auth.payload
}

export const authOptions = {
  providers: [], // Kosong karena menggunakan JWT custom
  secret: JWT_SECRET,
  session: {
    strategy: "jwt" as const,
  },
  callbacks: {
    async jwt({ token, user }: any) {
      if (user) {
        token.id = user.id
        token.role = user.role
      }
      return token
    },
    async session({ session, token }: any) {
      if (token) {
        session.user.id = token.id
        session.user.role = token.role
      }
      return session
    },
  },
} 