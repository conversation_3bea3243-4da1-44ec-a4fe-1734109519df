import { NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"

const prisma = new PrismaClient()

export async function PUT(request: Request, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const body = await request.json()
    const outlet = await prisma.outlet.update({
      where: {
        id: params.id
      },
      data: {
        name: body.name,
        code: body.code
      }
    })
    return NextResponse.json({
      success: true,
      message: "Berhasil mengupdate outlet",
      data: outlet
    })
  } catch (error) {
    console.error("Error updating outlet:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Gagal mengupdate outlet",
        error: error instanceof Error ? error.message : "Unknown error occurred"
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: Request, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    await prisma.outlet.delete({
      where: {
        id: params.id
      }
    })
    return NextResponse.json({
      success: true,
      message: "Berhasil menghapus outlet"
    })
  } catch (error) {
    console.error("Error deleting outlet:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Gagal menghapus outlet",
        error: error instanceof Error ? error.message : "Unknown error occurred"
      },
      { status: 500 }
    )
  }
} 