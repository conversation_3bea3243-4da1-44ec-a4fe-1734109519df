import Cookies from 'js-cookie'

export interface CookieOptions {
  httpOnly?: boolean
  secure?: boolean
  sameSite?: 'strict' | 'lax' | 'none'
  path?: string
  maxAge?: number
}

export function setCookie(name: string, value: string, options: CookieOptions = {}) {
  Cookies.set(name, value, {
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'strict',
    path: '/',
    expires: options.maxAge ? options.maxAge / (24 * 60 * 60 * 1000) : 1 // Convert maxAge from milliseconds to days
  })
}

export function getCookie(name: string): string | null {
  return Cookies.get(name) || null
}

export function deleteCookie(name: string) {
  Cookies.remove(name, { path: '/' })
} 