import { Employee } from "./employee"

export interface DailyCommission {
  id: string
  employeeId: string
  employee: Employee
  date: Date
  omzet: number
  commission: number
  notes: string | null
  createdAt: Date
  updatedAt: Date
}

export interface CreateDailyCommissionDTO {
  employeeId: string
  date: Date
  omzet: number
  commission: number
  notes?: string
}

export interface UpdateDailyCommissionDTO {
  id: string
  omzet?: number
  commission?: number
  notes?: string
}

export interface DailyCommissionResponse {
  success: boolean
  message: string
  data?: DailyCommission | DailyCommission[] | {
    totalOmzet: number
    totalCommission: number
    dailyCommissions: DailyCommission[]
    month: number
    year: number
  }
  totalOmzet?: number
  totalCommission?: number
  error?: string
}

export interface MonthlyCommissionSummary {
  totalOmzet: number
  totalCommission: number
  dailyCommissions: DailyCommission[]
  month: number
  year: number
} 