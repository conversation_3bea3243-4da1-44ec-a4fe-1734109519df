import { PrismaClient } from "@prisma/client"

const prisma = new PrismaClient()

const positions = [
  {
    name: "<PERSON><PERSON><PERSON>",
    omsetPercentage: 5.0,
    isActive: true
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    omsetPercentage: 3.0,
    isActive: true
  },
  {
    name: "Senior Terapis",
    omsetPercentage: 4.0,
    isActive: true
  },
  {
    name: "<PERSON><PERSON>",
    omsetPercentage: 2.0,
    isActive: true
  },
  {
    name: "O<PERSON>",
    omsetPercentage: 1.5,
    isActive: true
  },
  {
    name: "Security",
    omsetPercentage: 1.5,
    isActive: true
  }
]

async function main() {
  console.log("Start seeding positions...")
  
  for (const position of positions) {
    const existingPosition = await prisma.position.findFirst({
      where: { name: position.name }
    })

    if (!existingPosition) {
      await prisma.position.create({
        data: position
      })
      console.log(`Created position: ${position.name}`)
    } else {
      console.log(`Position ${position.name} already exists`)
    }
  }

  console.log("Seeding positions finished.")
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  }) 