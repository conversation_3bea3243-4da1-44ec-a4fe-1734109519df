import { PrismaClient } from '@prisma/client'
import * as fs from 'fs'
import * as path from 'path'

const prisma = new PrismaClient()

async function main() {
  try {
    // Baca file backup
    const backupPath = path.join(process.cwd(), 'backups', 'backup-2025-02-02T23-16-29-276Z.json')
    const backupData = JSON.parse(fs.readFileSync(backupPath, 'utf-8'))
    
    console.log('<PERSON><PERSON> proses restore backup...')

    // Hapus data yang ada
    console.log('Menghapus data yang ada...')
    await prisma.$transaction([
      prisma.dailyCommission.deleteMany(),
      prisma.slipGaji.deleteMany(),
      prisma.kasbon.deleteMany(),
      prisma.salary.deleteMany(),
      prisma.employee.deleteMany(),
      prisma.user.deleteMany(),
      prisma.position.deleteMany(),
      prisma.outlet.deleteMany()
    ])

    // Restore Outlets first
    console.log('<PERSON><PERSON><PERSON> restore outlets...')
    const outlets = [
      { 
        name: "SETIA BUDI", 
        code: "SB-001"
      },
      {
        name: "EMY SAELAN",
        code: "ES-002" 
      },
      {
        name: "MAKASSAR",
        code: "MKS-003"
      }
    ]

    const createdOutlets = []
    for (const outlet of outlets) {
      const createdOutlet = await prisma.outlet.create({
        data: outlet
      })
      createdOutlets.push(createdOutlet)
      console.log(`✓ Outlet ${outlet.name} dibuat`)
    }

    // Restore Positions
    console.log('Memulai restore positions...')
    for (const position of backupData.data.positions) {
      await prisma.position.create({
        data: {
          id: position.id,
          name: position.name,
          omsetPercentage: position.omsetPercentage,
          gajiPokok: position.gajiPokok,
          tunjangan: position.tunjangan,
          createdAt: new Date(position.createdAt),
          updatedAt: new Date(position.updatedAt)
        }
      })
      console.log(`✓ Position ${position.name} dipulihkan`)
    }

    // Restore Users
    console.log('Memulai restore users...')
    for (const user of backupData.data.users) {
      await prisma.user.create({
        data: {
          id: user.id,
          username: user.username,
          password: user.password,
          name: user.name,
          role: user.role,
          department: user.department || "Staff",
          position: user.position || "Staff",
          joinDate: new Date(user.joinDate || new Date()),
          createdAt: new Date(user.createdAt),
          updatedAt: new Date(user.updatedAt)
        }
      })
      console.log(`✓ User ${user.name} dipulihkan`)
    }

    // Restore Employees
    console.log('Memulai restore employees...')
    const usedPhoneNumbers = new Set()
    
    for (const employee of backupData.data.employees) {
      try {
        // Handle duplicate phone numbers
        let noTelp = employee.noTelp
        let counter = 1
        while (usedPhoneNumbers.has(noTelp)) {
          noTelp = `${employee.noTelp}-${counter}`
          counter++
        }
        usedPhoneNumbers.add(noTelp)

        // Assign outlet berdasarkan department
        let outletId = createdOutlets[0].id // Default ke outlet pertama
        if (employee.positionId.includes('mkt')) {
          outletId = createdOutlets[1].id // Marketing ke outlet kedua
        } else if (employee.positionId.includes('trp')) {
          // Bagi terapis secara merata ke 3 outlet
          const index = Math.floor(Math.random() * 3)
          outletId = createdOutlets[index].id
        }

        await prisma.employee.create({
          data: {
            id: employee.id,
            userId: employee.userId,
            positionId: employee.positionId,
            nik: employee.nik,
            alamat: employee.alamat,
            noTelp: noTelp,
            outletId: outletId,
            status: employee.status || 'ACTIVE',
            createdAt: new Date(employee.createdAt),
            updatedAt: new Date(employee.updatedAt)
          }
        })
        console.log(`✓ Employee ${employee.nik} dipulihkan`)
      } catch (error) {
        console.error(`Error saat memproses employee dengan NIK ${employee.nik}:`, error)
        continue
      }
    }

    // Restore Kasbon
    console.log('Memulai restore kasbon...')
    for (const kasbon of backupData.data.kasbons) {
      try {
        await prisma.kasbon.create({
          data: {
            id: kasbon.id,
            userId: kasbon.userId,
            amount: kasbon.amount,
            description: kasbon.description,
            status: kasbon.status,
            createdAt: new Date(kasbon.createdAt),
            updatedAt: new Date(kasbon.updatedAt)
          }
        })
        console.log(`✓ Kasbon untuk user ${kasbon.userId} dipulihkan`)
      } catch (error) {
        console.error(`Error saat memproses kasbon:`, error)
        continue
      }
    }

    // Restore Daily Commission
    console.log('Memulai restore daily commission...')
    for (const commission of backupData.data.dailyCommissions) {
      try {
        await prisma.dailyCommission.create({
          data: {
            id: commission.id,
            employeeId: commission.employeeId,
            date: new Date(commission.date),
            omzet: commission.omzet,
            commission: commission.total,
            notes: commission.notes || null,
            createdAt: new Date(commission.createdAt),
            updatedAt: new Date(commission.updatedAt)
          }
        })
        console.log(`✓ Commission untuk employee ${commission.employeeId} dipulihkan`)
      } catch (error) {
        console.error(`Error saat memproses commission:`, error)
        continue
      }
    }

    // Restore Slip Gaji
    console.log('Memulai restore slip gaji...')
    for (const slip of backupData.data.slipGajis) {
      try {
        await prisma.slipGaji.create({
          data: {
            id: slip.id,
            employeeId: slip.employeeId,
            periode: slip.periode,
            gaji: slip.gaji,
            status: slip.status,
            createdAt: new Date(slip.createdAt),
            updatedAt: new Date(slip.updatedAt)
          }
        })
        console.log(`✓ Slip gaji untuk employee ${slip.employeeId} dipulihkan`)
      } catch (error) {
        console.error(`Error saat memproses slip gaji:`, error)
        continue
      }
    }

    console.log('✅ Proses restore selesai!')
  } catch (error) {
    console.error('❌ Error:', error)
    throw error
  }
}

main()
  .catch((e) => {
    console.error('❌ Error:', e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  }) 