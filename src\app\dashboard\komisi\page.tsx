"use client"

import { useState, useEffect, useCallback } from "react"
import { motion } from "framer-motion"
import { Plus, Pencil, Trash2, Search, Calendar, DollarSign, ChevronDown, Download } from "lucide-react"
import { Employee } from "@/types/employee"
import { DailyCommission, MonthlyCommissionSummary } from "@/types/commission"
import { getEmployees } from "@/services/employee"
import { 
  getDailyCommissions, 
  getDailyCommissionsByEmployee,
  createDailyCommission,
  updateDailyCommission,
  deleteDailyCommission,
  formatCurrency,
  formatDate,
  getNamaBulan,
  calculateCommission
} from "@/services/commission"
import { toast } from "react-hot-toast"
import * as XLSX from 'xlsx'

export default function CommissionPage() {
  const [employees, setEmployees] = useState<Employee[]>([])
  const [commissions, setCommissions] = useState<DailyCommission[]>([])
  const [selectedEmployee, setSelectedEmployee] = useState<Employee | null>(null)
  const [monthlySummary, setMonthlySummary] = useState<MonthlyCommissionSummary | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingCommission, setEditingCommission] = useState<DailyCommission | null>(null)
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1)
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear())
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 50
  const [pageRange, setPageRange] = useState<number[]>([])
  const [formData, setFormData] = useState({
    employeeId: "",
    date: new Date().toISOString().split("T")[0],
    omzet: 0,
    commission: 0,
    notes: ""
  })
  const [employeeSearch, setEmployeeSearch] = useState("")
  const [isEmployeeDropdownOpen, setIsEmployeeDropdownOpen] = useState(false)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [deletingCommission, setDeletingCommission] = useState<DailyCommission | null>(null)

  // Fetch data
  useEffect(() => {
    fetchData()
  }, [])

  const fetchMonthlyData = useCallback(async () => {
    if (!selectedEmployee) return

    try {
      const data = await getDailyCommissionsByEmployee(
        selectedEmployee.id,
        selectedMonth,
        selectedYear
      )
      setMonthlySummary(data)
    } catch (error) {
      toast.error("Gagal mengambil data bulanan")
    }
  }, [selectedEmployee, selectedMonth, selectedYear])

  // Fetch monthly data when employee or period changes
  useEffect(() => {
    if (selectedEmployee) {
      fetchMonthlyData()
    }
  }, [selectedEmployee, fetchMonthlyData])

  const fetchData = async () => {
    try {
      const [employeesData, commissionsData] = await Promise.all([
        getEmployees(),
        getDailyCommissions()
      ])
      setEmployees(employeesData)
      setCommissions(commissionsData)
    } catch (error) {
      toast.error("Gagal mengambil data")
    } finally {
      setIsLoading(false)
    }
  }

  // Filter commissions based on search query
  const filteredCommissions = commissions.filter(commission =>
    commission.employee.user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    commission.employee.position.name.toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Calculate page range
  useEffect(() => {
    const totalPages = Math.ceil(filteredCommissions.length / itemsPerPage)
    let startPage = Math.max(1, currentPage - 2)
    let endPage = Math.min(totalPages, startPage + 4)
    
    if (endPage - startPage < 4) {
      startPage = Math.max(1, endPage - 4)
    }
    
    const range = []
    for (let i = startPage; i <= endPage; i++) {
      range.push(i)
    }
    setPageRange(range)
  }, [currentPage, filteredCommissions.length])

  // Get current page data
  const getCurrentPageData = () => {
    const startIndex = (currentPage - 1) * itemsPerPage
    const endIndex = startIndex + itemsPerPage
    return filteredCommissions.slice(startIndex, endIndex)
  }

  // Handle page change
  const handlePageChange = (page: number) => {
    setCurrentPage(page)
  }

  const resetForm = () => {
    setFormData({
      employeeId: "",
      date: new Date().toISOString().split("T")[0],
      omzet: 0,
      commission: 0,
      notes: ""
    })
    setEmployeeSearch("")
  }

  // Fungsi untuk format angka dengan titik
  const formatNumber = (value: string): string => {
    // Hapus semua karakter non-digit
    const number = value.replace(/\D/g, "")
    // Hapus leading zeros
    const cleanNumber = number.replace(/^0+/, "")
    // Format dengan titik
    return cleanNumber.replace(/\B(?=(\d{3})+(?!\d))/g, ".")
  }

  // Fungsi untuk parse string ke number
  const parseNumber = (value: string): number => {
    return parseInt(value.replace(/\./g, "")) || 0
  }

  // Handle input number
  const handleNumberInput = (value: string, field: "omzet" | "commission") => {
    const formattedValue = formatNumber(value)
    const numericValue = parseNumber(value)
    setFormData(prev => ({
      ...prev,
      [field]: numericValue
    }))
    return formattedValue
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      if (editingCommission) {
        await updateDailyCommission({
          id: editingCommission.id,
          omzet: formData.omzet,
          commission: formData.commission,
          notes: formData.notes
        })
        toast.success("Komisi berhasil diupdate")
      } else {
        // Validasi data
        if (!formData.employeeId || !formData.date || formData.omzet <= 0 || formData.commission <= 0) {
          toast.error("Mohon lengkapi semua data")
          return
        }

        await createDailyCommission({
          employeeId: formData.employeeId,
          date: new Date(formData.date),
          omzet: formData.omzet,
          commission: formData.commission,
          notes: formData.notes
        })
        toast.success("Komisi berhasil ditambahkan")
      }
      
      setIsModalOpen(false)
      setEditingCommission(null)
      resetForm()
      fetchData()
      if (selectedEmployee) {
        fetchMonthlyData()
      }
    } catch (error) {
      console.error("Error saving commission:", error)
      toast.error(error instanceof Error ? error.message : "Gagal menyimpan komisi")
    }
  }

  // Handle commission deletion
  const handleDelete = async (commission: DailyCommission) => {
    setDeletingCommission(commission)
    setIsDeleteModalOpen(true)
  }

  const confirmDelete = async () => {
    if (!deletingCommission) return

    try {
      await deleteDailyCommission(deletingCommission.id)
      toast.success("Komisi berhasil dihapus")
      fetchData()
      if (selectedEmployee) {
        fetchMonthlyData()
      }
    } catch (error) {
      toast.error("Gagal menghapus komisi")
    } finally {
      setIsDeleteModalOpen(false)
      setDeletingCommission(null)
    }
  }

  // Handle edit button click
  const handleEdit = (commission: DailyCommission) => {
    setEditingCommission(commission)
    setFormData({
      employeeId: commission.employeeId,
      date: new Date(commission.date).toISOString().split("T")[0],
      omzet: commission.omzet,
      commission: commission.commission,
      notes: commission.notes || ""
    })
    setIsModalOpen(true)
  }

  // Filter karyawan berdasarkan search
  const filteredEmployees = employees.filter(employee =>
    employee.user.name.toLowerCase().includes(employeeSearch.toLowerCase()) ||
    employee.position.name.toLowerCase().includes(employeeSearch.toLowerCase())
  )

  // Handle select karyawan dari dropdown
  const handleEmployeeSelect = (employee: Employee) => {
    setFormData({ ...formData, employeeId: employee.id })
    setEmployeeSearch(employee.user.name)
    setIsEmployeeDropdownOpen(false)
  }

  // Fungsi untuk mengunduh data komisi sebagai file Excel
  const handleDownloadExcel = () => {
    try {
      // Filter data berdasarkan karyawan dan periode jika dipilih
      let dataToExport = filteredCommissions
      if (selectedEmployee) {
        dataToExport = dataToExport.filter(commission => 
          commission.employeeId === selectedEmployee.id &&
          new Date(commission.date).getMonth() + 1 === selectedMonth &&
          new Date(commission.date).getFullYear() === selectedYear
        )
      }

      // Format data untuk Excel
      const excelData = dataToExport.map(commission => ({
        'Tanggal': formatDate(commission.date),
        'Nama Karyawan': commission.employee.user.name,
        'Jabatan': commission.employee.position.name,
        'Omzet': commission.omzet,
        'Komisi': commission.commission,
        'Keterangan': commission.notes || '-'
      }))

      // Buat workbook baru
      const wb = XLSX.utils.book_new()
      const ws = XLSX.utils.json_to_sheet(excelData)

      // Styling untuk header
      const headerStyle = {
        font: { bold: true },
        alignment: { horizontal: 'center' }
      }
      
      // Format angka untuk kolom omzet dan komisi
      const numFormat = '#,##0'
      for (let i = 0; i < excelData.length; i++) {
        const omzetCell = XLSX.utils.encode_cell({ r: i + 1, c: 3 }) // kolom D
        const komisiCell = XLSX.utils.encode_cell({ r: i + 1, c: 4 }) // kolom E
        ws[omzetCell].z = numFormat
        ws[komisiCell].z = numFormat
      }

      // Tambahkan worksheet ke workbook
      XLSX.utils.book_append_sheet(wb, ws, 'Data Komisi')

      // Generate nama file
      const fileName = selectedEmployee
        ? `Komisi_${selectedEmployee.user.name}_${getNamaBulan(selectedMonth)}_${selectedYear}.xlsx`
        : 'Data_Komisi.xlsx'

      // Unduh file
      XLSX.writeFile(wb, fileName)
      toast.success('Data berhasil diunduh')
    } catch (error) {
      console.error('Error downloading excel:', error)
      toast.error('Gagal mengunduh data')
    }
  }

  return (
    <div className="space-y-6">
      {/* Header with Download Button */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h2 className="text-2xl font-bold bg-gradient-to-r from-breaktime-primary to-breaktime-secondary bg-clip-text text-transparent">
          Manajemen Komisi
        </h2>
        <div className="flex gap-2">
          <button
            onClick={handleDownloadExcel}
            className="btn bg-success/20 text-success hover:bg-success/30 border-0"
          >
            <Download className="h-5 w-5" />
            Download Excel
          </button>
          <button
            onClick={() => {
              setEditingCommission(null)
              resetForm()
              setIsModalOpen(true)
            }}
            className="btn bg-gradient-to-r from-breaktime-primary to-breaktime-secondary text-white hover:from-breaktime-primary/90 hover:to-breaktime-secondary/90 border-0"
          >
            <Plus className="h-5 w-5" />
            Tambah Komisi
          </button>
        </div>
      </div>

      {/* Filter Section */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Search */}
        <div className="form-control">
          <div className="relative">
            <input
              type="text"
              placeholder="Cari karyawan atau jabatan..."
              className="input input-bordered w-full pr-12 placeholder:text-base-content/50 text-base-content"
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
            <button className="btn btn-ghost btn-sm absolute right-0 top-0 h-full px-3 hover:bg-transparent">
              <Search className="h-5 w-5 text-base-content/60" />
            </button>
          </div>
        </div>

        {/* Employee Select */}
        <div className="form-control">
          <select
            className="select select-bordered text-base-content"
            value={selectedEmployee?.id || ""}
            onChange={(e) => {
              const employee = employees.find(emp => emp.id === e.target.value)
              if (employee) handleEmployeeSelect(employee)
            }}
          >
            <option value="">Pilih Karyawan</option>
            {employees.map((employee) => (
              <option key={employee.id} value={employee.id}>
                {employee.user.name} - {employee.position.name}
              </option>
            ))}
          </select>
        </div>

        {/* Month Select */}
        <div className="form-control">
          <select
            className="select select-bordered text-base-content"
            value={selectedMonth}
            onChange={(e) => setSelectedMonth(parseInt(e.target.value))}
          >
            {Array.from({ length: 12 }, (_, i) => (
              <option key={i + 1} value={i + 1}>
                {getNamaBulan(i + 1)}
              </option>
            ))}
          </select>
        </div>

        {/* Year Select */}
        <div className="form-control">
          <select
            className="select select-bordered text-base-content"
            value={selectedYear}
            onChange={(e) => setSelectedYear(parseInt(e.target.value))}
          >
            {Array.from({ length: 5 }, (_, i) => {
              const year = new Date().getFullYear() - 2 + i
              return (
                <option key={year} value={year}>
                  {year}
                </option>
              )
            })}
          </select>
        </div>
      </div>

      {/* Monthly Summary */}
      {selectedEmployee && monthlySummary && (
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="stat bg-base-100 rounded-box shadow-sm border border-base-200">
            <div className="stat-title text-base-content/80">Total Omzet</div>
            <div className="stat-value text-primary">{formatCurrency(monthlySummary.totalOmzet)}</div>
            <div className="stat-desc text-base-content/60">
              {getNamaBulan(monthlySummary.month)} {monthlySummary.year}
            </div>
          </div>
          <div className="stat bg-base-100 rounded-box shadow-sm border border-base-200">
            <div className="stat-title text-base-content/80">Total Komisi</div>
            <div className="stat-value text-secondary">{formatCurrency(monthlySummary.totalCommission)}</div>
            <div className="stat-desc text-base-content/60">
              Total komisi bulan ini
            </div>
          </div>
          <div className="stat bg-base-100 rounded-box shadow-sm border border-base-200">
            <div className="stat-title text-base-content/80">Jumlah Transaksi</div>
            <div className="stat-value text-base-content">{monthlySummary.dailyCommissions.length}</div>
            <div className="stat-desc text-base-content/60">
              Transaksi bulan ini
            </div>
          </div>
        </div>
      )}

      {/* Commissions Table */}
      <div className="overflow-x-auto bg-base-100 rounded-box shadow-sm border border-base-200">
        <table className="table">
          <thead className="bg-base-200/50">
            <tr>
              <th className="font-semibold text-base-content">Tanggal</th>
              <th className="font-semibold text-base-content">Nama Karyawan</th>
              <th className="font-semibold text-base-content">Jabatan</th>
              <th className="font-semibold text-base-content">Omzet</th>
              <th className="font-semibold text-base-content">Komisi</th>
              <th className="font-semibold text-base-content">Keterangan</th>
              <th className="font-semibold text-base-content">Aksi</th>
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              <tr>
                <td colSpan={7} className="text-center">
                  <span className="loading loading-spinner loading-md text-primary"></span>
                </td>
              </tr>
            ) : filteredCommissions.length === 0 ? (
              <tr>
                <td colSpan={7} className="text-center text-base-content/70">
                  Tidak ada data komisi
                </td>
              </tr>
            ) : (
              getCurrentPageData().map((commission) => (
                <tr key={commission.id} className="hover:bg-base-200/30">
                  <td className="font-medium text-base-content">
                    {formatDate(commission.date)}
                  </td>
                  <td className="font-medium text-base-content">
                    {commission.employee.user.name}
                  </td>
                  <td className="font-medium text-base-content">
                    {commission.employee.position.name}
                  </td>
                  <td className="font-medium text-base-content">
                    {formatCurrency(commission.omzet)}
                  </td>
                  <td className="font-medium text-success">
                    {formatCurrency(commission.commission)}
                  </td>
                  <td className="text-base-content/70">
                    {commission.notes || "-"}
                  </td>
                  <td>
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleEdit(commission)}
                        className="btn btn-square btn-sm btn-ghost hover:bg-warning/20 text-warning"
                      >
                        <Pencil className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(commission)}
                        className="btn btn-square btn-sm btn-ghost hover:bg-error/20 text-error"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination */}
      {Math.ceil(filteredCommissions.length / itemsPerPage) > 1 && (
        <div className="flex justify-center items-center gap-1 mt-4">
          <button
            onClick={() => handlePageChange(1)}
            disabled={currentPage === 1}
            className="btn btn-sm btn-outline text-base-content px-2"
          >
            «
          </button>
          <button
            onClick={() => handlePageChange(currentPage - 1)}
            disabled={currentPage === 1}
            className="btn btn-sm btn-outline text-base-content px-2"
          >
            ‹
          </button>

          {pageRange.map((page) => (
            <button
              key={page}
              onClick={() => handlePageChange(page)}
              className={`btn btn-sm ${
                currentPage === page
                  ? "bg-gradient-to-r from-breaktime-primary to-breaktime-secondary text-white hover:from-breaktime-primary/90 hover:to-breaktime-secondary/90 border-0"
                  : "btn-outline text-base-content"
              }`}
            >
              {page}
            </button>
          ))}

          <button
            onClick={() => handlePageChange(currentPage + 1)}
            disabled={currentPage === Math.ceil(filteredCommissions.length / itemsPerPage)}
            className="btn btn-sm btn-outline text-base-content px-2"
          >
            ›
          </button>
          <button
            onClick={() => handlePageChange(Math.ceil(filteredCommissions.length / itemsPerPage))}
            disabled={currentPage === Math.ceil(filteredCommissions.length / itemsPerPage)}
            className="btn btn-sm btn-outline text-base-content px-2"
          >
            »
          </button>
        </div>
      )}

      {/* Modal Form */}
      {isModalOpen && (
        <div className="modal modal-open">
          <div className="modal-box">
            <h3 className="font-bold text-lg bg-gradient-to-r from-breaktime-primary to-breaktime-secondary bg-clip-text text-transparent">
              {editingCommission ? "Edit Komisi" : "Tambah Komisi Baru"}
            </h3>
            <form onSubmit={handleSubmit} className="space-y-4 mt-4">
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium text-base-content/80">Karyawan</span>
                </label>
                <div className="relative">
                  <input
                    type="text"
                    className="input input-bordered w-full text-base-content pr-10"
                    placeholder="Cari karyawan..."
                    value={employeeSearch}
                    onChange={(e) => {
                      setEmployeeSearch(e.target.value)
                      setIsEmployeeDropdownOpen(true)
                    }}
                    onFocus={() => setIsEmployeeDropdownOpen(true)}
                    disabled={!!editingCommission}
                    required
                  />
                  <button
                    type="button"
                    className="absolute right-2 top-1/2 -translate-y-1/2"
                    onClick={() => setIsEmployeeDropdownOpen(!isEmployeeDropdownOpen)}
                  >
                    <ChevronDown className={`h-5 w-5 text-base-content/70 transition-transform ${isEmployeeDropdownOpen ? 'rotate-180' : ''}`} />
                  </button>
                  {isEmployeeDropdownOpen && !editingCommission && (
                    <div className="absolute z-50 w-full mt-1 bg-base-100 rounded-lg shadow-lg border border-base-200 max-h-60 overflow-auto">
                      {filteredEmployees.length === 0 ? (
                        <div className="px-4 py-3 text-base-content/70">
                          Tidak ada karyawan ditemukan
                        </div>
                      ) : (
                        filteredEmployees.map((employee) => (
                          <div
                            key={employee.id}
                            className="px-4 py-3 hover:bg-base-200 cursor-pointer text-base-content"
                            onClick={() => handleEmployeeSelect(employee)}
                          >
                            <div className="font-medium">{employee.user.name}</div>
                            <div className="text-sm text-base-content/70">{employee.position.name}</div>
                          </div>
                        ))
                      )}
                    </div>
                  )}
                </div>
              </div>
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium text-base-content/80">Tanggal</span>
                </label>
                <input
                  type="date"
                  className="input input-bordered text-base-content"
                  value={formData.date}
                  onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                  disabled={!!editingCommission}
                  required
                />
              </div>
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium text-base-content/80">Omzet</span>
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-base-content/70">
                    Rp
                  </span>
                  <input
                    type="text"
                    inputMode="numeric"
                    className="input input-bordered pl-12 text-base-content"
                    value={formData.omzet === 0 ? "" : formatNumber(formData.omzet.toString())}
                    onChange={(e) => handleNumberInput(e.target.value, "omzet")}
                    placeholder="Masukkan jumlah omzet"
                    required
                  />
                </div>
              </div>
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium text-base-content/80">Komisi</span>
                </label>
                <div className="relative">
                  <span className="absolute left-3 top-1/2 -translate-y-1/2 text-base-content/70">
                    Rp
                  </span>
                  <input
                    type="text"
                    inputMode="numeric"
                    className="input input-bordered pl-12 text-base-content"
                    value={formData.commission === 0 ? "" : formatNumber(formData.commission.toString())}
                    onChange={(e) => handleNumberInput(e.target.value, "commission")}
                    placeholder="Masukkan jumlah komisi"
                    required
                  />
                </div>
              </div>
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium text-base-content/80">Keterangan</span>
                </label>
                <textarea
                  className="textarea textarea-bordered text-base-content placeholder:text-base-content/50"
                  value={formData.notes}
                  onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                  placeholder="Masukkan keterangan (opsional)"
                />
              </div>
              <div className="modal-action">
                <button 
                  type="button" 
                  className="btn bg-base-200 hover:bg-base-300 text-base-content border-base-300"
                  onClick={() => setIsModalOpen(false)}
                >
                  Batal
                </button>
                <button 
                  type="submit" 
                  className="btn bg-gradient-to-r from-breaktime-primary to-breaktime-secondary text-white hover:from-breaktime-primary/90 hover:to-breaktime-secondary/90 border-0"
                >
                  {editingCommission ? "Update" : "Simpan"}
                </button>
              </div>
            </form>
          </div>
          <div 
            className="modal-backdrop bg-base-content/20" 
            onClick={() => {
              setIsModalOpen(false)
              setIsEmployeeDropdownOpen(false)
            }}
          />
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {isDeleteModalOpen && deletingCommission && (
        <div className="modal modal-open">
          <div className="modal-box">
            <h3 className="font-bold text-lg text-error">Konfirmasi Hapus</h3>
            <p className="py-4 text-base-content">
              Apakah Anda yakin ingin menghapus data komisi untuk{" "}
              <span className="font-semibold">{deletingCommission.employee.user.name}</span> pada tanggal{" "}
              <span className="font-semibold">{formatDate(deletingCommission.date)}</span>?
            </p>
            <div className="modal-action">
              <button 
                className="btn bg-base-200 hover:bg-base-300 text-base-content border-base-300"
                onClick={() => {
                  setIsDeleteModalOpen(false)
                  setDeletingCommission(null)
                }}
              >
                Batal
              </button>
              <button 
                className="btn bg-error hover:bg-error/90 text-white border-0"
                onClick={confirmDelete}
              >
                Hapus
              </button>
            </div>
          </div>
          <div 
            className="modal-backdrop bg-base-content/20" 
            onClick={() => {
              setIsDeleteModalOpen(false)
              setDeletingCommission(null)
            }}
          />
        </div>
      )}
    </div>
  )
} 