export interface Position {
  id: string
  name: string
  omsetPercentage: number | null
  gajiPokok: number
  tunjangan?: {
    transport: number
    pulsa: number
    makan: number
  }
  isActive: boolean
  isKontrak: boolean
  targetKontrak: number
  createdAt: Date
  updatedAt: Date
}

export interface CreatePositionDTO {
  name: string
  omsetPercentage: number | null
  gajiPokok: number
  tunjangan: {
    transport: number
    pulsa: number
    makan: number
  }
  isKontrak: boolean
  targetKontrak: number
}

export interface UpdatePositionDTO {
  id: string
  name?: string
  omsetPercentage?: number | null
  gajiPokok?: number
  tunjangan?: {
    transport: number
    pulsa: number
    makan: number
  }
  isActive?: boolean
  isKontrak?: boolean
  targetKontrak?: number
}

export interface UpdatePositionStatusInput {
  id: string
  isActive: boolean
}

// Tipe untuk response dari API
export interface PositionResponse {
  success: boolean
  message: string
  data?: Position | Position[]
  error?: string
} 