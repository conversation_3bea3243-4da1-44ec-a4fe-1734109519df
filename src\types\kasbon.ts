import { Employee } from "./employee"

export type KasbonStatus = "PENDING" | "APPROVED" | "REJECTED" | "PAID"
export type KasbonType = "KASBON" | "PIUTANG"

export interface Kasbon {
  id: string
  employeeId: string
  employee: Employee
  amount: number
  type: KasbonType
  status: KasbonStatus
  notes: string
  approvedAt?: Date
  paidAt?: Date
  createdAt: Date
  updatedAt: Date
}

export interface CreateKasbonDTO {
  employeeId: string
  amount: number
  type: KasbonType
  notes: string
}

export interface UpdateKasbonDTO {
  id: string
  status: KasbonStatus
  notes?: string
}

export interface KasbonResponse {
  success: boolean
  message: string
  data?: Kasbon | Kasbon[]
} 