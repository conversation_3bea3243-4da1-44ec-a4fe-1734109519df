import { 
  TargetOmset, 
  CreateTargetOmsetDTO, 
  UpdateTargetOmsetDTO
} from "@/types/target-omset"

// Dummy data
let targets: TargetOmset[] = [
  {
    id: "1",
    periodStart: new Date("2024-01-01"),
    periodEnd: new Date("2024-01-31"),
    targetAmount: 100000000,
    actualAmount: 125000000,
    notes: "Target Januari 2024",
    isAchieved: true,
    createdAt: new Date(),
    updatedAt: new Date()
  },
  {
    id: "2",
    periodStart: new Date("2024-02-01"),
    periodEnd: new Date("2024-02-29"),
    targetAmount: 120000000,
    actualAmount: 95000000,
    notes: "Target Februari 2024",
    isAchieved: false,
    createdAt: new Date(),
    updatedAt: new Date()
  }
]

// Get all target omset
export async function getTargetOmset(): Promise<TargetOmset[]> {
  return targets.sort((a, b) => b.periodStart.getTime() - a.periodStart.getTime())
}

// Create new target omset
export async function createTargetOmset(data: CreateTargetOmsetDTO): Promise<TargetOmset> {
  const newTarget: TargetOmset = {
    id: (targets.length + 1).toString(),
    ...data,
    isAchieved: calculateIsAchieved(data.actualAmount, data.targetAmount),
    createdAt: new Date(),
    updatedAt: new Date()
  }

  targets.push(newTarget)
  return newTarget
}

// Update target omset
export async function updateTargetOmset(data: UpdateTargetOmsetDTO): Promise<TargetOmset> {
  const index = targets.findIndex(target => target.id === data.id)
  if (index === -1) throw new Error("Target omset tidak ditemukan")

  const updatedTarget: TargetOmset = {
    ...targets[index],
    ...data,
    isAchieved: calculateIsAchieved(data.actualAmount, data.targetAmount),
    updatedAt: new Date()
  }

  targets[index] = updatedTarget
  return updatedTarget
}

// Delete target omset
export async function deleteTargetOmset(id: string): Promise<void> {
  const index = targets.findIndex(target => target.id === id)
  if (index === -1) throw new Error("Target omset tidak ditemukan")

  targets = targets.filter(target => target.id !== id)
}

// Calculate achievement percentage
export function calculateAchievementPercentage(actual: number, target: number): number {
  if (target === 0) return 0
  return Math.round((actual / target) * 100)
}

// Calculate if target is achieved
export function calculateIsAchieved(actual: number, target: number): boolean {
  return actual >= target
}

// Format currency
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Format date range
export function formatDateRange(start: Date | string, end: Date | string): string {
  const months = [
    "Januari", "Februari", "Maret", "April", "Mei", "Juni",
    "Juli", "Agustus", "September", "Oktober", "November", "Desember"
  ]

  const formatDate = (date: Date | string) => {
    const d = new Date(date)
    const day = d.getDate()
    const month = months[d.getMonth()]
    const year = d.getFullYear()
    return `${day} ${month} ${year}`
  }

  return `${formatDate(start)} - ${formatDate(end)}`
} 