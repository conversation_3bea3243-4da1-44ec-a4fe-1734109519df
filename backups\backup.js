const { PrismaClient } = require('@prisma/client')
const fs = require('fs')
const path = require('path')

const prisma = new PrismaClient()

async function backup() {
  try {
    // Get all data from each table
    const [
      users,
      employees,
      positions,
      salaries,
      kasbons,
      slipGaji,
      dailyCommissions,
      outlets
    ] = await Promise.all([
      prisma.user.findMany(),
      prisma.employee.findMany(),
      prisma.position.findMany(),
      prisma.salary.findMany(),
      prisma.kasbon.findMany(),
      prisma.slipGaji.findMany(),
      prisma.dailyCommission.findMany(),
      prisma.outlet.findMany()
    ])

    // Create backup object
    const backup = {
      timestamp: new Date().toISOString(),
      data: {
        users,
        employees,
        positions,
        salaries,
        kasbons,
        slipGaji,
        dailyCommissions,
        outlets
      }
    }

    // Create filename with timestamp
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-')
    const filename = `backup_${timestamp}.json`
    const filepath = path.join(__dirname, filename)

    // Write to file
    fs.writeFileSync(filepath, JSON.stringify(backup, null, 2))
    console.log(`Backup created successfully: ${filename}`)

  } catch (error) {
    console.error('Backup failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

backup() 