import { NextResponse } from "next/server"
import { 
  getTargetOmset, 
  createTargetOmset, 
  updateTargetOmset, 
  deleteTargetOmset 
} from "@/services/target-omset"
import { CreateTargetOmsetDTO, UpdateTargetOmsetDTO } from "@/types/target-omset"

// GET /api/target-omset
export async function GET() {
  try {
    const targets = await getTargetOmset()
    return NextResponse.json({
      success: true,
      message: "Berhasil mengambil data target omset",
      data: targets
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "<PERSON>r<PERSON><PERSON> kesalahan",
    }, { status: 500 })
  }
}

// POST /api/target-omset
export async function POST(request: Request) {
  try {
    const data: CreateTargetOmsetDTO = await request.json()
    const target = await createTargetOmset({
      ...data,
      periodStart: new Date(data.periodStart),
      periodEnd: new Date(data.periodEnd)
    })
    return NextResponse.json({
      success: true,
      message: "Berhasil menambahkan target omset",
      data: target
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "Terjadi kesalahan",
    }, { status: 500 })
  }
}

// PUT /api/target-omset
export async function PUT(request: Request) {
  try {
    const data: UpdateTargetOmsetDTO = await request.json()
    const target = await updateTargetOmset({
      ...data,
      periodStart: new Date(data.periodStart),
      periodEnd: new Date(data.periodEnd)
    })
    return NextResponse.json({
      success: true,
      message: "Berhasil mengupdate target omset",
      data: target
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "Terjadi kesalahan",
    }, { status: 500 })
  }
}

// DELETE /api/target-omset
export async function DELETE(request: Request) {
  try {
    const { id } = await request.json()
    await deleteTargetOmset(id)
    return NextResponse.json({
      success: true,
      message: "Berhasil menghapus target omset"
    })
  } catch (error) {
    return NextResponse.json({
      success: false,
      message: error instanceof Error ? error.message : "Terjadi kesalahan",
    }, { status: 500 })
  }
} 