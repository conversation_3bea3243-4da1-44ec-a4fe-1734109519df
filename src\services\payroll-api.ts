// Service untuk integrasi dengan API Payroll BreakTime
// Untuk auto-fill data omzet, komisi, dan lembur terapis

// Types untuk response API Payroll
export interface PayrollSummaryResponse {
  success: boolean
  message: string
  data: {
    summary: PayrollTherapistSummary[]
    totalOmzetSemua: number
    totalKomisiSemua: number
    totalLemburSemua: number
    totalTerapisAktif: number
    periode: string
    filter?: {
      outlet?: string
      startDate: string
      endDate: string
    }
  }
}

export interface PayrollTherapistDetailResponse {
  success: boolean
  message: string
  data: {
    therapistId: string
    therapistName: string
    outletName: string
    isActive: boolean
    periode: string
    summary: {
      totalOmzet: number
      totalKomisi: number
      totalLembur: number
      totalLemburMinutes: number
      totalTransaksi: number
      averageOmzetPerTransaksi: number
      averageKomisiPerTransaksi: number
    }
    transactions: PayrollTransaction[]
    breakdown: {
      omzetPerHari: Record<string, number>
      komisiPerHari: Record<string, number>
      lemburPerHari: Record<string, number>
    }
  }
}

export interface PayrollTherapistSummary {
  therapistId: string
  therapistName: string
  outletName: string
  totalOmzet: number
  totalKomisi: number
  totalLembur: number
  totalLemburMinutes: number
  totalTransaksi: number
  periode: string
}

export interface PayrollTransaction {
  transactionId: string
  transactionDate: string
  customerName: string
  outletName: string
  serviceName: string
  totalAmount: number
  discountAmount: number
  additionalCharge: number
  omzetLayanan: number
  komisi: number
  overtimeMinutes: number
  overtimeAmount: number
}

export interface PayrollOutletsResponse {
  success: boolean
  message: string
  data: {
    outlets: PayrollOutletData[]
    grandTotal: {
      totalOmzetSemua: number
      totalKomisiSemua: number
      totalLemburSemua: number
      totalTransaksiSemua: number
      totalOutlet: number
    }
    periode: string
    filter: {
      startDate: string
      endDate: string
    }
  }
}

export interface PayrollOutletData {
  outletId: string
  outletName: string
  totalOmzet: number
  totalKomisi: number
  totalLembur: number
  totalTransaksi: number
  totalTerapisAktif: number
  averageOmzetPerTransaksi: number
  averageOmzetPerTerapis: number
  periode: string
  therapists: PayrollTherapistSummary[]
}

// Konfigurasi API
const PAYROLL_API_KEY = "c4a1c8fdcde5513a3feeb9b8462ada8909a4a54af4211ea11efaa87fa9eefb56"
const API_BASE_URL = typeof window !== 'undefined' ? window.location.origin : "http://localhost:3000"

// Headers untuk API requests
const getHeaders = () => ({
  "x-api-key": PAYROLL_API_KEY,
  "Content-Type": "application/json"
})

/**
 * Mengambil summary payroll untuk semua terapis
 * @param bulan - Bulan (1-12), optional
 * @param tahun - Tahun 4 digit, optional  
 * @param startDate - Tanggal mulai (YYYY-MM-DD), optional
 * @param endDate - Tanggal akhir (YYYY-MM-DD), optional
 * @param outletId - Filter outlet spesifik, optional
 */
export async function getPayrollSummary(params?: {
  bulan?: number
  tahun?: number
  startDate?: string
  endDate?: string
  outletId?: string
}): Promise<PayrollSummaryResponse> {
  try {
    const queryParams = new URLSearchParams()
    
    if (params?.bulan) queryParams.append('bulan', params.bulan.toString())
    if (params?.tahun) queryParams.append('tahun', params.tahun.toString())
    if (params?.startDate) queryParams.append('startDate', params.startDate)
    if (params?.endDate) queryParams.append('endDate', params.endDate)
    if (params?.outletId) queryParams.append('outletId', params.outletId)

    const url = `${API_BASE_URL}/api/payroll${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    
    const response = await fetch(url, {
      method: 'GET',
      headers: getHeaders()
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: PayrollSummaryResponse = await response.json()
    
    if (!data.success) {
      throw new Error(data.message || 'Gagal mengambil data payroll summary')
    }

    return data
  } catch (error) {
    console.error('Error fetching payroll summary:', error)
    throw new Error(error instanceof Error ? error.message : 'Gagal mengambil data payroll summary')
  }
}

/**
 * Mengambil detail payroll untuk terapis spesifik
 * @param therapistId - ID terapis (atau)
 * @param therapistName - Nama terapis
 * @param bulan - Bulan (1-12), optional
 * @param tahun - Tahun 4 digit, optional
 * @param startDate - Tanggal mulai (YYYY-MM-DD), optional
 * @param endDate - Tanggal akhir (YYYY-MM-DD), optional
 */
export async function getTherapistPayroll(params: {
  therapistId?: string
  therapistName?: string
  bulan?: number
  tahun?: number
  startDate?: string
  endDate?: string
}): Promise<PayrollTherapistDetailResponse> {
  try {
    if (!params.therapistId && !params.therapistName) {
      throw new Error('therapistId atau therapistName harus diisi')
    }

    const queryParams = new URLSearchParams()
    
    if (params.therapistId) queryParams.append('therapistId', params.therapistId)
    if (params.therapistName) queryParams.append('therapistName', params.therapistName)
    if (params.bulan) queryParams.append('bulan', params.bulan.toString())
    if (params.tahun) queryParams.append('tahun', params.tahun.toString())
    if (params.startDate) queryParams.append('startDate', params.startDate)
    if (params.endDate) queryParams.append('endDate', params.endDate)

    const url = `${API_BASE_URL}/api/payroll/therapist?${queryParams.toString()}`
    
    const response = await fetch(url, {
      method: 'GET',
      headers: getHeaders()
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: PayrollTherapistDetailResponse = await response.json()
    
    if (!data.success) {
      throw new Error(data.message || 'Gagal mengambil data detail terapis')
    }

    return data
  } catch (error) {
    console.error('Error fetching therapist payroll:', error)
    throw new Error(error instanceof Error ? error.message : 'Gagal mengambil data detail terapis')
  }
}

/**
 * Mengambil detail payroll terapis dari semua outlet
 * @param therapistName - Nama terapis
 * @param bulan - Bulan (1-12), optional
 * @param tahun - Tahun 4 digit, optional
 * @param startDate - Tanggal mulai (YYYY-MM-DD), optional
 * @param endDate - Tanggal akhir (YYYY-MM-DD), optional
 */
export async function getTherapistPayrollAllOutlets(params: {
  therapistName: string
  bulan?: number
  tahun?: number
  startDate?: string
  endDate?: string
}): Promise<PayrollTherapistDetailResponse> {
  try {
    if (!params.therapistName) {
      throw new Error('therapistName harus diisi')
    }

    const queryParams = new URLSearchParams()
    
    queryParams.append('therapistName', params.therapistName)
    if (params.bulan) queryParams.append('bulan', params.bulan.toString())
    if (params.tahun) queryParams.append('tahun', params.tahun.toString())
    if (params.startDate) queryParams.append('startDate', params.startDate)
    if (params.endDate) queryParams.append('endDate', params.endDate)

    const url = `${API_BASE_URL}/api/payroll/all-outlets?${queryParams.toString()}`
    
    console.log("🌐 Calling all-outlets API:", { url, params })
    
    const response = await fetch(url, {
      method: 'GET',
      headers: getHeaders()
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: PayrollTherapistDetailResponse = await response.json()
    
    if (!data.success) {
      throw new Error(data.message || 'Gagal mengambil data terapis dari semua outlet')
    }

    return data
  } catch (error) {
    console.error('Error fetching therapist payroll from all outlets:', error)
    throw new Error(error instanceof Error ? error.message : 'Gagal mengambil data terapis dari semua outlet')
  }
}

/**
 * Mengambil data payroll per outlet
 * @param bulan - Bulan (1-12), optional
 * @param tahun - Tahun 4 digit, optional
 * @param startDate - Tanggal mulai (YYYY-MM-DD), optional
 * @param endDate - Tanggal akhir (YYYY-MM-DD), optional
 */
export async function getPayrollOutlets(params?: {
  bulan?: number
  tahun?: number
  startDate?: string
  endDate?: string
}): Promise<PayrollOutletsResponse> {
  try {
    const queryParams = new URLSearchParams()
    
    if (params?.bulan) queryParams.append('bulan', params.bulan.toString())
    if (params?.tahun) queryParams.append('tahun', params.tahun.toString())
    if (params?.startDate) queryParams.append('startDate', params.startDate)
    if (params?.endDate) queryParams.append('endDate', params.endDate)

    const url = `${API_BASE_URL}/api/payroll/outlets${queryParams.toString() ? `?${queryParams.toString()}` : ''}`
    
    const response = await fetch(url, {
      method: 'GET',
      headers: getHeaders()
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data: PayrollOutletsResponse = await response.json()
    
    if (!data.success) {
      throw new Error(data.message || 'Gagal mengambil data payroll outlets')
    }

    return data
  } catch (error) {
    console.error('Error fetching payroll outlets:', error)
    throw new Error(error instanceof Error ? error.message : 'Gagal mengambil data payroll outlets')
  }
}

/**
 * Helper function untuk auto-fill data terapis berdasarkan nama
 * Menggunakan custom date range (30-30 atau 31-29 berdasarkan jumlah hari dalam bulan)
 * @param therapistName - Nama terapis
 * @param bulan - Bulan periode (optional, default bulan ini)
 * @param tahun - Tahun periode (optional, default tahun ini)
 * @param customStartDate - Tanggal mulai custom (YYYY-MM-DD), optional
 * @param customEndDate - Tanggal akhir custom (YYYY-MM-DD), optional
 */
export async function autoFillTherapistData(
  therapistName: string,
  bulan?: number,
  tahun?: number,
  customStartDate?: string,
  customEndDate?: string
): Promise<{
  totalOmzet: number
  totalKomisi: number
  totalLembur: number
  totalLemburMinutes: number
  totalTransaksi: number
  isFound: boolean
  message: string
}> {
  try {
    let startDate: string
    let endDate: string
    let periode: string

    // Prioritas: gunakan custom date jika tersedia, atau hitung dari bulan/tahun
    if (customStartDate && customEndDate) {
      startDate = customStartDate
      endDate = customEndDate
      periode = `${customStartDate} - ${customEndDate}`
      console.log("📅 Using custom date range:", { customStartDate, customEndDate })
    } else {
      // Gunakan bulan dan tahun sekarang jika tidak disediakan
      const currentDate = new Date()
      const targetBulan = bulan || (currentDate.getMonth() + 1)
      const targetTahun = tahun || currentDate.getFullYear()

      // Hitung custom date range berdasarkan aturan bisnis
      const dateRange = calculateCustomPayrollDateRange(targetBulan, targetTahun)
      startDate = dateRange.startDate
      endDate = dateRange.endDate
      periode = dateRange.periode
    }

    console.log("🔍 autoFillTherapistData called with date range:", {
      therapistName,
      bulan,
      tahun,
      customStartDate,
      customEndDate,
      startDate,
      endDate,
      periode,
      originalName: therapistName,
      trimmedName: therapistName.trim()
    })

    // Coba dulu endpoint all-outlets untuk mencari di semua outlet
    let response: PayrollTherapistDetailResponse
    
    try {
      console.log("🔍 Trying all-outlets endpoint first with date range...")
      response = await getTherapistPayrollAllOutlets({
        therapistName: therapistName.trim(),
        startDate,
        endDate
      })
    } catch (allOutletsError) {
      console.log("⚠️ All-outlets endpoint failed, trying specific outlet endpoint...")
      try {
        response = await getTherapistPayroll({
          therapistName: therapistName.trim(), // Trim whitespace
          startDate,
          endDate
        })
      } catch (specificError) {
        console.error("❌ Both endpoints failed:", { allOutletsError, specificError })
        throw specificError
      }
    }

    if (response && response.success && response.data) {
      return {
        totalOmzet: response.data.summary.totalOmzet,
        totalKomisi: response.data.summary.totalKomisi,
        totalLembur: response.data.summary.totalLembur,
        totalLemburMinutes: response.data.summary.totalLemburMinutes,
        totalTransaksi: response.data.summary.totalTransaksi,
        isFound: true,
        message: `Data terapis ${therapistName} berhasil ditemukan untuk periode ${response.data.periode}`
      }
    }

    return {
      totalOmzet: 0,
      totalKomisi: 0,
      totalLembur: 0,
      totalLemburMinutes: 0,
      totalTransaksi: 0,
      isFound: false,
      message: `Data terapis ${therapistName} tidak ditemukan untuk periode yang diminta`
    }
  } catch (error) {
    console.error('Error in autoFillTherapistData:', error)
    return {
      totalOmzet: 0,
      totalKomisi: 0,
      totalLembur: 0,
      totalLemburMinutes: 0,
      totalTransaksi: 0,
      isFound: false,
      message: error instanceof Error ? error.message : 'Terjadi kesalahan saat mengambil data terapis'
    }
  }
}

/**
 * Utility function untuk menghitung rentang tanggal custom payroll
 * Aturan: 
 * - Dari tanggal 30 bulan lalu sampai tanggal 30 bulan ini
 * - Jika ada tanggal 31 di bulan, maka dari tanggal 31 bulan lalu sampai tanggal 29 bulan ini
 * @param bulan - Bulan target (1-12)
 * @param tahun - Tahun target
 */
export function calculateCustomPayrollDateRange(bulan: number, tahun: number): {
  startDate: string
  endDate: string
  periode: string
} {
  // Validasi input
  if (bulan < 1 || bulan > 12) {
    throw new Error('Bulan harus antara 1-12')
  }

  // Hitung bulan dan tahun sebelumnya
  let prevMonth = bulan - 1
  let prevYear = tahun
  
  if (prevMonth === 0) {
    prevMonth = 12
    prevYear = tahun - 1
  }

  // Fungsi untuk mendapatkan jumlah hari dalam bulan
  const getDaysInMonth = (month: number, year: number) => {
    return new Date(year, month, 0).getDate()
  }

  const daysInCurrentMonth = getDaysInMonth(bulan, tahun)
  const daysInPrevMonth = getDaysInMonth(prevMonth, prevYear)

  let startDay: number
  let endDay: number

  // Tentukan tanggal berdasarkan aturan:
  // Jika ada tanggal 31 di salah satu bulan, gunakan 31-29
  // Jika tidak, gunakan 30-30
  if (daysInCurrentMonth === 31 || daysInPrevMonth === 31) {
    // Aturan: tanggal 31 bulan lalu sampai tanggal 29 bulan ini
    startDay = Math.min(31, daysInPrevMonth) // Gunakan 31 atau hari terakhir jika bulan hanya 28/29/30 hari
    endDay = Math.min(29, daysInCurrentMonth) // Gunakan 29 atau hari terakhir jika bulan hanya 28 hari
  } else {
    // Aturan: tanggal 30 bulan lalu sampai tanggal 30 bulan ini
    startDay = Math.min(30, daysInPrevMonth) // Gunakan 30 atau hari terakhir jika februari
    endDay = Math.min(30, daysInCurrentMonth) // Gunakan 30 atau hari terakhir jika februari
  }

  // Format tanggal ke YYYY-MM-DD
  const startDate = `${prevYear}-${prevMonth.toString().padStart(2, '0')}-${startDay.toString().padStart(2, '0')}`
  const endDate = `${tahun}-${bulan.toString().padStart(2, '0')}-${endDay.toString().padStart(2, '0')}`

  // Buat string periode yang lebih deskriptif
  const monthNames = [
    "Januari", "Februari", "Maret", "April", "Mei", "Juni",
    "Juli", "Agustus", "September", "Oktober", "November", "Desember"
  ]
  
  const periode = `${startDay} ${monthNames[prevMonth - 1]} ${prevYear} - ${endDay} ${monthNames[bulan - 1]} ${tahun}`

  console.log("📅 Custom date range calculated:", {
    input: { bulan, tahun },
    output: { startDate, endDate, periode },
    logic: {
      daysInCurrentMonth,
      daysInPrevMonth,
      startDay,
      endDay,
      rule: (daysInCurrentMonth === 31 || daysInPrevMonth === 31) ? "31-29" : "30-30"
    }
  })

  return {
    startDate,
    endDate,
    periode
  }
}

/**
 * Helper function untuk mencari terapis berdasarkan nama parsial
 * @param searchTerm - Term pencarian (bisa sebagian nama)
 * @param bulan - Bulan periode (optional)
 * @param tahun - Tahun periode (optional)
 */
export async function searchTherapists(
  searchTerm: string,
  bulan?: number,
  tahun?: number
): Promise<PayrollTherapistSummary[]> {
  try {
    const response = await getPayrollSummary({
      bulan,
      tahun
    })

    if (response.success && response.data.summary) {
      // Filter berdasarkan nama yang mengandung search term (case insensitive)
      return response.data.summary.filter(therapist =>
        therapist.therapistName.toLowerCase().includes(searchTerm.toLowerCase())
      )
    }

    return []
  } catch (error) {
    console.error('Error searching therapists:', error)
    return []
  }
} 