import { PrismaClient, SlipStatus } from "../generated/client"
const prisma = new PrismaClient()

async function main() {
  // Ambil data employee yang sudah ada
  const employees = await prisma.employee.findMany({
    include: {
      user: true,
      position: true
    }
  })

  if (employees.length === 0) {
    console.log("No employees found. Please seed employees first.")
    return
  }

  // Data slip gaji untuk setiap karyawan
  const slipGajiData = []
  
  for (const employee of employees) {
    // Buat slip gaji untuk 3 bulan terakhir
    for (let i = 0; i < 3; i++) {
      const date = new Date()
      date.setMonth(date.getMonth() - i)
      
      const gajiPokok = Math.floor(Math.random() * (10000000 - 5000000) + 5000000)
      const transport = 500000
      const pulsa = 100000
      const makan = 800000
      const bpjs = 200000
      const penyesuaian = Math.floor(Math.random() * 500000)
      const lembur = Math.floor(Math.random() * 1000000)
      const bonus = Math.floor(Math.random() * 2000000)
      const komisi = Math.floor(Math.random() * (employee.position.omsetPercentage * 10000000) / 100)
      
      const kasbon = Math.floor(Math.random() * 500000)
      const piutang = Math.floor(Math.random() * 300000)
      const bpjsTP = Math.floor(gajiPokok * 0.02)
      const bpjsTK = Math.floor(gajiPokok * 0.03)
      const lainnya = 0

      const totalTunjangan = transport + pulsa + makan + bpjs + penyesuaian + lembur + bonus + komisi
      const totalPotongan = kasbon + piutang + bpjsTP + bpjsTK + lainnya
      const totalGaji = gajiPokok + totalTunjangan - totalPotongan

      slipGajiData.push({
        employeeId: employee.id,
        periode: {
          bulan: date.getMonth() + 1,
          tahun: date.getFullYear()
        },
        gaji: {
          pokok: gajiPokok,
          tunjangan: {
            transport,
            pulsa,
            makan,
            bpjs,
            penyesuaian
          },
          lembur,
          bonus,
          komisi,
          potongan: {
            kasbon,
            piutang,
            bpjsTP,
            bpjsTK,
            lainnya,
            keterangan: ""
          },
          total: totalGaji
        },
        status: Math.random() > 0.3 ? SlipStatus.PAID : SlipStatus.DRAFT
      })
    }
  }

  // Hapus data slip gaji yang ada
  await prisma.slipGaji.deleteMany()

  // Insert data slip gaji baru
  for (const slip of slipGajiData) {
    await prisma.slipGaji.create({
      data: slip
    })
  }

  console.log(`Created ${slipGajiData.length} slip gaji records`)
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  }) 