export interface TargetOmset {
  id: string
  periodStart: Date
  periodEnd: Date
  targetAmount: number
  actualAmount: number
  notes: string
  isAchieved: boolean
  createdAt: Date
  updatedAt: Date
}

export interface CreateTargetOmsetDTO {
  periodStart: Date
  periodEnd: Date
  targetAmount: number
  actualAmount: number
  notes: string
}

export interface UpdateTargetOmsetDTO extends CreateTargetOmsetDTO {
  id: string
}

export interface TargetOmsetResponse {
  success: boolean
  message: string
  data?: TargetOmset | TargetOmset[]
} 