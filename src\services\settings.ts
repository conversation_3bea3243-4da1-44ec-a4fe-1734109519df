import { CompanySettings, UserSettings, UpdateCompanySettingsDTO, UpdateUserSettingsDTO } from "@/types/settings"

// Dummy data
let companySettings: CompanySettings = {
  id: "1",
  name: "Breaktime",
  email: "<EMAIL>",
  phone: "081234567890",
  address: "Jl. Setia <PERSON>i, Palu, Sulawesi Tengah 94111",
  logo: "/logo.png",
  createdAt: new Date(),
  updatedAt: new Date()
}

let userSettings: UserSettings = {
  id: "1",
  username: "admin",
  password: "hashed_password",
  createdAt: new Date(),
  updatedAt: new Date()
}

// Get company settings
export async function getCompanySettings(): Promise<CompanySettings> {
  return companySettings
}

// Update company settings
export async function updateCompanySettings(data: UpdateCompanySettingsDTO): Promise<CompanySettings> {
  companySettings = {
    ...companySettings,
    ...data,
    updatedAt: new Date()
  }
  return companySettings
}

// Get user settings
export async function getUserSettings(): Promise<Omit<UserSettings, "password">> {
  const { password, ...settings } = userSettings
  return settings
}

// Update user settings
export async function updateUserSettings(data: UpdateUserSettingsDTO): Promise<Omit<UserSettings, "password">> {
  // In real implementation, verify old password and hash new password
  if (data.oldPassword !== "admin") {
    throw new Error("Password lama tidak valid")
  }

  userSettings = {
    ...userSettings,
    username: data.username || userSettings.username,
    password: data.newPassword, // Should be hashed in real implementation
    updatedAt: new Date()
  }

  const { password, ...settings } = userSettings
  return settings
}

// Upload company logo
export async function uploadCompanyLogo(file: File): Promise<string> {
  // In real implementation, upload file to storage
  const logoUrl = "/logo.png"
  companySettings = {
    ...companySettings,
    logo: logoUrl,
    updatedAt: new Date()
  }
  return logoUrl
} 