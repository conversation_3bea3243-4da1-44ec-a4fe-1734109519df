import { Employee } from "./employee"

export enum SlipStatus {
  DRAFT = "DRAFT",
  PUBLISHED = "PUBLISHED",
  PAID = "PAID"
}

export interface SlipGaji {
  id: string
  employeeId: string
  employee: Employee
  periode: {
    bulan: number
    tahun: number
  }
  gaji: {
    pokok: number
    tunjangan: {
      transport: number
      pulsa: number
      makan: number
      bpjs: number
      penyesuaian: number
    }
    lembur: number
    bonus: number
    komisi: number
    omzet: number
    rasioOmzet: number
    hariMasuk: number
    potongan: {
      kasbon: number
      piutang: number
      bpjsTP: number
      bpjsTK: number
      lainnya: number
      penyesuaian: number
      keterangan: string
    }
    total: number
  }
  status: SlipStatus
  createdAt: Date
  updatedAt: Date
}

export interface CreateSlipGajiDTO {
  employeeId: string
  periode: {
    bulan: number
    tahun: number
  }
  gaji: {
    pokok: number
    tunjangan: {
      transport: number
      pulsa: number
      makan: number
      bpjs: number
      penyesuaian: number
    }
    lembur: number
    bonus: number
    komisi: number
    omzet: number
    rasioOmzet: number
    hariMasuk: number
    potongan: {
      kasbon: number
      piutang: number
      bpjsTP: number
      bpjsTK: number
      lainnya: number
      penyesuaian: number
      keterangan: string
    }
    total: number
  }
}

export interface UpdateSlipGajiDTO {
  id: string
  gaji?: {
    pokok: number
    tunjangan: {
      transport: number
      pulsa: number
      makan: number
      bpjs: number
      penyesuaian: number
    }
    lembur: number
    bonus: number
    komisi: number
    potongan: {
      kasbon: number
      piutang: number
      bpjsTP: number
      bpjsTK: number
      lainnya: number
      keterangan?: string
    }
    total: number
  }
  status?: SlipStatus
}

export interface SlipGajiResponse {
  success: boolean
  message: string
  data?: SlipGaji | SlipGaji[]
  error?: string
} 