import { Role } from "@/types/employee"

export interface LoginCredentials {
  username: string
  password: string
}

export interface AuthResponse {
  success: boolean
  message: string
  data?: {
    id: string
    username: string
    name: string
    role: Role
    department: string
    position: string
    joinDate: Date
  }
  error?: string
}

export interface User {
  id: string
  username: string
  name: string
  role: Role
  department: string
  position: string
  joinDate: string
  salary: {
    base: number
    allowances: number
    deductions: {
      bpjs: number
      tax: number
    }
  } | null
} 