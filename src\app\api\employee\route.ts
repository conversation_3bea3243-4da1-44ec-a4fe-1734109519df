import { NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"
import { CreateEmployeeInput } from "@/types/employee"
import { hash } from "bcrypt"
import { authMiddleware } from "@/lib/auth"

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  }
})

export async function GET(request: Request) {
  try {
    // Tambahkan autentikasi
    const auth = await authMiddleware(request)
    if (!auth) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    console.log("Fetching employees...")
    const employees = await prisma.employee.findMany({
      include: {
        user: {
          select: {
            id: true,
            name: true,
            username: true,
            department: true,
            position: true,
            joinDate: true
          }
        },
        position: {
          select: {
            id: true,
            name: true,
            omsetPercentage: true
          }
        }
      },
      where: {
        status: "ACTIVE"
      },
      orderBy: {
        user: {
          name: 'asc'
        }
      }
    })
    console.log("Employees fetched:", employees)

    return NextResponse.json({
      success: true,
      message: "Berhasil mengambil data karyawan",
      data: employees
    })
  } catch (error) {
    console.error("Detailed error in GET /api/employee:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Gagal mengambil data karyawan",
        error: error instanceof Error ? error.message : "Unknown error occurred"
      },
      { status: 500 }
    )
  }
}

export async function POST(request: Request) {
  try {
    // Tambahkan autentikasi
    const auth = await authMiddleware(request)
    if (!auth) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    const body = await request.json() as CreateEmployeeInput
    console.log("Creating employee with data:", body)
    
    // Generate username dari nama (lowercase dan hapus spasi)
    const username = body.name.toLowerCase().replace(/\s+/g, '')
    // Generate password default (kombinasi nama dan 4 digit random)
    const randomDigits = Math.floor(1000 + Math.random() * 9000)
    const defaultPassword = `${username}${randomDigits}`
    
    // Ambil data posisi untuk mendapatkan nama posisi
    const position = await prisma.position.findUnique({
      where: { id: body.positionId }
    })

    if (!position) {
      throw new Error("Posisi tidak ditemukan")
    }
    
    // Buat user baru terlebih dahulu
    const user = await prisma.user.create({
      data: {
        username: username,
        password: await hash(defaultPassword, 12),
        name: body.name,
        role: body.role || "EMPLOYEE",
        department: "Staff",
        position: position.name,
        joinDate: new Date(body.joinDate)
      }
    })
    
    // Kemudian buat employee dengan userId yang baru dibuat
    const employee = await prisma.employee.create({
      data: {
        userId: user.id,
        positionId: body.positionId,
        outletId: body.outletId,
        nik: username, // Gunakan username sebagai NIK sementara
        alamat: body.alamat,
        noTelp: body.noTelp,
        status: "ACTIVE"
      },
      include: {
        user: {
          select: {
            id: true,
            name: true,
            username: true,
            department: true,
            position: true,
            joinDate: true
          }
        },
        position: {
          select: {
            id: true,
            name: true,
            omsetPercentage: true
          }
        }
      }
    })
    console.log("Employee created:", employee)

    return NextResponse.json({
      success: true,
      message: "Berhasil menambahkan karyawan",
      data: employee
    })
  } catch (error) {
    console.error("Detailed error in POST /api/employee:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Gagal menambahkan karyawan",
        error: error instanceof Error ? error.message : "Unknown error occurred"
      },
      { status: 500 }
    )
  }
} 