"use client"

import { useState, useEffect } from "react"
import { 
  Plus, 
  Pencil, 
  Trash2, 
  Search, 
  Calendar,
  DollarSign,
  User,
  FileText,
  CheckCircle2,
  XCircle,
  AlertCircle,
  CircleDollarSign,
  Wallet,
  Download
} from "lucide-react"
import { toast } from "react-hot-toast"
import { Kasbon, KasbonType } from "@/types/kasbon"
import { Employee } from "@/types/employee"
import { 
  formatCurrency, 
  formatDate,
  getStatusColor,
  getStatusLabel,
  getTypeLabel,
  generateExcelKasbon
} from "@/services/kasbon"

export default function KasbonPage() {
  const [kasbons, setKasbons] = useState<Kasbon[]>([])
  const [employees, setEmployees] = useState<Employee[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [deletingKasbon, setDeletingKasbon] = useState<string | null>(null)
  const [editingKasbon, setEditingKasbon] = useState<Kasbon | null>(null)
  const [formData, setFormData] = useState({
    employeeId: "",
    amount: 0,
    type: "KASBON" as KasbonType,
    notes: ""
  })
  const [searchEmployee, setSearchEmployee] = useState("")
  const [isDropdownOpen, setIsDropdownOpen] = useState(false)
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth() + 1)
  const [selectedYear, setSelectedYear] = useState(2025)

  // Fetch data
  const fetchData = async () => {
    try {
      setIsLoading(true)
      
      // Buat tanggal awal dan akhir bulan dengan timezone yang tepat
      const startDate = new Date(Date.UTC(selectedYear, selectedMonth - 1, 1, 0, 0, 0))
      const endDate = new Date(Date.UTC(selectedYear, selectedMonth, 0, 23, 59, 59))
      
      // Log debugging
      console.log("Fetching kasbon data with date range:", { 
        startDate: startDate.toISOString(), 
        endDate: endDate.toISOString(),
        selectedMonth,
        selectedYear
      })
      
      // Fetch kasbon data dengan parameter yang benar
      const kasbonResponse = await fetch(`/api/kasbon?startDate=${startDate.toISOString()}&endDate=${endDate.toISOString()}`, {
        method: "GET",
        headers: {
          "Accept": "application/json"
        },
        cache: "no-store",
        next: { revalidate: 0 }
      })
      
      if (!kasbonResponse.ok) {
        const errorText = await kasbonResponse.text()
        console.error("Kasbon API error response:", errorText)
        throw new Error(`HTTP error! status: ${kasbonResponse.status}, message: ${errorText}`)
      }
      
      const kasbonData = await kasbonResponse.json()
      console.log("Kasbon data received:", kasbonData) // Log tambahan
      
      // Pastikan data yang diterima adalah array
      if (Array.isArray(kasbonData)) {
        setKasbons(kasbonData)
      } else if (kasbonData.data && Array.isArray(kasbonData.data)) {
        setKasbons(kasbonData.data)
      } else {
        console.error("Unexpected data format from API:", kasbonData)
        setKasbons([])
        toast.error("Format data tidak valid")
      }

      // Fetch employee data
      const employeeResponse = await fetch("/api/employee", {
        method: "GET",
        headers: {
          "Accept": "application/json"
        },
        cache: "no-store",
        next: { revalidate: 0 }
      })
      
      if (!employeeResponse.ok) {
        const errorText = await employeeResponse.text()
        console.error("Employee API error response:", errorText)
        throw new Error(`HTTP error! status: ${employeeResponse.status}, message: ${errorText}`)
      }
      
      const employeeData = await employeeResponse.json()
      
      // Pastikan data karyawan valid
      if (employeeData.data && Array.isArray(employeeData.data)) {
        setEmployees(employeeData.data)
      } else {
        console.error("Unexpected employee data format:", employeeData)
        setEmployees([])
        toast.error("Format data karyawan tidak valid")
      }
    } catch (error) {
      console.error("Error fetching data:", error)
      toast.error("Gagal mengambil data: " + (error instanceof Error ? error.message : String(error)))
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    const loadData = async () => {
      try {
        console.log("Loading kasbon data for:", { month: selectedMonth, year: selectedYear })
        await fetchData()
      } catch (error) {
        console.error("Error in useEffect:", error)
      }
    }
    loadData()
  }, [selectedMonth, selectedYear])

  // Filter kasbons based on search query
  const filteredKasbons = kasbons.filter(kasbon =>
    kasbon.user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    formatCurrency(kasbon.amount).includes(searchQuery) ||
    kasbon.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
    getTypeLabel(kasbon.type).toLowerCase().includes(searchQuery.toLowerCase()) ||
    getStatusLabel(kasbon.status).toLowerCase().includes(searchQuery.toLowerCase())
  )

  // Filter employees berdasarkan pencarian
  const filteredEmployees = employees.filter(employee =>
    employee.user.name.toLowerCase().includes(searchEmployee.toLowerCase())
  )

  // Format number dengan titik
  const formatNumber = (value: string) => {
    // Hapus semua karakter non-digit
    const number = value.replace(/\D/g, '')
    // Format dengan titik
    return number.replace(/\B(?=(\d{3})+(?!\d))/g, '.')
  }

  // Handle number input change
  const handleNumberChange = (value: string) => {
    const formattedValue = formatNumber(value)
    setFormData({
      ...formData,
      amount: parseInt(value.replace(/\D/g, '') || '0')
    })
    return formattedValue
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      // Validate form data
      if (!formData.employeeId) {
        toast.error("Pilih karyawan terlebih dahulu")
        return
      }
      if (!formData.amount || formData.amount <= 0) {
        toast.error("Nominal harus lebih dari 0")
        return
      }
      if (!formData.notes.trim()) {
        toast.error("Keterangan harus diisi")
        return
      }

      // Prepare data
      const payload = {
        employeeId: formData.employeeId,
        amount: Number(formData.amount),
        type: formData.type,
        notes: formData.notes.trim()
      }
      
      console.log("Sending kasbon payload:", payload) // Untuk debugging
      
      // Tampilkan loading toast
      const loadingToast = toast.loading("Menyimpan data...")
      
      const response = await fetch("/api/kasbon", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Accept": "application/json"
        },
        credentials: "include",
        body: JSON.stringify(payload)
      })

      const responseText = await response.text()
      
      try {
        const result = responseText.length ? JSON.parse(responseText) : {}
        console.log("Server response for kasbon:", result) // Untuk debugging
        
        // Dismiss loading toast
        toast.dismiss(loadingToast)
        
        if (!response.ok) {
          const errorMessage = result.message || result.error || "Gagal menyimpan data"
          console.error("Server error details:", result.details || errorMessage)
          throw new Error(errorMessage)
        }

        toast.success(result.message || "Data kasbon berhasil disimpan")
        setIsModalOpen(false)
        setFormData({
          employeeId: "",
          amount: 0,
          type: "KASBON",
          notes: ""
        })
        setSearchEmployee("")
        setIsDropdownOpen(false)
        
        // Re-fetch data setelah submit berhasil
        await fetchData()
      } catch (parseError) {
        // Dismiss loading toast
        toast.dismiss(loadingToast)
        console.error("Error parsing response:", parseError, "Raw response:", responseText)
        toast.error("Terjadi kesalahan saat memproses respons server")
      }
    } catch (error) {
      console.error("Error submitting kasbon form:", error)
      const errorMessage = error instanceof Error ? error.message : "Terjadi kesalahan saat menyimpan data"
      toast.error(errorMessage)
    }
  }

  // Handle status update
  const handleUpdateStatus = async (id: string, status: string) => {
    try {
      console.log(`Updating kasbon status: ID=${id}, Status=${status}`)
      
      // Tampilkan loading toast
      const loadingToast = toast.loading(`${status === 'APPROVED' ? 'Menyetujui' : 'Menolak'} kasbon...`)
      
      const response = await fetch(`/api/kasbon/${id}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ status })
      })
      
      const responseText = await response.text()
      
      try {
        // Parse response jika ada content
        const result = responseText.length ? JSON.parse(responseText) : {}
        
        // Dismiss loading toast
        toast.dismiss(loadingToast)
        
        console.log(`Status update response for kasbon ID=${id}:`, result)
        
        if (!response.ok) {
          const errorMessage = result.message || result.error || `Gagal mengubah status kasbon`
          console.error(`Error updating status:`, result)
          throw new Error(errorMessage)
        }
        
        // Sukses, tampilkan pesan sukses
        toast.success(
          status === 'APPROVED' 
            ? 'Kasbon berhasil disetujui'
            : status === 'REJECTED'
              ? 'Kasbon berhasil ditolak'
              : 'Status kasbon berhasil diubah'
        )
        
        // Re-fetch data setelah update berhasil
        await fetchData()
      } catch (parseError) {
        // Dismiss loading toast
        toast.dismiss(loadingToast)
        console.error("Error parsing response:", parseError, "Raw response:", responseText)
        toast.error("Terjadi kesalahan saat memproses respons server")
      }
    } catch (error) {
      console.error(`Error updating kasbon status:`, error)
      const errorMessage = error instanceof Error ? error.message : "Terjadi kesalahan"
      toast.error(errorMessage)
    }
  }

  // Fungsi untuk menangani klik tombol delete
  const handleDelete = async (id: string) => {
    if (!id) {
      toast.error("ID kasbon tidak valid")
      return
    }
    
    console.log(`Preparing to delete kasbon with ID: ${id}`)
    
    // Cari data kasbon yang akan dihapus
    const kasbonToDelete = kasbons.find(k => k.id === id)
    if (!kasbonToDelete) {
      toast.error("Data kasbon tidak ditemukan")
      return
    }
    
    // Set deletingKasbon dan buka modal konfirmasi
    setDeletingKasbon(id)
    setIsDeleteModalOpen(true)
  }

  // Fungsi untuk mengkonfirmasi penghapusan kasbon
  const handleConfirmDelete = async () => {
    if (!deletingKasbon) {
      toast.error("ID kasbon tidak valid")
      setIsDeleteModalOpen(false)
      return
    }
    
    try {
      console.log(`Deleting kasbon with ID: ${deletingKasbon}`)
      
      // Tampilkan loading toast
      const loadingToast = toast.loading("Menghapus data kasbon...")
      
      const response = await fetch(`/api/kasbon/${deletingKasbon}`, {
        method: 'DELETE',
        headers: {
          'Accept': 'application/json'
        }
      })
      
      const responseText = await response.text()
      
      try {
        // Parse response jika ada content
        const result = responseText.length ? JSON.parse(responseText) : {}
        
        // Dismiss loading toast
        toast.dismiss(loadingToast)
        
        console.log(`Delete response for kasbon ID=${deletingKasbon}:`, result)
        
        if (!response.ok) {
          const errorMessage = result.message || result.error || "Gagal menghapus kasbon"
          console.error(`Error deleting kasbon:`, result)
          throw new Error(errorMessage)
        }
        
        // Sukses, tampilkan pesan sukses
        toast.success(result.message || "Kasbon berhasil dihapus")
        
        // Tutup modal konfirmasi
        setIsDeleteModalOpen(false)
        setDeletingKasbon(null)
        
        // Re-fetch data setelah delete berhasil
        await fetchData()
      } catch (parseError) {
        // Dismiss loading toast
        toast.dismiss(loadingToast)
        console.error("Error parsing response:", parseError, "Raw response:", responseText)
        toast.error("Terjadi kesalahan saat memproses respons server")
      }
    } catch (error) {
      console.error(`Error deleting kasbon:`, error)
      const errorMessage = error instanceof Error ? error.message : "Terjadi kesalahan saat menghapus data"
      toast.error(errorMessage)
    }
  }

  // Handle Excel download
  const handleDownloadExcel = async () => {
    try {
      const startDate = new Date(selectedYear, selectedMonth - 1, 1)
      const endDate = new Date(selectedYear, selectedMonth, 0)
      
      const excelBlob = await generateExcelKasbon(kasbons, startDate, endDate)
      const url = URL.createObjectURL(excelBlob)
      const a = document.createElement('a')
      a.href = url
      a.download = `kasbon-${new Date(selectedYear, selectedMonth - 1).toLocaleDateString('id-ID', { month: 'long', year: 'numeric' }).toLowerCase()}.xlsx`
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      URL.revokeObjectURL(url)
    } catch (error) {
      console.error("Error downloading Excel:", error)
      toast.error("Gagal mengunduh file Excel")
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h2 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-breaktime-primary to-breaktime-secondary bg-clip-text text-transparent">
          Kasbon & Piutang
        </h2>
        <div className="flex gap-2 items-center">
          {/* Filter Bulan dan Tahun */}
          <div className="join">
            <select
              className="select select-bordered join-item text-base-content"
              value={selectedMonth}
              onChange={(e) => setSelectedMonth(Number(e.target.value))}
            >
              {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                <option key={month} value={month}>
                  {new Date(2024, month - 1).toLocaleDateString('id-ID', { month: 'long' })}
                </option>
              ))}
            </select>
            <select
              className="select select-bordered join-item text-base-content"
              value={selectedYear}
              onChange={(e) => setSelectedYear(Number(e.target.value))}
            >
              {Array.from({ length: 5 }, (_, i) => 2025 + i).map((year) => (
                <option key={year} value={year}>
                  {year}
                </option>
              ))}
            </select>
          </div>
          <div className="flex gap-2">
            <button
              onClick={handleDownloadExcel}
              className="btn btn-sm sm:btn-md bg-success/10 hover:bg-success/20 text-success border-0 gap-2"
            >
              <Download className="h-4 w-4 sm:h-5 sm:w-5" />
              <span className="hidden xs:inline">Export Excel</span>
              <span className="inline xs:hidden">Excel</span>
            </button>
            <button
              onClick={() => {
                setFormData({
                  employeeId: "",
                  amount: 0,
                  type: "KASBON",
                  notes: ""
                })
                setIsModalOpen(true)
              }}
              className="btn btn-sm sm:btn-md bg-gradient-to-r from-breaktime-primary to-breaktime-secondary text-white hover:from-breaktime-primary/90 hover:to-breaktime-secondary/90 border-0"
            >
              <Plus className="h-4 w-4 sm:h-5 sm:w-5" />
              <span className="hidden xs:inline">Tambah Baru</span>
              <span className="inline xs:hidden">Tambah</span>
            </button>
          </div>
        </div>
      </div>

      {/* Search Bar */}
      <div className="form-control w-full sm:w-96">
        <div className="relative">
          <input
            type="text"
            placeholder="Cari berdasarkan nama, nominal, status..."
            className="input input-bordered w-full pr-12 placeholder:text-base-content/50 text-base-content"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <button className="btn btn-ghost btn-sm absolute right-0 top-0 h-full px-3 hover:bg-transparent">
            <Search className="h-5 w-5 text-base-content/60" />
          </button>
        </div>
      </div>

      {/* Kasbon Table */}
      <div className="overflow-x-auto bg-base-100 rounded-box shadow-sm border border-base-200">
        <table className="table table-sm sm:table-md">
          <thead className="bg-base-200/50 text-xs sm:text-sm">
            <tr>
              <th className="font-semibold text-base-content">Tanggal</th>
              <th className="font-semibold text-base-content">Karyawan</th>
              <th className="font-semibold text-base-content">Nominal</th>
              <th className="hidden sm:table-cell font-semibold text-base-content">Jenis</th>
              <th className="hidden sm:table-cell font-semibold text-base-content">Status</th>
              <th className="hidden md:table-cell font-semibold text-base-content">Keterangan</th>
              <th className="font-semibold text-base-content">Aksi</th>
            </tr>
          </thead>
          <tbody className="text-xs sm:text-sm">
            {isLoading ? (
              <tr>
                <td colSpan={7} className="text-center">
                  <span className="loading loading-spinner loading-md text-primary"></span>
                </td>
              </tr>
            ) : filteredKasbons.length === 0 ? (
              <tr>
                <td colSpan={7} className="text-center text-base-content">
                  Tidak ada data kasbon & piutang
                </td>
              </tr>
            ) : (
              filteredKasbons.map((kasbon) => (
                <tr key={kasbon.id} className="hover:bg-base-200/30">
                  <td>
                    <div className="flex items-center gap-2 text-base-content">
                      <Calendar className="h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="line-clamp-1">{formatDate(kasbon.createdAt)}</span>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center gap-2">
                      <User className="h-3 w-3 sm:h-4 sm:w-4 text-primary" />
                      <span className="font-medium text-base-content line-clamp-1">
                        {kasbon.user.name}
                        {kasbon.user.employee && (
                          <span className="text-xs text-base-content/70 ml-1">
                            ({kasbon.user.employee.position.name})
                          </span>
                        )}
                      </span>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center gap-2">
                      <DollarSign className="h-3 w-3 sm:h-4 sm:w-4 text-primary" />
                      <span className="font-medium text-base-content">{formatCurrency(kasbon.amount)}</span>
                    </div>
                  </td>
                  <td className="hidden sm:table-cell">
                    <span className={`badge badge-sm sm:badge-md ${
                      kasbon.type === "KASBON" 
                      ? "bg-gradient-to-r from-orange-500 to-amber-500 text-white border-0" 
                      : "bg-gradient-to-r from-blue-500 to-cyan-500 text-white border-0"
                    } font-medium`}>
                      <div className="flex items-center gap-1.5">
                        {kasbon.type === "KASBON" ? (
                          <Wallet className="w-3 h-3 sm:w-4 sm:h-4" />
                        ) : (
                          <CircleDollarSign className="w-3 h-3 sm:w-4 sm:h-4" />
                        )}
                        {getTypeLabel(kasbon.type)}
                      </div>
                    </span>
                  </td>
                  <td className="hidden sm:table-cell">
                    <span className={`badge badge-sm sm:badge-md ${getStatusColor(kasbon.status)}`}>
                      {getStatusLabel(kasbon.status)}
                    </span>
                  </td>
                  <td className="hidden md:table-cell">
                    <div className="flex items-center gap-2">
                      <FileText className="h-3 w-3 sm:h-4 sm:w-4 text-primary" />
                      <span className="text-base-content line-clamp-1">{kasbon.description}</span>
                    </div>
                  </td>
                  <td>
                    <div className="flex gap-1 sm:gap-2">
                      {kasbon.status === "PENDING" && (
                        <>
                          <button
                            onClick={() => handleUpdateStatus(kasbon.id, "APPROVED")}
                            className="btn btn-square btn-xs sm:btn-sm btn-ghost hover:bg-success/20 text-success"
                            title="Setujui"
                          >
                            <CheckCircle2 className="h-3 w-3 sm:h-4 sm:w-4" />
                          </button>
                          <button
                            onClick={() => handleUpdateStatus(kasbon.id, "REJECTED")}
                            className="btn btn-square btn-xs sm:btn-sm btn-ghost hover:bg-error/20 text-error"
                            title="Tolak"
                          >
                            <XCircle className="h-3 w-3 sm:h-4 sm:w-4" />
                          </button>
                        </>
                      )}
                      {kasbon.status === "APPROVED" && (
                        <button
                          onClick={() => handleUpdateStatus(kasbon.id, "PAID")}
                          className="btn btn-square btn-xs sm:btn-sm btn-ghost hover:bg-primary/20 text-primary"
                          title="Tandai Lunas"
                        >
                          <CircleDollarSign className="h-3 w-3 sm:h-4 sm:w-4" />
                        </button>
                      )}
                      <button
                        onClick={() => handleDelete(kasbon.id)}
                        className="btn btn-square btn-xs sm:btn-sm btn-ghost hover:bg-error/20 text-error"
                        title="Hapus"
                      >
                        <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Modal Form */}
      {isModalOpen && (
        <div className="modal modal-open">
          <div className="modal-box w-11/12 max-w-3xl p-4 sm:p-6">
            <h3 className="text-lg sm:text-xl font-bold bg-gradient-to-r from-breaktime-primary to-breaktime-secondary bg-clip-text text-transparent">
              Tambah Kasbon / Piutang
            </h3>
            <form onSubmit={handleSubmit} className="mt-4 sm:mt-6 space-y-4 sm:space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6">
                <div className="form-control w-full">
                  <label className="label">
                    <span className="label-text text-sm sm:text-base font-medium text-base-content">Karyawan</span>
                  </label>
                  <div className="dropdown w-full">
                    <input
                      type="text"
                      className="input input-bordered w-full text-xs sm:text-sm md:text-base text-base-content placeholder:text-base-content/50"
                      placeholder="Cari karyawan..."
                      value={searchEmployee}
                      onChange={(e) => {
                        setSearchEmployee(e.target.value)
                        setIsDropdownOpen(true)
                      }}
                      onFocus={() => setIsDropdownOpen(true)}
                    />
                    {isDropdownOpen && (
                      <ul className="dropdown-content menu p-2 sm:p-3 shadow bg-base-100 rounded-box w-full max-h-[150px] sm:max-h-[200px] md:max-h-[250px] overflow-y-auto z-[1]">
                        {filteredEmployees.length === 0 ? (
                          <li className="text-xs sm:text-sm md:text-base text-base-content/70 p-2 sm:p-3">Tidak ada karyawan ditemukan</li>
                        ) : (
                          filteredEmployees.map((employee) => (
                            <li key={employee.id}>
                              <button
                                type="button"
                                className="text-xs sm:text-sm md:text-base text-base-content py-2 sm:py-3 px-2 sm:px-3 hover:bg-base-200/50 rounded-lg transition-colors"
                                onClick={() => {
                                  setFormData({ ...formData, employeeId: employee.id })
                                  setSearchEmployee(employee.user.name)
                                  setIsDropdownOpen(false)
                                }}
                              >
                                <div className="flex flex-col gap-0.5 sm:gap-1">
                                  <span className="font-medium line-clamp-1">{employee.user.name}</span>
                                  <span className="text-[10px] sm:text-xs md:text-sm text-base-content/70 line-clamp-1">{employee.position.name}</span>
                                </div>
                              </button>
                            </li>
                          ))
                        )}
                      </ul>
                    )}
                  </div>
                </div>
                <div className="form-control w-full">
                  <label className="label">
                    <span className="label-text text-sm sm:text-base font-medium text-base-content">Jenis</span>
                  </label>
                  <select
                    className="select select-bordered w-full text-sm sm:text-base text-base-content"
                    value={formData.type}
                    onChange={(e) => setFormData({ ...formData, type: e.target.value as KasbonType })}
                    required
                  >
                    <option value="KASBON">Kasbon</option>
                    <option value="PIUTANG">Piutang</option>
                  </select>
                </div>
                <div className="form-control w-full">
                  <label className="label">
                    <span className="label-text text-sm sm:text-base font-medium text-base-content">Nominal</span>
                  </label>
                  <div className="join w-full">
                    <span className="btn join-item bg-base-200 border-base-300 text-sm sm:text-base text-base-content hover:bg-base-300">Rp</span>
                    <input
                      type="text"
                      className="input input-bordered join-item w-full text-sm sm:text-base text-base-content placeholder:text-base-content/50"
                      value={formatNumber(formData.amount.toString())}
                      onChange={(e) => handleNumberChange(e.target.value)}
                      placeholder="Masukkan nominal"
                      required
                    />
                  </div>
                </div>
                <div className="form-control w-full">
                  <label className="label">
                    <span className="label-text text-sm sm:text-base font-medium text-base-content">Keterangan</span>
                  </label>
                  <textarea
                    className="textarea textarea-bordered w-full min-h-[80px] text-sm sm:text-base text-base-content placeholder:text-base-content/50"
                    value={formData.notes}
                    onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                    placeholder="Masukkan keterangan"
                    required
                  />
                </div>
              </div>
              <div className="modal-action flex-wrap gap-2 sm:gap-3 mt-6">
                <button 
                  type="button" 
                  className="btn btn-sm sm:btn-md bg-base-200 hover:bg-base-300 text-sm sm:text-base text-base-content border-base-300"
                  onClick={() => {
                    setIsModalOpen(false)
                    setSearchEmployee("")
                    setFormData({
                      employeeId: "",
                      amount: 0,
                      type: "KASBON",
                      notes: ""
                    })
                  }}
                >
                  Batal
                </button>
                <button 
                  type="submit" 
                  className="btn btn-sm sm:btn-md bg-gradient-to-r from-breaktime-primary to-breaktime-secondary text-sm sm:text-base text-white hover:from-breaktime-primary/90 hover:to-breaktime-secondary/90 border-0"
                >
                  Simpan
                </button>
              </div>
            </form>
          </div>
          <div 
            className="modal-backdrop bg-base-content/20" 
            onClick={() => {
              setIsModalOpen(false)
              setSearchEmployee("")
              setFormData({
                employeeId: "",
                amount: 0,
                type: "KASBON",
                notes: ""
              })
            }}
          ></div>
        </div>
      )}

      {/* Modal Delete */}
      {isDeleteModalOpen && (
        <div className="modal modal-open">
          <div className="modal-box bg-base-100 border border-base-300">
            <h3 className="font-bold text-lg text-base-content">Konfirmasi Hapus</h3>
            <p className="py-4 text-base-content">Apakah Anda yakin ingin menghapus data kasbon ini?</p>
            <p className="text-sm text-base-content/70">Data yang sudah dihapus tidak dapat dikembalikan.</p>
            <div className="modal-action">
              <button
                onClick={() => {
                  setIsDeleteModalOpen(false)
                  setDeletingKasbon(null)
                }}
                className="btn btn-ghost text-base-content hover:bg-base-200"
              >
                Batal
              </button>
              <button
                onClick={handleConfirmDelete}
                className="btn bg-error hover:bg-error/90 text-error-content border-none"
              >
                Hapus
              </button>
            </div>
          </div>
          <div className="modal-backdrop bg-base-300/50"></div>
        </div>
      )}
    </div>
  )
} 