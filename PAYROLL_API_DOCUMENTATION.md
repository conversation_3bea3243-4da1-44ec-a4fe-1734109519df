# 📊 API Payroll BreakTime - Dokumentasi Lengkap

API ini dirancang khusus untuk integrasi dengan aplikasi penggajian eksternal guna mengambil data omzet, komisi, dan lembur terapis dari sistem BreakTime.

## 🔐 Autentikasi

Semua endpoint API payroll memerlukan API key untuk autentikasi.

### Header yang <PERSON>
```
x-api-key: YOUR_API_KEY_HERE
Content-Type: application/json
```

### Setup Environment Variable
Tambahkan di file `.env`:
```env
PAYROLL_API_KEY="your-secure-api-key-here-64-characters-minimum"
```

**Rekomendasi:** Generate API key dengan `openssl rand -hex 32`

---

## 📍 Endpoints

### 1. GET `/api/payroll` - Summary Payroll Semua Terapis

Mendapatkan ringkasan payroll untuk semua terapis dalam periode tertentu.

#### Query Parameters

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `bulan` | string | No | Bulan (1-12) | `12` |
| `tahun` | string | No | Tahun 4 digit | `2024` |
| `startDate` | string | No | Tanggal mulai (YYYY-MM-DD) | `2024-12-01` |
| `endDate` | string | No | Tanggal akhir (YYYY-MM-DD) | `2024-12-31` |
| `outletId` | string | No | Filter outlet spesifik | `uuid-outlet-id` |

**Note:** Jika tidak ada parameter tanggal, default ke bulan ini. `startDate/endDate` menimpa `bulan/tahun`.

#### Response Example
```json
{
  "success": true,
  "message": "Data payroll berhasil diambil untuk periode Desember 2024",
  "data": {
    "summary": [
      {
        "therapistId": "uuid-terapis-1",
        "therapistName": "Ahmad Spa",
        "outletName": "BreakTime Makassar",
        "totalOmzet": 15500000,
        "totalKomisi": 2325000,
        "totalLembur": 450000,
        "totalLemburMinutes": 180,
        "totalTransaksi": 85,
        "periode": "Desember 2024"
      }
    ],
    "totalOmzetSemua": 45500000,
    "totalKomisiSemua": 6825000,
    "totalLemburSemua": 1250000,
    "totalTerapisAktif": 12,
    "periode": "Desember 2024",
    "filter": {
      "outlet": "uuid-outlet-id",
      "startDate": "2024-12-01T00:00:00.000Z",
      "endDate": "2024-12-31T23:59:59.999Z"
    }
  }
}
```

---

### 2. GET `/api/payroll/therapist` - Detail Payroll Terapis Spesifik

Mendapatkan detail lengkap payroll untuk satu terapis termasuk breakdown per transaksi.

#### Query Parameters

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `therapistId` | string | Yes* | ID terapis | `uuid-terapis-1` |
| `therapistName` | string | Yes* | Nama terapis | `Ahmad Spa` |
| `bulan` | string | No | Bulan (1-12) | `12` |
| `tahun` | string | No | Tahun 4 digit | `2024` |
| `startDate` | string | No | Tanggal mulai | `2024-12-01` |
| `endDate` | string | No | Tanggal akhir | `2024-12-31` |

**Note:** Salah satu dari `therapistId` atau `therapistName` harus diisi.

#### Response Example
```json
{
  "success": true,
  "message": "Detail payroll terapis Ahmad Spa berhasil diambil untuk periode Desember 2024",
  "data": {
    "therapistId": "uuid-terapis-1",
    "therapistName": "Ahmad Spa",
    "outletName": "BreakTime Makassar",
    "isActive": true,
    "periode": "Desember 2024",
    "summary": {
      "totalOmzet": 15500000,
      "totalKomisi": 2325000,
      "totalLembur": 450000,
      "totalLemburMinutes": 180,
      "totalTransaksi": 85,
      "averageOmzetPerTransaksi": 182352.94,
      "averageKomisiPerTransaksi": 27352.94
    },
    "transactions": [
      {
        "transactionId": "B000123",
        "transactionDate": "2024-12-15T10:30:00.000Z",
        "customerName": "Budi Santoso",
        "outletName": "BreakTime Makassar",
        "serviceName": "Deep Tissue Massage (1x), Reflexology (1x)",
        "totalAmount": 280000,
        "discountAmount": 0,
        "additionalCharge": 0,
        "omzetLayanan": 280000,
        "komisi": 42000,
        "overtimeMinutes": 30,
        "overtimeAmount": 75000
      }
    ],
    "breakdown": {
      "omzetPerHari": {
        "2024-12-01": 520000,
        "2024-12-02": 675000,
        "2024-12-03": 890000
      },
      "komisiPerHari": {
        "2024-12-01": 78000,
        "2024-12-02": 101250,
        "2024-12-03": 133500
      },
      "lemburPerHari": {
        "2024-12-01": 0,
        "2024-12-02": 50000,
        "2024-12-03": 125000
      }
    }
  }
}
```

---

### 3. GET `/api/payroll/outlets` - Data Payroll Per Outlet

Mendapatkan summary payroll yang dikelompokkan per outlet dengan detail terapis di setiap outlet.

#### Query Parameters

| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `bulan` | string | No | Bulan (1-12) | `12` |
| `tahun` | string | No | Tahun 4 digit | `2024` |
| `startDate` | string | No | Tanggal mulai | `2024-12-01` |
| `endDate` | string | No | Tanggal akhir | `2024-12-31` |

#### Response Example
```json
{
  "success": true,
  "message": "Data payroll outlet berhasil diambil untuk periode Desember 2024",
  "data": {
    "outlets": [
      {
        "outletId": "uuid-outlet-1",
        "outletName": "BreakTime Makassar",
        "totalOmzet": 25000000,
        "totalKomisi": 3750000,
        "totalLembur": 800000,
        "totalTransaksi": 150,
        "totalTerapisAktif": 8,
        "averageOmzetPerTransaksi": 166666.67,
        "averageOmzetPerTerapis": 3125000,
        "periode": "Desember 2024",
        "therapists": [
          {
            "therapistId": "uuid-terapis-1",
            "therapistName": "Ahmad Spa",
            "totalOmzet": 5500000,
            "totalKomisi": 825000,
            "totalLembur": 200000,
            "totalTransaksi": 35
          }
        ]
      }
    ],
    "grandTotal": {
      "totalOmzetSemua": 45500000,
      "totalKomisiSemua": 6825000,
      "totalLemburSemua": 1250000,
      "totalTransaksiSemua": 285,
      "totalOutlet": 3
    },
    "periode": "Desember 2024",
    "filter": {
      "startDate": "2024-12-01T00:00:00.000Z",
      "endDate": "2024-12-31T23:59:59.999Z"
    }
  }
}
```

---

## 💰 Definisi Data Keuangan

### 1. **Total Omzet**
- **Formula:** `totalAmount + discountAmount + additionalCharge`
- **Deskripsi:** Total nilai layanan yang diberikan TANPA termasuk lembur
- **Catatan:** Menambahkan diskon untuk mendapatkan harga asli layanan

### 2. **Total Komisi**
- **Sumber:** Field `therapistCommissionEarned` dari database
- **Deskripsi:** Komisi terapis yang sudah dihitung berdasarkan komisi khusus atau default service
- **Rumus Asli:** `(special_commission || service_commission) × quantity`

### 3. **Total Lembur**
- **Sumber:** Field `overtimeAmount` dari database
- **Deskripsi:** Pembayaran lembur terapis (TERPISAH dari omzet layanan)
- **Satuan:** Rupiah dan menit

---

## 🔧 Error Handling

### Error Responses

#### 401 Unauthorized
```json
{
  "success": false,
  "message": "API key tidak valid atau tidak ditemukan",
  "data": null
}
```

#### 400 Bad Request
```json
{
  "success": false,
  "message": "Format bulan atau tahun tidak valid. Gunakan bulan 1-12 dan tahun 4 digit.",
  "data": null
}
```

#### 404 Not Found
```json
{
  "success": false,
  "message": "Terapis dengan nama \"Ahmad Spa\" tidak ditemukan",
  "data": null
}
```

#### 500 Internal Server Error
```json
{
  "success": false,
  "message": "Terjadi kesalahan saat mengambil data payroll",
  "data": null
}
```

---

## 📝 Contoh Implementasi

### JavaScript/Node.js
```javascript
const API_BASE_URL = 'https://your-breaktime-domain.com';
const API_KEY = 'your-api-key-here';

// Get payroll summary
async function getPayrollSummary(bulan, tahun, outletId = null) {
  const params = new URLSearchParams({
    bulan: bulan.toString(),
    tahun: tahun.toString(),
    ...(outletId && { outletId })
  });

  const response = await fetch(`${API_BASE_URL}/api/payroll?${params}`, {
    headers: {
      'x-api-key': API_KEY,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
}

// Get therapist detail
async function getTherapistPayroll(therapistName, bulan, tahun) {
  const params = new URLSearchParams({
    therapistName,
    bulan: bulan.toString(),
    tahun: tahun.toString()
  });

  const response = await fetch(`${API_BASE_URL}/api/payroll/therapist?${params}`, {
    headers: {
      'x-api-key': API_KEY,
      'Content-Type': 'application/json'
    }
  });

  return await response.json();
}

// Usage example
const payrollData = await getPayrollSummary(12, 2024);
const therapistDetail = await getTherapistPayroll('Ahmad Spa', 12, 2024);
```

### Python
```python
import requests
from datetime import datetime

API_BASE_URL = 'https://your-breaktime-domain.com'
API_KEY = 'your-api-key-here'

class BreakTimePayrollAPI:
    def __init__(self, api_key):
        self.api_key = api_key
        self.headers = {
            'x-api-key': api_key,
            'Content-Type': 'application/json'
        }
    
    def get_payroll_summary(self, bulan, tahun, outlet_id=None):
        params = {
            'bulan': str(bulan),
            'tahun': str(tahun)
        }
        if outlet_id:
            params['outletId'] = outlet_id
            
        response = requests.get(
            f'{API_BASE_URL}/api/payroll',
            headers=self.headers,
            params=params
        )
        return response.json()
    
    def get_therapist_detail(self, therapist_name, bulan, tahun):
        params = {
            'therapistName': therapist_name,
            'bulan': str(bulan),
            'tahun': str(tahun)
        }
        
        response = requests.get(
            f'{API_BASE_URL}/api/payroll/therapist',
            headers=self.headers,
            params=params
        )
        return response.json()

# Usage
api = BreakTimePayrollAPI(API_KEY)
payroll_data = api.get_payroll_summary(12, 2024)
therapist_detail = api.get_therapist_detail('Ahmad Spa', 12, 2024)
```

---

## 🚀 Best Practices

### 1. **Caching**
- Cache response untuk data bulanan (jarang berubah)
- TTL rekomendasi: 1 jam untuk data bulan berjalan, 24 jam untuk data bulan lalu

### 2. **Rate Limiting**
- Maksimal 100 requests per menit per API key
- Implement exponential backoff untuk retry

### 3. **Error Handling**
- Selalu cek field `success` sebelum memproses `data`
- Handle semua HTTP status codes (401, 404, 500)
- Log semua API calls untuk debugging

### 4. **Security**
- Jangan expose API key di client-side code
- Gunakan HTTPS untuk semua requests
- Rotasi API key secara berkala

### 5. **Data Validation**
- Validasi response structure sebelum mapping ke database payroll
- Handle null/undefined values dengan graceful fallback

---

## 📞 Support

Untuk pertanyaan teknis atau masalah integrasi:
- **Email:** <EMAIL>
- **Developer:** <EMAIL>

**Last Updated:** Desember 2024
**API Version:** 1.0 