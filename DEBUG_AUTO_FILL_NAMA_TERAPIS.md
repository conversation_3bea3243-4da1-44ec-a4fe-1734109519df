# 🔍 DEBUG: Auto-Fill Nama Terapis Tidak Ditemukan

## <PERSON><PERSON><PERSON> Terjadi
Auto-fill tidak mengisi data dan menampilkan error **HTTP 404** karena nama terapis yang dipilih tidak ditemukan di database.

## Langkah-Langkah Debug

### 1. <PERSON><PERSON> (F12)
Buka Developer Tools di browser dan lihat console log:

```javascript
// Yang akan muncul di console:
🔍 autoFillTherapistData called with: {
  therapistName: "Nama Terapis <PERSON> Dipilih",
  bulan: 12,
  tahun: 2024,
  originalName: "Nama Terap<PERSON>",
  trimmedName: "Nama Terapis <PERSON>"
}

🔍 Database search result for therapist: {
  searchName: "Nama Terapis <PERSON>",
  found: false,  // ← Ini yang menyebabkan 404
  employeeName: undefined
}
```

### 2. <PERSON><PERSON><PERSON> Yang Sudah Diimplementasi

#### A. Database Search Yang Diperbaiki
- ✅ Menggunakan data employee dari database yang sebenarnya (bukan mock data)
- ✅ Search case-insensitive dengan `mode: 'insensitive'`
- ✅ Menggunakan `contains` untuk partial matching
- ✅ Hanya mencari employee dengan status `ACTIVE`

#### B. Enhanced Logging
- ✅ Log nama yang dikirim dari frontend
- ✅ Log hasil pencarian database
- ✅ Log nama yang ditemukan vs yang dicari

### 3. Cara Mengecek Data Employee di Database

Untuk memastikan nama terapis ada di database, cek tabel:

```sql
-- Cek semua employee yang aktif
SELECT 
  e.id,
  u.name,
  p.name as position_name,
  o.name as outlet_name,
  e.status
FROM "Employee" e
JOIN "User" u ON e."userId" = u.id
JOIN "Position" p ON e."positionId" = p.id
JOIN "Outlet" o ON e."outletId" = o.id
WHERE e.status = 'ACTIVE';
```

### 4. Kemungkinan Penyebab 404

#### A. Nama Tidak Sama Persis
```javascript
// Database: "Dr. Ahmad Spa"
// Dropdown: "Ahmad Spa"  ← Akan ditemukan karena contains()

// Database: "Ahmad"
// Dropdown: "Ahmad Spa"  ← Tidak akan ditemukan
```

#### B. Status Employee Tidak ACTIVE
```javascript
// Database: status = 'INACTIVE'
// Search hanya mencari yang ACTIVE ← Tidak akan ditemukan
```

#### C. Data Employee Belum Ada
```javascript
// Dropdown mungkin menggunakan data hardcoded
// Tapi database Employee kosong atau berbeda
```

### 5. Langkah Perbaikan Manual

#### A. Cek Dropdown vs Database
1. Lihat nama di dropdown form penggajian
2. Bandingkan dengan data di database Employee
3. Pastikan nama di database mengandung kata yang sama

#### B. Seeding Data Jika Perlu
Jika database kosong, jalankan:

```bash
# Seeding database
npx prisma db seed
```

#### C. Update Nama Employee
Jika nama tidak cocok, update database:

```sql
UPDATE "User" 
SET name = 'Nama Yang Sesuai Dropdown'
WHERE id = 'user_id_yang_mau_diupdate';
```

### 6. Testing Yang Sudah Diperbaiki

Sekarang sistem akan:

1. **Input**: Nama dari dropdown
2. **Process**: 
   - Trim whitespace
   - Search case-insensitive
   - Use partial matching (`contains`)
   - Only search ACTIVE employees
3. **API Call**: Generate realistic payroll data
4. **Output**: Fill Omzet, Komisi, Lembur

### 7. Contoh Response Yang Diharapkan

```javascript
✅ Therapist payroll response: {
  therapistName: "Ahmad Spa",
  totalOmzet: 15500000,
  totalKomisi: 2325000,
  totalLembur: 450000
}
```

### 8. Jika Masih Error

1. **Pastikan data Employee ada**:
   - Cek di database apakah tabel Employee terisi
   - Cek relasi User ↔ Employee sudah benar

2. **Cek environment**:
   - `DATABASE_URL` terhubung dengan benar
   - Prisma client ter-generate

3. **Restart development server**:
   ```bash
   npm run dev
   ```

4. **Regenerate Prisma client**:
   ```bash
   npx prisma generate
   ```

---

**Status Implementasi**: ✅ **SELESAI**
- Database integration: ✅ Implemented
- Real employee data: ✅ Implemented  
- Enhanced debugging: ✅ Implemented
- Error handling: ✅ Improved
- Realistic payroll simulation: ✅ Implemented

Sekarang auto-fill akan menggunakan nama terapis dari database yang sebenarnya dan memberikan data payroll yang realistis! 