import { NextResponse } from "next/server"
import type { NextRequest } from "next/server"
import * as jose from 'jose'

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key"
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || "1d"

export async function middleware(request: NextRequest) {
  const token = request.cookies.get("auth_token")?.value
  const isLoginPage = request.nextUrl.pathname === "/auth/login"
  const isPublicPath = request.nextUrl.pathname === "/"
  const isDashboardPath = request.nextUrl.pathname.startsWith("/dashboard")

  console.log("[Middleware] Path:", request.nextUrl.pathname)
  console.log("[Middleware] Token exists:", !!token)
  console.log("[Middleware] Is login page:", isLoginPage)
  console.log("[Middleware] Is dashboard path:", isDashboardPath)

  const secret = new TextEncoder().encode(JWT_SECRET)

  // Jika user mencoba akses login page tapi sudah login
  if (isLoginPage && token) {
    try {
      const { payload } = await jose.jwtVerify(token, secret)
      console.log("[Middleware] Token valid, redirecting to dashboard")
      return NextResponse.redirect(new URL("/dashboard", request.url))
    } catch (error) {
      console.log("[Middleware] Token invalid:", error)
      // Token tidak valid, hapus cookie
      const response = NextResponse.redirect(new URL("/auth/login", request.url))
      response.cookies.delete("auth_token")
      return response
    }
  }

  // Jika user mencoba akses halaman yang butuh auth tapi belum login
  if (isDashboardPath && !token) {
    console.log("[Middleware] No token for dashboard access")
    const response = NextResponse.redirect(new URL("/auth/login", request.url))
    response.cookies.delete("auth_token")
    return response
  }

  // Verifikasi token untuk halaman yang butuh auth
  if (isDashboardPath && token) {
    try {
      const { payload } = await jose.jwtVerify(token, secret)
      console.log("[Middleware] Token valid for dashboard")
      // Tambahkan user info ke headers untuk digunakan di API routes
      const response = NextResponse.next()
      response.headers.set("x-user-id", String(payload.userId))
      response.headers.set("x-user-role", String(payload.role))
      return response
    } catch (error) {
      console.log("[Middleware] Token invalid for dashboard:", error)
      // Token tidak valid, redirect ke login
      const response = NextResponse.redirect(new URL("/auth/login", request.url))
      response.cookies.delete("auth_token")
      return response
    }
  }

  const response = NextResponse.next()

  try {
    // Handle CORS jika diperlukan
    response.headers.set("Access-Control-Allow-Origin", "*")
    response.headers.set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
    response.headers.set("Access-Control-Allow-Headers", "Content-Type, Authorization")
    
    return response
  } catch (error) {
    console.error("Middleware error:", error)
    return new NextResponse(
      JSON.stringify({
        success: false,
        message: "Terjadi kesalahan server",
      }),
      { status: 500, headers: { "Content-Type": "application/json" } }
    )
  }
}

export const config = {
  matcher: [
    "/dashboard/:path*",
    "/auth/login",
    "/api/:path*"
  ]
} 