import { CreatePositionDTO, Position, UpdatePositionDTO } from "@/types/position"

export async function getPositions(): Promise<Position[]> {
  try {
    const response = await fetch("/api/positions")
    if (!response.ok) {
      throw new Error("Failed to fetch positions")
    }
    const data = await response.json()
    return data as Position[]
  } catch (error) {
    console.error("Error fetching positions:", error)
    throw error
  }
}

export async function createPosition(input: CreatePositionDTO): Promise<Position> {
  try {
    const response = await fetch("/api/positions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(input),
    })
    if (!response.ok) {
      throw new Error("Failed to create position")
    }
    const data = await response.json()
    return data as Position
  } catch (error) {
    console.error("Error creating position:", error)
    throw error
  }
}

export async function updatePosition(input: UpdatePositionDTO): Promise<Position> {
  try {
    const response = await fetch(`/api/positions/${input.id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(input),
    })
    if (!response.ok) {
      throw new Error("Failed to update position")
    }
    const data = await response.json()
    return data as Position
  } catch (error) {
    console.error("Error updating position:", error)
    throw error
  }
}

export async function deletePosition(id: string): Promise<void> {
  try {
    const response = await fetch(`/api/positions/${id}`, {
      method: "DELETE",
    })
    if (!response.ok) {
      throw new Error("Failed to delete position")
    }
  } catch (error) {
    console.error("Error deleting position:", error)
    throw error
  }
}

// Fungsi untuk menghitung gaji berdasarkan omset dan persentase jabatan
export function calculateSalaryByPosition(omset: number, omsetPercentage: number | null): number {
  if (omsetPercentage === null) return 0;
  return omset * (omsetPercentage / 100)
}

// Fungsi untuk menghitung gaji kontrak berdasarkan omset dan target kontrak
export function calculateContractSalary(omset: number, targetKontrak: number, gajiPokok: number): number {
  return (((omset * 100) / targetKontrak) * 0.01) * gajiPokok
}

export async function duplicatePosition(position: Position): Promise<Position> {
  try {
    const duplicateData: CreatePositionDTO = {
      name: `${position.name} (Copy)`,
      omsetPercentage: position.omsetPercentage,
      gajiPokok: position.gajiPokok,
      tunjangan: position.tunjangan || {
        transport: 0,
        pulsa: 0,
        makan: 0
      },
      isKontrak: position.isKontrak,
      targetKontrak: position.targetKontrak
    }

    const response = await fetch("/api/positions", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(duplicateData),
    })
    
    if (!response.ok) {
      throw new Error("Gagal menduplikasi jabatan")
    }
    
    const data = await response.json()
    return data as Position
  } catch (error) {
    console.error("Error duplicating position:", error)
    throw error
  }
} 