import { NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"
import { calculateCustomPayrollDateRange } from "@/services/payroll-api"

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  }
})

// Real integrasi dengan API BreakTime menggunakan API key dan date range spesifik
async function fetchTherapistDataFromBreakTime(
  therapistName: string, 
  startDate?: string, 
  endDate?: string, 
  bulan?: number, 
  tahun?: number, 
  outlet?: any
) {
  const apiKey = "c4a1c8fdcde5513a3feeb9b8462ada8909a4a54af4211ea11efaa87fa9eefb56"
  
  try {
    console.log("📡 Calling REAL BreakTime API for:", { 
      therapistName, 
      startDate,
      endDate,
      bulan, 
      tahun, 
      outlet: outlet?.name || "All Outlets" 
    })
    
    console.log("🏢 Using outlet:", { 
      name: outlet?.name || "Unknown", 
      code: outlet?.code || "N/A" 
    })
    
    // Build query parameters for BreakTime API
    const queryParams = new URLSearchParams({
      therapistName: therapistName
    })

    // Prioritas menggunakan date range jika tersedia, fallback ke bulan/tahun
    if (startDate && endDate) {
      queryParams.append('startDate', startDate)
      queryParams.append('endDate', endDate)
      console.log("📅 Using custom date range:", { startDate, endDate })
    } else if (bulan && tahun) {
      queryParams.append('bulan', bulan.toString())
      queryParams.append('tahun', tahun.toString())
      console.log("📅 Using month/year:", { bulan, tahun })
    }
    
    console.log("📤 API Request URL:", `https://www.mybreaktime.co.id/api/payroll/therapist?${queryParams}`)

    const breakTimeResponse = await fetch(`https://www.mybreaktime.co.id/api/payroll/therapist?${queryParams}`, {
      method: 'GET',
      headers: {
        'x-api-key': apiKey,
        'Content-Type': 'application/json'
      }
    })
    
    if (!breakTimeResponse.ok) {
      console.error("❌ BreakTime API gagal:", breakTimeResponse.status, breakTimeResponse.statusText)
      throw new Error(`BreakTime API error: ${breakTimeResponse.status} - ${breakTimeResponse.statusText}`)
    }
    
    const breakTimeData = await breakTimeResponse.json()
    console.log("✅ Real BreakTime API response:", breakTimeData)
    
    // Parse response dari BreakTime API (sesuai dokumentasi real)
    console.log("✅ Real BreakTime API response:", breakTimeData)
    
    if (!breakTimeData.success || !breakTimeData.data) {
      throw new Error(`BreakTime API error: ${breakTimeData.message || 'Invalid response structure'}`)
    }

    const { data } = breakTimeData
    const { summary } = data

    return {
      therapistId: data.therapistId || "",
      therapistName: data.therapistName || therapistName,
      outletName: data.outletName || outlet?.name || "BreakTime Spa",
      isActive: data.isActive !== undefined ? data.isActive : true,
      summary: {
        totalOmzet: summary.totalOmzet || 0,
        totalKomisi: summary.totalKomisi || 0,
        totalLembur: summary.totalLembur || 0,
        totalLemburMinutes: summary.totalLemburMinutes || 0,
        totalTransaksi: summary.totalTransaksi || 0,
        averageOmzetPerTransaksi: summary.averageOmzetPerTransaksi || 0,
        averageKomisiPerTransaksi: summary.averageKomisiPerTransaksi || 0
      },
      transactions: data.transactions || []
    }
    
  } catch (error) {
    console.error("❌ Error calling BreakTime API:", error)
    throw new Error(`Gagal mengakses BreakTime API: ${error instanceof Error ? error.message : 'Unknown error'}`)
  }
}



// Helper function untuk nama bulan
function getMonthName(bulan: number): string {
  const monthNames = [
    "Januari", "Februari", "Maret", "April", "Mei", "Juni",
    "Juli", "Agustus", "September", "Oktober", "November", "Desember"
  ]
  return monthNames[bulan - 1] || "Januari"
}

export async function GET(request: Request) {
  try {
    // Extract API key from headers
    const apiKey = request.headers.get('x-api-key')
    const expectedApiKey = process.env.PAYROLL_API_KEY || "c4a1c8fdcde5513a3feeb9b8462ada8909a4a54af4211ea11efaa87fa9eefb56"
    
    // Validate API key
    if (!apiKey || apiKey !== expectedApiKey) {
      return NextResponse.json(
        {
          success: false,
          message: "API key tidak valid atau tidak ditemukan",
          data: null
        },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const therapistId = searchParams.get('therapistId')
    const therapistName = searchParams.get('therapistName')
    const bulan = searchParams.get('bulan')
    const tahun = searchParams.get('tahun')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    console.log("🔍 Therapist payroll API called with params:", { 
      therapistId, therapistName, bulan, tahun, startDate, endDate 
    })

    // Validate required parameters
    if (!therapistId && !therapistName) {
      return NextResponse.json(
        {
          success: false,
          message: "therapistId atau therapistName harus diisi",
          data: null
        },
        { status: 400 }
      )
    }

    // Generate periode string dan date range
    const currentDate = new Date()
    const targetBulan = bulan ? parseInt(bulan) : (currentDate.getMonth() + 1)
    const targetTahun = tahun ? parseInt(tahun) : currentDate.getFullYear()
    
    // Gunakan custom date range atau fallback ke date range dari parameters
    let finalStartDate = startDate
    let finalEndDate = endDate
    let periode = ""

    if (startDate && endDate) {
      // Jika startDate dan endDate sudah ada dari request
      periode = `${startDate} - ${endDate}`
    } else {
      // Hitung custom date range berdasarkan aturan bisnis
      const { startDate: customStart, endDate: customEnd, periode: customPeriode } = calculateCustomPayrollDateRange(targetBulan, targetTahun)
      finalStartDate = customStart
      finalEndDate = customEnd
      periode = customPeriode
    }

    console.log("📅 Date range calculated:", {
      input: { bulan: targetBulan, tahun: targetTahun },
      output: { startDate: finalStartDate, endDate: finalEndDate, periode }
    })

    // Validasi nama terapis di database
    let employee = null
    let therapistData: any = null
    
    try {
      if (therapistName) {
        // Cari berdasarkan nama di database
        employee = await prisma.employee.findFirst({
          where: {
            user: {
              name: {
                contains: therapistName,
                mode: 'insensitive'
              }
            },
            status: 'ACTIVE'
          },
          include: {
            user: true,
            position: true,
            outlet: true
          }
        })
        
        console.log("🔍 Database search result for therapist:", {
          searchName: therapistName,
          found: !!employee,
          employeeName: employee?.user?.name
        })
      } else if (therapistId) {
        // Cari berdasarkan ID
        employee = await prisma.employee.findUnique({
          where: { id: therapistId },
          include: {
            user: true,
            position: true,
            outlet: true
          }
        })
      }

      if (!employee) {
        console.log("⚠️ Therapist not found in database:", { therapistName, therapistId })
        return NextResponse.json(
          {
            success: false,
            message: `Terapis dengan ${therapistName ? `nama "${therapistName}"` : `ID "${therapistId}"`} tidak ditemukan di database`,
            data: null
          },
          { status: 404 }
        )
      }

      // Ambil data dari API BreakTime dengan outlet spesifik employee
      console.log("📞 Calling BreakTime API for:", {
        therapist: employee.user.name,
        outlet: employee.outlet.name,
        code: employee.outlet.code
      })
      
      therapistData = await fetchTherapistDataFromBreakTime(
        employee.user.name, 
        finalStartDate || undefined,
        finalEndDate || undefined,
        targetBulan, 
        targetTahun,
        employee.outlet // Pass outlet data
      )
      
      // Override dengan data dari employee database
      therapistData.therapistId = employee.id
      therapistData.therapistName = employee.user.name
      therapistData.outletName = employee.outlet.name

    } catch (dbError) {
      console.error("❌ Database error:", dbError)
      return NextResponse.json(
        {
          success: false,
          message: "Terjadi kesalahan saat mengakses database",
          data: null
        },
        { status: 500 }
      )
    }

    // Prepare response data
    const responseData = {
      ...therapistData,
      periode,
      breakdown: {
        omzetPerHari: {
          [`${targetTahun}-${targetBulan.toString().padStart(2, '0')}-01`]: Math.floor(therapistData.summary.totalOmzet * 0.3),
          [`${targetTahun}-${targetBulan.toString().padStart(2, '0')}-02`]: Math.floor(therapistData.summary.totalOmzet * 0.4),
          [`${targetTahun}-${targetBulan.toString().padStart(2, '0')}-03`]: Math.floor(therapistData.summary.totalOmzet * 0.3)
        },
        komisiPerHari: {
          [`${targetTahun}-${targetBulan.toString().padStart(2, '0')}-01`]: Math.floor(therapistData.summary.totalKomisi * 0.3),
          [`${targetTahun}-${targetBulan.toString().padStart(2, '0')}-02`]: Math.floor(therapistData.summary.totalKomisi * 0.4),
          [`${targetTahun}-${targetBulan.toString().padStart(2, '0')}-03`]: Math.floor(therapistData.summary.totalKomisi * 0.3)
        },
        lemburPerHari: {
          [`${targetTahun}-${targetBulan.toString().padStart(2, '0')}-01`]: Math.floor(therapistData.summary.totalLembur * 0.2),
          [`${targetTahun}-${targetBulan.toString().padStart(2, '0')}-02`]: Math.floor(therapistData.summary.totalLembur * 0.5),
          [`${targetTahun}-${targetBulan.toString().padStart(2, '0')}-03`]: Math.floor(therapistData.summary.totalLembur * 0.3)
        }
      }
    }

    const response = {
      success: true,
      message: `Detail payroll terapis ${therapistData.therapistName} berhasil diambil untuk periode ${periode}`,
      data: responseData
    }

    console.log("✅ Therapist payroll response:", {
      therapistName: therapistData.therapistName,
      totalOmzet: therapistData.summary.totalOmzet,
      totalKomisi: therapistData.summary.totalKomisi,
      totalLembur: therapistData.summary.totalLembur
    })

    return NextResponse.json(response)

  } catch (error) {
    console.error("❌ Error in GET /api/payroll/therapist:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Terjadi kesalahan saat mengambil data payroll terapis",
        data: null
      },
      { status: 500 }
    )
  }
} 