import { CreateEmployeeInput, Employee, UpdateEmployeeInput, EmployeeResponse } from "@/types/employee"

export async function getEmployees(): Promise<Employee[]> {
  try {
    const response = await fetch("/api/employee")
    if (!response.ok) {
      throw new Error("Failed to fetch employees")
    }
    const { data } = await response.json() as EmployeeResponse
    if (!data || !Array.isArray(data)) {
      return []
    }
    return data as Employee[]
  } catch (error) {
    console.error("Error fetching employees:", error)
    throw error
  }
}

export async function createEmployee(input: CreateEmployeeInput): Promise<Employee> {
  try {
    console.log("Creating employee with data:", {
      ...input,
      password: "***" // Sembunyikan password di log
    })
    
    const response = await fetch("/api/employee", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(input),
    })
    
    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || "Failed to create employee")
    }
    
    const { data } = await response.json() as EmployeeResponse
    if (!data || Array.isArray(data)) {
      throw new Error("Invalid response data")
    }
    return data as Employee
  } catch (error) {
    console.error("Error creating employee:", error)
    throw error
  }
}

export async function updateEmployee(input: UpdateEmployeeInput): Promise<Employee> {
  try {
    const response = await fetch(`/api/employee/${input.id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(input),
    })
    if (!response.ok) {
      throw new Error("Failed to update employee")
    }
    const { data } = await response.json() as EmployeeResponse
    if (!data || Array.isArray(data)) {
      throw new Error("Invalid response data")
    }
    return data as Employee
  } catch (error) {
    console.error("Error updating employee:", error)
    throw error
  }
}

export async function deleteEmployee(id: string): Promise<{ success: boolean; message: string }> {
  if (!id) {
    return {
      success: false,
      message: "ID karyawan tidak valid"
    }
  }

  try {
    const response = await fetch(`/api/employee/${id}`, {
      method: "DELETE"
    })
    
    if (!response.ok) {
      const errorData = await response.json()
      return {
        success: false,
        message: errorData.message || "Gagal menghapus karyawan"
      }
    }

    const data = await response.json()
    return {
      success: true,
      message: "Karyawan berhasil dihapus"
    }
  } catch (error) {
    console.error("Error deleting employee:", error)
    return {
      success: false,
      message: "Terjadi kesalahan saat menghapus karyawan"
    }
  }
}

// Format nomor telepon
export function formatPhoneNumber(phoneNumber: string): string {
  // Hapus semua karakter non-digit
  const cleaned = phoneNumber.replace(/\D/g, "")
  
  // Format nomor telepon Indonesia
  if (cleaned.startsWith("62")) {
    return "+62 " + cleaned.slice(2).replace(/(\d{3})(\d{4})(\d{4})/, "$1-$2-$3")
  } else if (cleaned.startsWith("0")) {
    return "+62 " + cleaned.slice(1).replace(/(\d{3})(\d{4})(\d{4})/, "$1-$2-$3")
  }
  
  return phoneNumber
}

// Format tanggal ke format Indonesia
export function formatDate(date: Date): string {
  return new Date(date).toLocaleDateString("id-ID", {
    day: "numeric",
    month: "long",
    year: "numeric"
  })
} 