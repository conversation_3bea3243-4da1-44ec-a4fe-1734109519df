"use client"

import { useState, useEffect } from "react"
import { 
  Plus, 
  Pencil, 
  Trash2, 
  Search, 
  Calendar,
  Target,
  TrendingUp,
  ArrowUp,
  ArrowDown,
  DollarSign
} from "lucide-react"
import { toast } from "react-hot-toast"
import { TargetOmset } from "@/types/target-omset"
import { 
  formatCurrency, 
  formatDateRange, 
  calculateAchievementPercentage 
} from "@/services/target-omset"

export default function TargetOmsetPage() {
  const [targets, setTargets] = useState<TargetOmset[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingTarget, setEditingTarget] = useState<TargetOmset | null>(null)
  const [formData, setFormData] = useState({
    periodStart: new Date().toISOString().split("T")[0],
    periodEnd: new Date().toISOString().split("T")[0],
    targetAmount: 0,
    actualAmount: 0,
    notes: ""
  })

  // Fetch data
  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      const response = await fetch("/api/target-omset")
      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.message)
      }

      setTargets(result.data)
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Gagal mengambil data")
    } finally {
      setIsLoading(false)
    }
  }

  // Filter targets based on search query
  const filteredTargets = targets.filter(target =>
    target.notes.toLowerCase().includes(searchQuery.toLowerCase()) ||
    formatCurrency(target.targetAmount).includes(searchQuery) ||
    formatCurrency(target.actualAmount).includes(searchQuery)
  )

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const response = await fetch("/api/target-omset", {
        method: editingTarget ? "PUT" : "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(editingTarget ? {
          id: editingTarget.id,
          ...formData
        } : formData)
      })

      const result = await response.json()
      
      if (!result.success) {
        throw new Error(result.message)
      }

      toast.success(result.message)
      setIsModalOpen(false)
      setEditingTarget(null)
      setFormData({
        periodStart: new Date().toISOString().split("T")[0],
        periodEnd: new Date().toISOString().split("T")[0],
        targetAmount: 0,
        actualAmount: 0,
        notes: ""
      })
      fetchData()
    } catch (error) {
      toast.error(error instanceof Error ? error.message : "Gagal menyimpan data")
    }
  }

  // Handle target deletion
  const handleDelete = async (id: string) => {
    if (window.confirm("Apakah Anda yakin ingin menghapus target omset ini?")) {
      try {
        const response = await fetch("/api/target-omset", {
          method: "DELETE",
          headers: {
            "Content-Type": "application/json"
          },
          body: JSON.stringify({ id })
        })

        const result = await response.json()
        
        if (!result.success) {
          throw new Error(result.message)
        }

        toast.success(result.message)
        fetchData()
      } catch (error) {
        toast.error(error instanceof Error ? error.message : "Gagal menghapus data")
      }
    }
  }

  // Handle edit button click
  const handleEdit = (target: TargetOmset) => {
    setEditingTarget(target)
    setFormData({
      periodStart: new Date(target.periodStart).toISOString().split("T")[0],
      periodEnd: new Date(target.periodEnd).toISOString().split("T")[0],
      targetAmount: target.targetAmount,
      actualAmount: target.actualAmount,
      notes: target.notes
    })
    setIsModalOpen(true)
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h2 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-breaktime-primary to-breaktime-secondary bg-clip-text text-transparent">
          Target & Omset
        </h2>
        <button
          onClick={() => {
            setEditingTarget(null)
            setFormData({
              periodStart: new Date().toISOString().split("T")[0],
              periodEnd: new Date().toISOString().split("T")[0],
              targetAmount: 0,
              actualAmount: 0,
              notes: ""
            })
            setIsModalOpen(true)
          }}
          className="btn btn-sm sm:btn-md bg-gradient-to-r from-breaktime-primary to-breaktime-secondary text-white hover:from-breaktime-primary/90 hover:to-breaktime-secondary/90 border-0"
        >
          <Plus className="h-4 w-4 sm:h-5 sm:w-5" />
          <span className="hidden xs:inline">Tambah Target</span>
          <span className="inline xs:hidden">Tambah</span>
        </button>
      </div>

      {/* Search Bar */}
      <div className="form-control w-full sm:w-96">
        <div className="relative">
          <input
            type="text"
            placeholder="Cari berdasarkan keterangan atau nominal..."
            className="input input-bordered w-full pr-12 placeholder:text-base-content/50 text-base-content"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <button className="btn btn-ghost btn-sm absolute right-0 top-0 h-full px-3 hover:bg-transparent">
            <Search className="h-5 w-5 text-base-content/60" />
          </button>
        </div>
      </div>

      {/* Target Table */}
      <div className="overflow-x-auto bg-base-100 rounded-box shadow-sm border border-base-200">
        <table className="table table-sm sm:table-md">
          <thead className="bg-base-200/50 text-xs sm:text-sm">
            <tr>
              <th className="font-semibold text-base-content">Periode</th>
              <th className="font-semibold text-base-content">Target</th>
              <th className="font-semibold text-base-content">Realisasi</th>
              <th className="hidden sm:table-cell font-semibold text-base-content">Pencapaian</th>
              <th className="hidden sm:table-cell font-semibold text-base-content">Status</th>
              <th className="hidden md:table-cell font-semibold text-base-content">Keterangan</th>
              <th className="font-semibold text-base-content">Aksi</th>
            </tr>
          </thead>
          <tbody className="text-xs sm:text-sm">
            {isLoading ? (
              <tr>
                <td colSpan={7} className="text-center">
                  <span className="loading loading-spinner loading-md text-primary"></span>
                </td>
              </tr>
            ) : filteredTargets.length === 0 ? (
              <tr>
                <td colSpan={7} className="text-center text-base-content">
                  Tidak ada data target omset
                </td>
              </tr>
            ) : (
              filteredTargets.map((target) => (
                <tr key={target.id} className="hover:bg-base-200/30">
                  <td>
                    <div className="flex items-center gap-2 text-base-content">
                      <Calendar className="h-3 w-3 sm:h-4 sm:w-4" />
                      <span className="line-clamp-1">{formatDateRange(target.periodStart, target.periodEnd)}</span>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center gap-2">
                      <Target className="h-3 w-3 sm:h-4 sm:w-4 text-primary" />
                      <span className="font-medium text-base-content">{formatCurrency(target.targetAmount)}</span>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center gap-2">
                      <TrendingUp className="h-3 w-3 sm:h-4 sm:w-4 text-primary" />
                      <span className="font-medium text-base-content">{formatCurrency(target.actualAmount)}</span>
                    </div>
                  </td>
                  <td className="hidden sm:table-cell">
                    <div className="flex items-center gap-2">
                      {target.actualAmount >= target.targetAmount ? (
                        <>
                          <ArrowUp className="h-3 w-3 sm:h-4 sm:w-4 text-success" />
                          <span className="text-success font-medium">
                            {calculateAchievementPercentage(target.actualAmount, target.targetAmount)}%
                          </span>
                        </>
                      ) : (
                        <>
                          <ArrowDown className="h-3 w-3 sm:h-4 sm:w-4 text-error" />
                          <span className="text-error font-medium">
                            {calculateAchievementPercentage(target.actualAmount, target.targetAmount)}%
                          </span>
                        </>
                      )}
                    </div>
                  </td>
                  <td className="hidden sm:table-cell">
                    <span className={`badge badge-sm sm:badge-md ${
                      target.isAchieved
                        ? "bg-success/20 text-success border-success" 
                        : "bg-error/20 text-error border-error"
                    }`}>
                      {target.isAchieved ? "Tercapai" : "Belum"}
                    </span>
                  </td>
                  <td className="hidden md:table-cell">
                    <span className="text-base-content line-clamp-1">{target.notes}</span>
                  </td>
                  <td>
                    <div className="flex gap-1 sm:gap-2">
                      <button
                        onClick={() => handleEdit(target)}
                        className="btn btn-square btn-xs sm:btn-sm btn-ghost hover:bg-warning/20 text-warning"
                      >
                        <Pencil className="h-3 w-3 sm:h-4 sm:w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(target.id)}
                        className="btn btn-square btn-xs sm:btn-sm btn-ghost hover:bg-error/20 text-error"
                      >
                        <Trash2 className="h-3 w-3 sm:h-4 sm:w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Modal Form */}
      {isModalOpen && (
        <div className="modal modal-open">
          <div className="modal-box w-11/12 max-w-3xl">
            <h3 className="font-bold text-lg bg-gradient-to-r from-breaktime-primary to-breaktime-secondary bg-clip-text text-transparent">
              {editingTarget ? "Edit Target Omset" : "Tambah Target Omset"}
            </h3>
            <form onSubmit={handleSubmit} className="space-y-4 mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium text-base-content">Periode</span>
                  </label>
                  <div className="flex flex-col sm:flex-row gap-2">
                    <input
                      type="date"
                      className="input input-bordered flex-1 text-base-content"
                      value={formData.periodStart}
                      onChange={(e) => setFormData({ ...formData, periodStart: e.target.value })}
                      required
                    />
                    <input
                      type="date"
                      className="input input-bordered flex-1 text-base-content"
                      value={formData.periodEnd}
                      onChange={(e) => setFormData({ ...formData, periodEnd: e.target.value })}
                      required
                    />
                  </div>
                </div>
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium text-base-content">Target Omset</span>
                  </label>
                  <div className="join w-full">
                    <span className="btn join-item bg-base-200 border-base-300 text-base-content hover:bg-base-300">Rp</span>
                    <input
                      type="number"
                      className="input input-bordered join-item w-full text-base-content placeholder:text-base-content/50"
                      value={formData.targetAmount}
                      onChange={(e) => setFormData({ ...formData, targetAmount: parseInt(e.target.value) })}
                      placeholder="Masukkan target omset"
                      min="0"
                      required
                    />
                  </div>
                </div>
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium text-base-content">Realisasi Omset</span>
                  </label>
                  <div className="join w-full">
                    <span className="btn join-item bg-base-200 border-base-300 text-base-content hover:bg-base-300">Rp</span>
                    <input
                      type="number"
                      className="input input-bordered join-item w-full text-base-content placeholder:text-base-content/50"
                      value={formData.actualAmount}
                      onChange={(e) => setFormData({ ...formData, actualAmount: parseInt(e.target.value) })}
                      placeholder="Masukkan realisasi omset"
                      min="0"
                      required
                    />
                  </div>
                </div>
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium text-base-content">Keterangan</span>
                  </label>
                  <textarea
                    className="textarea textarea-bordered text-base-content placeholder:text-base-content/50"
                    value={formData.notes}
                    onChange={(e) => setFormData({ ...formData, notes: e.target.value })}
                    placeholder="Masukkan keterangan target omset"
                    required
                  />
                </div>
              </div>
              <div className="modal-action flex-wrap gap-2">
                <button 
                  type="button" 
                  className="btn btn-sm sm:btn-md bg-base-200 hover:bg-base-300 text-base-content border-base-300"
                  onClick={() => setIsModalOpen(false)}
                >
                  Batal
                </button>
                <button 
                  type="submit" 
                  className="btn btn-sm sm:btn-md bg-gradient-to-r from-breaktime-primary to-breaktime-secondary text-white hover:from-breaktime-primary/90 hover:to-breaktime-secondary/90 border-0"
                >
                  {editingTarget ? "Update" : "Simpan"}
                </button>
              </div>
            </form>
          </div>
          <div className="modal-backdrop bg-base-content/20" onClick={() => setIsModalOpen(false)}></div>
        </div>
      )}
    </div>
  )
} 