export enum Role {
  ADMIN = "ADMIN",
  MANAGER = "MANAGER",
  EMPLOYEE = "EMPLOYEE"
}

export enum EmployeeStatus {
  ACTIVE = "ACTIVE",
  INACTIVE = "INACTIVE"
}

export interface Salary {
  id: string
  employeeId: string
  periodStart: Date
  periodEnd: Date
  baseOmset: number
  totalOmset: number
  salaryAmount: number
  deductions: any
  bonuses: any
  finalAmount: number
  isPaid: boolean
  paidAt?: Date
  createdAt: Date
  updatedAt: Date
}

export interface SlipGaji {
  id: string
  employeeId: string
  periode: {
    bulan: number
    tahun: number
  }
  gaji: {
    pokok: number
    tunjangan: {
      transport: number
      pulsa: number
      makan: number
      bpjs: number
      penyesuaian: number
    }
    lembur: number
    bonus: number
    komisi: number
    omzet: number
    rasioOmzet: number
    potongan: {
      kasbon: number
      piutang: number
      bpjsTP: number
      bpjsTK: number
      lainnya: number
      keterangan: string
    }
    total: number
  }
  status: string
  createdAt: Date
  updatedAt: Date
}

export interface Employee {
  id: string
  userId: string
  user: {
    id: string
    name: string
    username: string
    department: string
    position: string
    role: Role
    joinDate: Date
  }
  positionId: string
  position: {
    id: string
    name: string
    omsetPercentage: number
    isKontrak: boolean
    targetKontrak: number
    gajiPokok: number
  }
  outletId: string
  nik: string
  alamat: string
  noTelp: string
  status: EmployeeStatus
  salaries?: Salary[]
  slipGaji?: SlipGaji[]
  createdAt: Date
  updatedAt: Date
}

export interface CreateEmployeeInput {
  name: string
  username: string
  password: string
  department: string
  position: string
  joinDate: Date
  positionId: string
  outletId: string
  alamat: string
  noTelp: string
  role?: Role
}

export interface UpdateEmployeeInput {
  id: string
  name?: string
  positionId?: string
  nik?: string
  alamat?: string
  noTelp?: string
  status?: EmployeeStatus
  outletId?: string
  joinDate?: Date | string
}

export interface EmployeeResponse {
  success: boolean
  message: string
  data?: Employee | Employee[]
  error?: string
} 