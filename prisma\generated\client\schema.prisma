// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
  output   = "./generated/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id         String    @id @default(cuid())
  username   String    @unique
  password   String
  name       String
  role       Role      @default(EMPLOYEE)
  department String
  position   String
  joinDate   DateTime
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  employee   Employee?
  kasbon     Kasbon[]
}

enum EmployeeStatus {
  ACTIVE
  INACTIVE
}

model Employee {
  id               String            @id @default(cuid())
  userId           String            @unique
  user             User              @relation(fields: [userId], references: [id])
  positionId       String
  position         Position          @relation(fields: [positionId], references: [id])
  nik              String            @unique
  alamat           String
  noTelp           String
  status           EmployeeStatus    @default(ACTIVE)
  salaries         Salary[]
  slipGaji         SlipGaji[]
  dailyCommissions DailyCommission[]
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  outletId         String
  outlet           Outlet            @relation(fields: [outletId], references: [id])
}

model Salary {
  id           String    @id @default(cuid())
  employeeId   String
  employee     Employee  @relation(fields: [employeeId], references: [id])
  periodStart  DateTime
  periodEnd    DateTime
  baseOmset    Float
  totalOmset   Float
  salaryAmount Float
  deductions   Json
  bonuses      Json
  finalAmount  Float
  isPaid       Boolean   @default(false)
  paidAt       DateTime?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
}

model Kasbon {
  id          String       @id @default(cuid())
  userId      String
  user        User         @relation(fields: [userId], references: [id])
  amount      Float
  description String
  status      KasbonStatus @default(PENDING)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
}

model SlipGaji {
  id         String     @id @default(cuid())
  employeeId String
  employee   Employee   @relation(fields: [employeeId], references: [id])
  periode    Json // { bulan: number, tahun: number }
  gaji       Json // { pokok, tunjangan, lembur, bonus, komisi, omzet, rasioOmzet, potongan: { kasbon, piutang, lainnya }, total }
  status     SlipStatus @default(DRAFT)
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt
}

enum Role {
  ADMIN
  MANAGER
  EMPLOYEE
}

enum KasbonStatus {
  PENDING
  APPROVED
  REJECTED
  PAID
}

enum SlipStatus {
  DRAFT
  PUBLISHED
  PAID
}

model Position {
  id              String     @id @default(cuid())
  name            String
  omsetPercentage Float      @default(0)
  gajiPokok       Float      @default(0)
  tunjangan       Json?
  isActive        Boolean    @default(true)
  isKontrak       Boolean    @default(false)
  targetKontrak   Float      @default(0)
  employees       Employee[]
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @default(now())

  @@map("position")
}

model DailyCommission {
  id         String   @id @default(cuid())
  employeeId String
  employee   Employee @relation(fields: [employeeId], references: [id])
  date       DateTime
  omzet      Float
  commission Float
  notes      String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @default(now())

  @@index([employeeId, date])
  @@map("daily_commission")
}

model Outlet {
  id        String     @id @default(cuid())
  name      String
  code      String     @unique
  employees Employee[]
  createdAt DateTime   @default(now())
  updatedAt DateTime   @default(now())

  @@map("outlet")
}
