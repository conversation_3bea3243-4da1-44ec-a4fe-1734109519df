import { PrismaClient } from './generated/client'
import * as fs from 'fs'
import * as path from 'path'

const prisma = new PrismaClient()

async function main() {
  try {
    // Baca file backup
    const backupPath = path.join(__dirname, '../backups/backup-2025-02-02T23-16-29-276Z.json')
    const backupData = JSON.parse(fs.readFileSync(backupPath, 'utf-8'))
    
    // Reset database
    console.log('Menghapus data existing...')
    await prisma.$transaction([
      prisma.dailyCommission.deleteMany(),
      prisma.slipGaji.deleteMany(),
      prisma.employee.deleteMany(),
      prisma.user.deleteMany(),
      prisma.position.deleteMany(),
      prisma.outlet.deleteMany(),
    ])

    console.log('Memulihkan data dari backup...')
    
    // Create default outlet first
    console.log('Membuat outlet default...')
    const outlet = await prisma.outlet.create({
      data: {
        name: 'Outlet Utama',
        code: `OUT-${Date.now()}`
      }
    })

    // Restore positions
    console.log('Memulihkan data posisi...')
    for (const position of backupData.data.positions) {
      await prisma.position.create({
        data: position
      })
    }

    // Restore users
    console.log('Memulihkan data users...')
    for (const user of backupData.data.users) {
      await prisma.user.create({
        data: user
      })
    }

    // Restore employees
    console.log('Memulihkan data karyawan...')
    for (const employee of backupData.data.employees) {
      await prisma.employee.create({
        data: {
          id: employee.id,
          nik: employee.nik,
          alamat: employee.alamat,
          noTelp: employee.noTelp,
          status: employee.status,
          createdAt: new Date(employee.createdAt),
          updatedAt: new Date(employee.updatedAt),
          user: {
            connect: {
              id: employee.userId
            }
          },
          position: {
            connect: {
              id: employee.positionId
            }
          },
          outlet: {
            connect: {
              id: outlet.id
            }
          }
        }
      })
    }

    // Restore slip gaji
    console.log('Memulihkan data slip gaji...')
    for (const slip of backupData.data.slipGajis) {
      await prisma.slipGaji.create({
        data: slip
      })
    }

    // Restore daily commissions
    console.log('Memulihkan data komisi harian...')
    for (const commission of backupData.data.dailyCommissions) {
      await prisma.dailyCommission.create({
        data: commission
      })
    }

    console.log('Pemulihan data selesai!')
  } catch (error) {
    console.error('Error:', error)
    process.exit(1)
  }
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  })
  .finally(async () => {
    await prisma.$disconnect()
  })
