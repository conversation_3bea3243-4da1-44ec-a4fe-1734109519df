import { NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"
import * as bcrypt from "bcryptjs"
import * as jose from 'jose'

const prisma = new PrismaClient()

const JWT_SECRET = process.env.JWT_SECRET
const JWT_EXPIRES_IN = process.env.JWT_EXPIRES_IN || "1d"

// Validasi environment variables
if (!JWT_SECRET) {
  console.error("JWT_SECRET tidak terdefinisi di environment variables")
  process.exit(1)
}

export async function POST(request: Request) {
  try {
    const { username, password } = await request.json()

    // Cari user berdasarkan username
    const user = await prisma.user.findUnique({
      where: { username },
      include: {
        employee: {
          include: {
            position: true,
            salaries: {
              select: {
                id: true,
                periodStart: true,
                periodEnd: true,
                baseOmset: true,
                totalOmset: true,
                salaryAmount: true,
                deductions: true,
                bonuses: true,
                finalAmount: true,
                isPaid: true,
                paidAt: true,
                createdAt: true,
                updatedAt: true,
              },
              orderBy: { createdAt: 'desc' },
              take: 1,
            }
          }
        }
      }
    })

    if (!user) {
      return NextResponse.json(
        { success: false, message: "Username atau password salah" },
        { status: 401 }
      )
    }

    // Verifikasi password
    const isPasswordValid = await bcrypt.compare(password, user.password)
    if (!isPasswordValid) {
      return NextResponse.json(
        { success: false, message: "Username atau password salah" },
        { status: 401 }
      )
    }

    // Generate JWT token menggunakan jose
    const secret = new TextEncoder().encode(JWT_SECRET)
    const token = await new jose.SignJWT({ 
      userId: user.id,
      username: user.username,
      role: user.role
    })
      .setProtectedHeader({ alg: 'HS256' })
      .setIssuedAt()
      .setExpirationTime(JWT_EXPIRES_IN)
      .sign(secret)

    if (!token) {
      throw new Error("Gagal membuat token JWT")
    }

    // Buat response dengan token dan user data
    const safeUserData = {
      id: user.id,
      username: user.username,
      name: user.name,
      role: user.role,
      department: user.department,
      position: user.position,
      joinDate: user.joinDate instanceof Date ? user.joinDate.toISOString() : user.joinDate,
      employee: user.employee ? {
        id: user.employee.id,
        nik: user.employee.nik,
        position: {
          name: user.employee.position.name,
          omsetPercentage: user.employee.position.omsetPercentage
        },
        status: user.employee.status,
        latestSalary: user.employee.salaries[0] ? {
          id: user.employee.salaries[0].id,
          periodStart: user.employee.salaries[0].periodStart.toISOString(),
          periodEnd: user.employee.salaries[0].periodEnd.toISOString(),
          salaryAmount: user.employee.salaries[0].salaryAmount
        } : null
      } : null
    }

    const response = NextResponse.json({
      success: true,
      token,
      user: safeUserData
    })

    // Set cookie menggunakan header secara manual
    response.headers.set(
      "set-cookie",
      `auth_token=${token}; HttpOnly; Path=/; SameSite=Strict; Max-Age=${24 * 60 * 60}${process.env.NODE_ENV === "production" ? "; Secure" : ""}`
    )

    return response
  } catch (error: any) {
    console.error("Detail error login:", {
      message: error.message,
      stack: error.stack,
      name: error.name
    })
    
    return NextResponse.json(
      {
        success: false,
        message: "Terjadi kesalahan sistem",
        error: process.env.NODE_ENV === "development" ? error.message : undefined
      },
      { status: 500 }
    )
  }
} 