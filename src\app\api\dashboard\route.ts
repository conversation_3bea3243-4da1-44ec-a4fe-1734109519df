import { NextResponse } from "next/server"
import prisma from "@/lib/prisma"
import { authMiddleware } from "@/lib/auth"

export async function GET(request: Request) {
  try {
    const auth = await authMiddleware(request)
    
    if (!auth) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    // Mendapatkan parameter bulan dan tahun dari URL
    const url = new URL(request.url)
    const month = parseInt(url.searchParams.get('month') || (new Date().getMonth() + 1).toString())
    const year = parseInt(url.searchParams.get('year') || new Date().getFullYear().toString())

    // Menentukan tanggal awal dan akhir bulan
    const startDate = new Date(year, month - 1, 1)
    const endDate = new Date(year, month, 0)

    // Mengambil data untuk stats cards dengan filter bulan
    const [
      totalOmset,
      totalKaryawan,
      totalKasbon,
      jabatanAktif,
      slipGajiBulanIni,
      komisiTertinggi,
      kasbonBelumLunas
    ] = await Promise.all([
      // Total Omset (dari daily commission)
      prisma.dailyCommission.aggregate({
        where: {
          date: {
            gte: startDate,
            lte: endDate
          }
        },
        _sum: {
          omzet: true
        }
      }),
      // Total Karyawan
      prisma.employee.count({
        where: {
          status: "ACTIVE"
        }
      }),
      // Total Kasbon
      prisma.kasbon.aggregate({
        where: {
          status: "PENDING",
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        },
        _sum: {
          amount: true
        }
      }),
      // Jabatan Aktif
      prisma.position.count({
        where: {
          isActive: true
        }
      }),
      // Slip Gaji Bulan Ini
      prisma.slipGaji.count({
        where: {
          periode: {
            path: ["bulan"],
            equals: month
          }
        }
      }),
      // Komisi Tertinggi
      prisma.dailyCommission.findFirst({
        where: {
          date: {
            gte: startDate,
            lte: endDate
          }
        },
        orderBy: {
          commission: 'desc'
        }
      }),
      // Kasbon Belum Lunas
      prisma.kasbon.count({
        where: {
          status: "PENDING",
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        }
      })
    ])

    // Mengambil data untuk grafik bulanan
    const monthlyData = await prisma.$transaction([
      // Data Omset Bulanan
      prisma.dailyCommission.groupBy({
        by: ['date'],
        where: {
          date: {
            gte: startDate,
            lte: endDate
          }
        },
        _sum: {
          omzet: true
        },
        orderBy: {
          date: 'asc'
        }
      }),
      // Data Gaji Bulanan
      prisma.slipGaji.findMany({
        where: {
          createdAt: {
            gte: startDate,
            lte: endDate
          }
        },
        orderBy: {
          createdAt: 'asc'
        }
      })
    ])

    // Mengelompokkan data gaji berdasarkan tanggal
    const dailyGaji = monthlyData[1].reduce((acc: { [key: string]: number }, slip) => {
      const gaji = slip.gaji as any
      const date = new Date(slip.createdAt).toISOString().split('T')[0]
      acc[date] = (acc[date] || 0) + (gaji.total || 0)
      return acc
    }, {})

    // Format data untuk response
    const formattedMonthlyData = monthlyData[0].map((day) => {
      const dateStr = day.date.toISOString().split('T')[0]
      const omsetValue = day._sum?.omzet || 0
      const gajiValue = dailyGaji[dateStr] || 0
      const persentase = omsetValue > 0 ? (gajiValue / omsetValue * 100) : 0
      
      return {
        name: day.date.toLocaleDateString('id-ID', { day: 'numeric', month: 'short' }),
        omset: omsetValue,
        totalGaji: gajiValue,
        persentaseGaji: persentase.toFixed(1)
      }
    })

    // Mengisi data kosong untuk hari tanpa transaksi
    const allDates = []
    for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
      const dateStr = d.toISOString().split('T')[0]
      const existingData = formattedMonthlyData.find(
        item => new Date(d).toLocaleDateString('id-ID', { day: 'numeric', month: 'short' }) === item.name
      )

      if (!existingData) {
        allDates.push({
          name: d.toLocaleDateString('id-ID', { day: 'numeric', month: 'short' }),
          omset: 0,
          totalGaji: 0,
          persentaseGaji: "0"
        })
      } else {
        allDates.push(existingData)
      }
    }

    // Mengambil data slip gaji terbaru
    const recentSlipGaji = await prisma.slipGaji.findMany({
      take: 5,
      orderBy: {
        createdAt: 'desc'
      },
      include: {
        employee: {
          include: {
            user: {
              select: {
                name: true
              }
            }
          }
        }
      }
    })

    const formattedRecentSlipGaji = recentSlipGaji.map(slip => {
      const gaji = slip.gaji as any // Type assertion untuk JSON field
      
      // Menghitung total tunjangan
      const totalTunjangan = typeof gaji.tunjangan === 'object' 
        ? Object.values(gaji.tunjangan).reduce((sum: number, value: any) => sum + (Number(value) || 0), 0)
        : Number(gaji.tunjangan) || 0

      return {
        id: slip.id,
        user: slip.employee.user.name,
        action: "Slip gaji diterbitkan",
        time: formatTimeAgo(slip.createdAt),
        amount: `Rp ${gaji.total.toLocaleString('id-ID')}`,
        gradient: "from-[#8b5cf6] to-[#7c3aed]",
        icon: "Receipt",
        category: "Slip Gaji",
        details: {
          gajiPokok: `Rp ${gaji.pokok.toLocaleString('id-ID')}`,
          komisi: `Rp ${gaji.komisi.toLocaleString('id-ID')}`,
          tunjangan: `Rp ${totalTunjangan.toLocaleString('id-ID')}`
        }
      }
    })

    // Menghitung perubahan persentase dengan filter bulan
    const lastMonthStart = new Date(year, month - 2, 1)
    const lastMonthEnd = new Date(year, month - 1, 0)

    const lastMonthOmset = await prisma.dailyCommission.aggregate({
      where: {
        date: {
          gte: lastMonthStart,
          lte: lastMonthEnd
        }
      },
      _sum: {
        omzet: true
      }
    })

    const omsetChange = lastMonthOmset._sum.omzet 
      ? ((totalOmset._sum.omzet || 0) - lastMonthOmset._sum.omzet) / lastMonthOmset._sum.omzet * 100
      : 0

    return NextResponse.json({
      statsCards: [
        {
          title: "Total Omset",
          value: `Rp ${(totalOmset._sum.omzet || 0).toLocaleString('id-ID')}`,
          change: `${omsetChange.toFixed(1)}%`,
          isIncrease: omsetChange > 0,
          icon: "TrendingUp",
          gradient: "from-breaktime-primary to-breaktime-secondary"
        },
        {
          title: "Total Karyawan",
          value: totalKaryawan.toString(),
          change: "0",
          isIncrease: true,
          icon: "Users",
          gradient: "from-[#FF8B42] to-[#FEB139]"
        },
        {
          title: "Target Bulanan",
          value: "85%",
          change: "0%",
          isIncrease: true,
          icon: "Target",
          gradient: "from-[#4CB9E7] to-[#3081D0]"
        },
        {
          title: "Total Kasbon",
          value: `Rp ${(totalKasbon._sum.amount || 0).toLocaleString('id-ID')}`,
          change: "0",
          isIncrease: false,
          icon: "Wallet",
          gradient: "from-[#FF6B6B] to-[#FF4949]"
        }
      ],
      quickStats: [
        {
          title: "Jabatan Aktif",
          value: jabatanAktif.toString(),
          icon: "Briefcase",
          gradient: "from-[#06b6d4] to-[#0891b2]"
        },
        {
          title: "Slip Gaji Bulan Ini",
          value: slipGajiBulanIni.toString(),
          icon: "Receipt",
          gradient: "from-[#8b5cf6] to-[#7c3aed]"
        },
        {
          title: "Komisi Tertinggi",
          value: `Rp ${(komisiTertinggi?.commission || 0).toLocaleString('id-ID')}`,
          icon: "DollarSign",
          gradient: "from-[#f43f5e] to-[#e11d48]"
        },
        {
          title: "Kasbon Belum Lunas",
          value: kasbonBelumLunas.toString(),
          icon: "AlertCircle",
          gradient: "from-[#f97316] to-[#ea580c]"
        }
      ],
      weeklyData: allDates,
      recentActivities: formattedRecentSlipGaji
    })
  } catch (error) {
    console.error("Dashboard Error:", error)
    return NextResponse.json(
      { message: "Internal Server Error" },
      { status: 500 }
    )
  }
}

// Helper function untuk format waktu
function formatTimeAgo(date: Date) {
  const now = new Date()
  const diffInMinutes = Math.floor((now.getTime() - date.getTime()) / (1000 * 60))
  
  if (diffInMinutes < 60) {
    return `${diffInMinutes} menit yang lalu`
  } else if (diffInMinutes < 1440) {
    const hours = Math.floor(diffInMinutes / 60)
    return `${hours} jam yang lalu`
  } else {
    const days = Math.floor(diffInMinutes / 1440)
    return `${days} hari yang lalu`
  }
} 