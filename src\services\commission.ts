import { DailyCommission, DailyCommissionResponse, MonthlyCommissionSummary } from "@/types/commission"

// Format currency
export const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat("id-ID", {
    style: "currency",
    currency: "IDR",
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Format date
export const formatDate = (date: Date) => {
  return new Date(date).toLocaleDateString("id-ID", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric"
  })
}

// Get nama bulan
export const getNamaBulan = (month: number) => {
  const date = new Date(2024, month - 1, 1)
  return date.toLocaleDateString("id-ID", { month: "long" })
}

// Get daily commissions by employee
export const getDailyCommissionsByEmployee = async (
  employeeId: string,
  month: number,
  year: number
): Promise<MonthlyCommissionSummary> => {
  console.log(`Memanggil API untuk: employeeId=${employeeId}, month=${month}, year=${year}`)
  
  try {
    const response = await fetch(
      `/api/commissions/employee/${employeeId}?month=${month}&year=${year}`
    )
    
    console.log(`Status respons API: ${response.status}`)
    
    const data = await response.json()
    console.log(`Respons API:`, data)
    
    if (!response.ok) {
      console.error(`Error API: ${data.error || "Kesalahan respons API"}`)
      throw new Error(data.error || "Failed to get daily commissions")
    }
    
    if (!data.success) {
      console.error(`Respons tidak berhasil: ${data.error || "Alasan tidak diketahui"}`)
      throw new Error(data.error || "Failed to get daily commissions")
    }
    
    // Pastikan struktur data sesuai dengan MonthlyCommissionSummary
    if (!data.data || typeof data.data.totalOmzet !== 'number') {
      console.warn("Data tidak sesuai format yang diharapkan:", data)
      // Kembalikan data default jika format tidak sesuai
      return {
        totalOmzet: 0,
        totalCommission: 0,
        dailyCommissions: [],
        month,
        year
      }
    }
    
    console.log(`Data komisi berhasil diterima: omzet=${data.data.totalOmzet}, komisi=${data.data.totalCommission}`)
    return data.data as MonthlyCommissionSummary
  } catch (error) {
    console.error("Error dalam getDailyCommissionsByEmployee:", error)
    // Daripada melempar exception, kembalikan data default
    // sehingga aplikasi tidak crash
    return {
      totalOmzet: 0,
      totalCommission: 0,
      dailyCommissions: [],
      month,
      year
    }
  }
}

// Get all daily commissions
export const getDailyCommissions = async (): Promise<DailyCommission[]> => {
  const response = await fetch("/api/commissions")
  const data = await response.json()

  if (!data.success) {
    throw new Error(data.error || "Failed to get commissions")
  }

  return data.data as DailyCommission[]
}

// Create daily commission
export const createDailyCommission = async (commission: {
  employeeId: string
  date: Date
  omzet: number
  commission: number
  notes?: string
}): Promise<DailyCommission> => {
  const response = await fetch("/api/commissions", {
    method: "POST",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify(commission)
  })
  const data = await response.json()

  if (!data.success) {
    throw new Error(data.error || "Failed to create commission")
  }

  return data.data as DailyCommission
}

// Update daily commission
export const updateDailyCommission = async (commission: {
  id: string
  omzet: number
  commission: number
  notes?: string
}): Promise<DailyCommission> => {
  const response = await fetch("/api/commissions", {
    method: "PUT",
    headers: {
      "Content-Type": "application/json"
    },
    body: JSON.stringify(commission)
  })
  const data = await response.json()

  if (!data.success) {
    throw new Error(data.error || "Failed to update commission")
  }

  return data.data as DailyCommission
}

// Delete daily commission
export const deleteDailyCommission = async (id: string): Promise<void> => {
  const response = await fetch(`/api/commissions?id=${id}`, {
    method: "DELETE"
  })
  const data = await response.json()

  if (!data.success) {
    throw new Error(data.error || "Failed to delete commission")
  }
}

// Calculate commission
export const calculateCommission = (omzet: number, percentage: number) => {
  return (omzet * percentage) / 100
} 