generator client {
  provider = "prisma-client-js"
  output   = "../node_modules/.prisma/client"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id         String    @id @default(cuid())
  username   String    @unique
  password   String
  name       String
  role       Role      @default(EMPLOYEE)
  department String
  position   String
  joinDate   DateTime
  createdAt  DateTime  @default(now())
  updatedAt  DateTime  @updatedAt
  employee   Employee?
  kasbon     Kasbon[]
}

model Employee {
  id               String            @id @default(cuid())
  userId           String            @unique
  positionId       String
  nik              String            @unique
  alamat           String
  noTelp           String
  status           EmployeeStatus    @default(ACTIVE)
  createdAt        DateTime          @default(now())
  updatedAt        DateTime          @updatedAt
  outletId         String
  outlet           Outlet            @relation(fields: [outletId], references: [id])
  position         Position          @relation(fields: [positionId], references: [id])
  user             User              @relation(fields: [userId], references: [id])
  salaries         Salary[]
  slipGaji         SlipGaji[]
  dailyCommissions DailyCommission[]
}

model Salary {
  id           String    @id @default(cuid())
  employeeId   String
  periodStart  DateTime
  periodEnd    DateTime
  baseOmset    Float
  totalOmset   Float
  salaryAmount Float
  deductions   Json
  bonuses      Json
  finalAmount  Float
  isPaid       Boolean   @default(false)
  paidAt       DateTime?
  createdAt    DateTime  @default(now())
  updatedAt    DateTime  @updatedAt
  employee     Employee  @relation(fields: [employeeId], references: [id])
}

model Kasbon {
  id          String       @id @default(cuid())
  userId      String
  amount      Float
  type        String      @default("KASBON")
  description String
  status      KasbonStatus @default(PENDING)
  createdAt   DateTime     @default(now())
  updatedAt   DateTime     @updatedAt
  user        User         @relation(fields: [userId], references: [id])
}

model SlipGaji {
  id         String     @id @default(cuid())
  employeeId String
  periode    Json
  gaji       Json
  status     SlipStatus @default(DRAFT)
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt
  employee   Employee   @relation(fields: [employeeId], references: [id])
}

model Position {
  id              String     @id @default(cuid())
  name            String
  omsetPercentage Float      @default(0)
  gajiPokok       Float      @default(0)
  tunjangan       Json?
  isActive        Boolean    @default(true)
  isKontrak       Boolean    @default(false)
  targetKontrak   Float      @default(0)
  createdAt       DateTime   @default(now())
  updatedAt       DateTime   @default(now())
  employees       Employee[]

  @@map("position")
}

model DailyCommission {
  id         String   @id @default(cuid())
  employeeId String
  date       DateTime
  omzet      Float
  commission Float
  notes      String?
  createdAt  DateTime @default(now())
  updatedAt  DateTime @default(now())
  employee   Employee @relation(fields: [employeeId], references: [id])

  @@index([employeeId, date])
  @@map("daily_commission")
}

model Outlet {
  id        String     @id @default(cuid())
  name      String
  code      String     @unique
  createdAt DateTime   @default(now())
  updatedAt DateTime   @default(now())
  employees Employee[]

  @@map("outlet")
}

enum EmployeeStatus {
  ACTIVE
  INACTIVE
}

enum Role {
  ADMIN
  MANAGER
  EMPLOYEE
}

enum KasbonStatus {
  PENDING
  APPROVED
  REJECTED
  PAID
}

enum SlipStatus {
  DRAFT
  PUBLISHED
  PAID
}
