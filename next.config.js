/** @type {import('next').NextConfig} */
const nextConfig = {
  webpack: (config, { isServer }) => {
    // Mengabaikan file HTML di node_modules
    config.module.rules.push({
      test: /\.html$/,
      include: /node_modules/,
      type: 'javascript/auto',
    })

    return config
  },
  typescript: {
    // Sementara mengabaikan error TypeScript untuk production build
    ignoreBuildErrors: true,
  },
}

module.exports = nextConfig 