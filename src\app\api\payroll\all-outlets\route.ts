import { NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"
import { calculateCustomPayrollDateRange } from "@/services/payroll-api"

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  }
})

// API untuk mengambil data payroll dari semua outlet
export async function GET(request: Request) {
  try {
    // Extract API key from headers
    const apiKey = request.headers.get('x-api-key')
    const expectedApiKey = process.env.PAYROLL_API_KEY || "c4a1c8fdcde5513a3feeb9b8462ada8909a4a54af4211ea11efaa87fa9eefb56"
    
    // Validate API key
    if (!apiKey || apiKey !== expectedApiKey) {
      return NextResponse.json(
        {
          success: false,
          message: "API key tidak valid atau tidak ditemukan",
          data: null
        },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const therapistName = searchParams.get('therapistName')
    const bulan = searchParams.get('bulan')
    const tahun = searchParams.get('tahun')
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')

    console.log("🌐 All outlets payroll API called with params:", { 
      therapistName, bulan, tahun, startDate, endDate 
    })

    // Validate required parameters
    if (!therapistName) {
      return NextResponse.json(
        {
          success: false,
          message: "therapistName harus diisi",
          data: null
        },
        { status: 400 }
      )
    }

    // Generate periode string dan date range
    const currentDate = new Date()
    const targetBulan = bulan ? parseInt(bulan) : (currentDate.getMonth() + 1)
    const targetTahun = tahun ? parseInt(tahun) : currentDate.getFullYear()
    
    // Gunakan custom date range atau fallback ke date range dari parameters
    let finalStartDate = startDate
    let finalEndDate = endDate
    let periode = ""

    if (startDate && endDate) {
      // Jika startDate dan endDate sudah ada dari request
      periode = `${startDate} - ${endDate}`
    } else {
      // Hitung custom date range berdasarkan aturan bisnis
      const { startDate: customStart, endDate: customEnd, periode: customPeriode } = calculateCustomPayrollDateRange(targetBulan, targetTahun)
      finalStartDate = customStart
      finalEndDate = customEnd
      periode = customPeriode
    }

    console.log("📅 All outlets date range calculated:", {
      input: { bulan: targetBulan, tahun: targetTahun },
      output: { startDate: finalStartDate, endDate: finalEndDate, periode }
    })

    try {
      // Ambil semua outlet dari database
      const outlets = await prisma.outlet.findMany({
        include: {
          employees: {
            where: {
              user: {
                name: {
                  contains: therapistName,
                  mode: 'insensitive'
                }
              },
              status: 'ACTIVE'
            },
            include: {
              user: true,
              position: true
            }
          }
        }
      })

      console.log("🏢 Found outlets:", outlets.map(o => ({ name: o.name, code: o.code, employees: o.employees.length })))

      // Cari terapis di SEMUA outlet (tidak berhenti di outlet pertama)
      const foundEmployees: Array<{employee: any, outlet: any}> = []

      for (const outlet of outlets) {
        for (const employee of outlet.employees) {
          if (employee.user.name.toLowerCase().includes(therapistName.toLowerCase())) {
            foundEmployees.push({ employee, outlet })
          }
        }
      }

      if (foundEmployees.length === 0) {
        console.log("⚠️ Therapist not found in any outlet:", { therapistName })
        return NextResponse.json(
          {
            success: false,
            message: `Terapis dengan nama "${therapistName}" tidak ditemukan di outlet manapun`,
            data: null
          },
          { status: 404 }
        )
      }

      console.log("✅ Found therapist in outlets:", foundEmployees.map(fe => ({
        name: fe.employee.user.name,
        outlet: fe.outlet.name,
        code: fe.outlet.code
      })))

      // 🚀 STRATEGI BARU: Panggil BreakTime API langsung dengan nama terapis 
      // Biarkan BreakTime API yang handle agregasi internal dari semua outlet
      const apiKey = "c4a1c8fdcde5513a3feeb9b8462ada8909a4a54af4211ea11efaa87fa9eefb56"
      
      console.log("🚀 NEW STRATEGY: Calling BreakTime API directly with therapist name (auto-aggregation)")

      // Build query parameters untuk BreakTime API (tanpa outlet spesifik)
      const queryParams = new URLSearchParams({
        therapistName: therapistName.trim() // Gunakan nama asli dari request
      })

      // Prioritas menggunakan date range jika tersedia, fallback ke bulan/tahun
      if (finalStartDate && finalEndDate) {
        queryParams.append('startDate', finalStartDate)
        queryParams.append('endDate', finalEndDate)
        console.log("📅 Direct API call with custom date range:", { startDate: finalStartDate, endDate: finalEndDate })
      } else {
        queryParams.append('bulan', targetBulan.toString())
        queryParams.append('tahun', targetTahun.toString())
        console.log("📅 Direct API call with month/year:", { bulan: targetBulan, tahun: targetTahun })
      }

      console.log("📤 Calling BreakTime API directly:", `https://www.mybreaktime.co.id/api/payroll/therapist?${queryParams}`)

      let payrollApiData = null
      try {
        const breakTimeResponse = await fetch(`https://www.mybreaktime.co.id/api/payroll/therapist?${queryParams}`, {
          method: 'GET',
          headers: {
            'x-api-key': apiKey,
            'Content-Type': 'application/json'
          }
        })

        if (!breakTimeResponse.ok) {
          throw new Error(`BreakTime API error: ${breakTimeResponse.status} - ${breakTimeResponse.statusText}`)
        }

        const breakTimeData = await breakTimeResponse.json()
        console.log("✅ BreakTime API Direct Response:", {
          success: breakTimeData.success,
          therapistName: breakTimeData.data?.therapistName,
          outletName: breakTimeData.data?.outletName,
          totalOmzet: breakTimeData.data?.summary?.totalOmzet || 0,
          totalKomisi: breakTimeData.data?.summary?.totalKomisi || 0,
          totalLembur: breakTimeData.data?.summary?.totalLembur || 0,
          dataStructure: Object.keys(breakTimeData.data || {})
        })
        
        if (!breakTimeData.success || !breakTimeData.data) {
          throw new Error(`BreakTime API error: ${breakTimeData.message || 'Invalid response structure'}`)
        }

        payrollApiData = breakTimeData.data

      } catch (apiError) {
        console.error("❌ BreakTime API Direct Call failed:", apiError)
        throw new Error(`Gagal mengakses BreakTime API: ${apiError instanceof Error ? apiError.message : 'Unknown error'}`)
      }

      // Buat payroll data dari hasil BreakTime API langsung (sudah teragregasi)
      const primaryEmployee = foundEmployees[0].employee
      const payrollData = {
        therapistId: primaryEmployee.id,
        therapistName: payrollApiData.therapistName || primaryEmployee.user.name,
        outletName: payrollApiData.outletName || `Data Teragregasi dari ${foundEmployees.length} outlet`,
        outletCode: foundEmployees.map(fe => fe.outlet.code).join(', '),
        isActive: payrollApiData.isActive !== undefined ? payrollApiData.isActive : true,
        summary: {
          totalOmzet: payrollApiData.summary?.totalOmzet || 0,
          totalKomisi: payrollApiData.summary?.totalKomisi || 0,
          totalLembur: payrollApiData.summary?.totalLembur || 0,
          totalLemburMinutes: payrollApiData.summary?.totalLemburMinutes || 0,
          totalTransaksi: payrollApiData.summary?.totalTransaksi || 0,
          averageOmzetPerTransaksi: payrollApiData.summary?.averageOmzetPerTransaksi || 0,
          averageKomisiPerTransaksi: payrollApiData.summary?.averageKomisiPerTransaksi || 0
        },
        transactions: payrollApiData.transactions || [],
        outletsInfo: foundEmployees.map(fe => ({
          outletName: fe.outlet.name,
          outletCode: fe.outlet.code
        }))
      }

      // Prepare response
      const responseData = {
        ...payrollData,
        periode,
        breakdown: {
          omzetPerHari: generateDailyBreakdown(payrollData.summary.totalOmzet, targetBulan, targetTahun),
          komisiPerHari: generateDailyBreakdown(payrollData.summary.totalKomisi, targetBulan, targetTahun),
          lemburPerHari: generateDailyBreakdown(payrollData.summary.totalLembur, targetBulan, targetTahun)
        }
      }

      const response = {
        success: true,
        message: `Detail payroll terapis ${payrollData.therapistName} berhasil diaggregasi dari ${foundEmployees.length} outlet untuk periode ${periode}`,
        data: responseData
      }

      console.log("✅ All outlets payroll response (DIRECT API):", {
        therapistName: payrollData.therapistName,
        outletsCount: foundEmployees.length,
        outletNames: foundEmployees.map(fe => fe.outlet.name),
        totalOmzet: payrollData.summary.totalOmzet,
        totalKomisi: payrollData.summary.totalKomisi,
        totalLembur: payrollData.summary.totalLembur,
        apiOutletName: payrollApiData.outletName,
        outletsInfo: payrollData.outletsInfo
      })

      return NextResponse.json(response)

    } catch (dbError) {
      console.error("❌ Database error:", dbError)
      return NextResponse.json(
        {
          success: false,
          message: "Terjadi kesalahan saat mengakses database",
          data: null
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error("❌ Error in GET /api/payroll/all-outlets:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Terjadi kesalahan saat mengambil data payroll dari semua outlet",
        data: null
      },
      { status: 500 }
    )
  }
}



// Helper function untuk generate daily breakdown
function generateDailyBreakdown(total: number, bulan: number, tahun: number) {
  const daysInMonth = new Date(tahun, bulan, 0).getDate()
  const breakdown: Record<string, number> = {}
  
  // Distribute total across days with some variation
  for (let day = 1; day <= Math.min(daysInMonth, 10); day++) {
    const dayStr = `${tahun}-${bulan.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`
    const percentage = Math.random() * 0.3 + 0.05 // 5-35% per day
    breakdown[dayStr] = Math.floor(total * percentage)
  }
  
  return breakdown
} 