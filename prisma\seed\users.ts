import { PrismaClient, Role } from "../generated/client"
import * as bcrypt from "bcrypt"

const prisma = new PrismaClient()

async function main() {
  try {
    console.log("Mulai seeding users dan employees...")

    // Hapus data lama
    await prisma.slipGaji.deleteMany()
    await prisma.salary.deleteMany()
    await prisma.employee.deleteMany()
    await prisma.user.deleteMany()

    // Buat admin
    const adminPassword = await bcrypt.hash("admin123", 10)
    const admin = await prisma.user.create({
      data: {
        username: "admin",
        password: adminPassword,
        name: "Administrator",
        role: Role.ADMIN,
        department: "Management",
        position: "Administrator",
        joinDate: new Date("2024-01-01")
      }
    })
    console.log("✅ Admin created:", admin.name)

    // Ambil data posisi dari database
    const positions = await prisma.position.findMany()
    console.log("Available positions:", positions)

    // Buat outlet default
    const outlet = await prisma.outlet.create({
      data: {
        name: "Outlet Utama",
        code: `OUT-${Date.now()}`
      }
    })
    console.log("✅ Outlet created:", outlet.name)

    // Data dummy untuk karyawan
    const employees = [
      {
        username: "terapis1",
        password: "terapis123",
        name: "Terapis Satu",
        role: Role.EMPLOYEE,
        department: "Terapis",
        position: "Terapis",
        joinDate: new Date("2024-01-01"),
        employee: {
          nik: "T001",
          alamat: "Jl. Terapis No. 1",
          noTelp: "081234567890",
          position: {
            connect: {
              id: positions.find(p => p.name === "Terapis")?.id
            }
          }
        }
      },
      {
        username: "terapis2",
        password: "terapis123",
        name: "Terapis Dua",
        role: Role.EMPLOYEE,
        department: "Terapis",
        position: "Senior Terapis",
        joinDate: new Date("2024-01-01"),
        employee: {
          nik: "T002",
          alamat: "Jl. Terapis No. 2",
          noTelp: "081234567891",
          position: {
            connect: {
              id: positions.find(p => p.name === "Senior Terapis")?.id
            }
          }
        }
      },
      {
        username: "kasir1",
        password: "kasir123",
        name: "Kasir Satu",
        role: Role.EMPLOYEE,
        department: "Kasir",
        position: "Kasir",
        joinDate: new Date("2024-01-01"),
        employee: {
          nik: "K001",
          alamat: "Jl. Kasir No. 1",
          noTelp: "081234567892",
          position: {
            connect: {
              id: positions.find(p => p.name === "Kasir")?.id
            }
          }
        }
      }
    ]

    // Buat user dan employee
    for (const employeeData of employees) {
      const hashedPassword = await bcrypt.hash(employeeData.password, 10)
      const { employee, ...userData } = employeeData

      const user = await prisma.user.create({
        data: {
          ...userData,
          password: hashedPassword,
          employee: {
            create: {
              ...employee,
              outlet: {
                connect: {
                  id: outlet.id
                }
              }
            }
          }
        },
        include: {
          employee: true
        }
      })
      console.log("✅ Employee created:", user.name)
    }

    console.log("✅ Seeding users dan employees selesai!")
  } catch (error) {
    console.error("❌ Error saat seeding users dan employees:", error)
    throw error
  } finally {
    await prisma.$disconnect()
  }
}

main()
  .catch((e) => {
    console.error(e)
    process.exit(1)
  }) 