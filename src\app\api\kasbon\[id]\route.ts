import { NextResponse } from "next/server"
import prisma from "@/lib/prisma"
import { authMiddleware } from "@/lib/auth"

export const dynamic = 'force-dynamic'
export const revalidate = 0

export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const auth = await authMiddleware(request)
    if (!auth) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    const { id } = params
    const body = await request.json()

    if (!id) {
      return NextResponse.json(
        { message: "ID kasbon tidak valid" },
        { status: 400 }
      )
    }

    // Validasi status
    if (!body.status || !["PENDING", "APPROVED", "REJECTED", "PAID"].includes(body.status)) {
      return NextResponse.json(
        { message: "Status tidak valid" },
        { status: 400 }
      )
    }

    try {
      const kasbon = await prisma.kasbon.update({
        where: { id },
        data: {
          status: body.status
        }
      })

      return NextResponse.json({
        message: "Status kasbon berhasil diubah",
        data: kasbon
      })
    } catch (error) {
      console.error("Database Error:", error)
      return NextResponse.json(
        { 
          message: "Gagal mengubah status kasbon",
          error: error instanceof Error ? error.message : "Unknown error"
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error("Server Error:", error)
    return NextResponse.json(
      { message: "Internal Server Error" },
      { status: 500 }
    )
  }
}

export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const auth = await authMiddleware(request)
    if (!auth) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    const { id } = params

    if (!id) {
      return NextResponse.json(
        { message: "ID kasbon tidak valid" },
        { status: 400 }
      )
    }

    try {
      // Cek apakah kasbon exists
      const existingKasbon = await prisma.kasbon.findUnique({
        where: { id }
      })

      if (!existingKasbon) {
        return NextResponse.json(
          { message: "Kasbon tidak ditemukan" },
          { status: 404 }
        )
      }

      // Hapus kasbon
      await prisma.kasbon.delete({
        where: { id }
      })

      return NextResponse.json({
        message: "Kasbon berhasil dihapus"
      })
    } catch (error) {
      console.error("Database Error:", error)
      return NextResponse.json(
        { 
          message: "Gagal menghapus kasbon",
          error: error instanceof Error ? error.message : "Unknown error"
        },
        { status: 500 }
      )
    }
  } catch (error) {
    console.error("Server Error:", error)
    return NextResponse.json(
      { message: "Internal Server Error" },
      { status: 500 }
    )
  }
} 