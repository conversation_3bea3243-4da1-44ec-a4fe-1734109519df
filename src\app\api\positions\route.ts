import { NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"

const prisma = new PrismaClient()

// GET /api/positions
export async function GET() {
  try {
    const positions = await prisma.position.findMany({
      orderBy: {
        name: 'asc'
      }
    })
    return NextResponse.json(positions)
  } catch (error) {
    console.error("Error fetching positions:", error)
    return NextResponse.json(
      { error: "Terjadi kesalahan saat mengambil data jabatan" },
      { status: 500 }
    )
  }
}

// POST /api/positions
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const position = await prisma.position.create({
      data: {
        name: body.name,
        omsetPercentage: body.omsetPercentage,
        gajiPokok: body.gajiPokok,
        tunjangan: body.tunjangan,
        isActive: true,
        isKontrak: body.isKontrak,
        targetKontrak: body.targetKontrak
      }
    })
    return NextResponse.json(position)
  } catch (error) {
    console.error("Error creating position:", error)
    return NextResponse.json(
      { error: "Te<PERSON><PERSON><PERSON> kesalahan saat membuat jabatan" },
      { status: 500 }
    )
  }
} 