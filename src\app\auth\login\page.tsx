"use client"

import { useState } from "react"
import { motion, AnimatePresence } from "framer-motion"
import { useRouter } from "next/navigation"
import { toast } from "react-hot-toast"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { 
  User, 
  Lock, 
  LogIn, 
  EyeOff, 
  Eye,
  Clock, 
  Building2, 
  Wallet,
  AlertCircle,
  Sparkles
} from "lucide-react"

// Validation Schema
const loginSchema = z.object({
  username: z
    .string()
    .min(3, "Username minimal 3 karakter")
    .max(50, "Username maksimal 50 karakter")
    .regex(/^[a-zA-Z0-9_]+$/, "Username hanya boleh berisi huruf, angka, dan underscore"),
  password: z
    .string()
    .min(6, "Password minimal 6 karakter")
    .max(50, "Password maksimal 50 karakter")
})

type LoginFormData = z.infer<typeof loginSchema>

const backgroundVariants = {
  initial: { scale: 1, rotate: 0 },
  animate: {
    scale: [1, 1.05, 1],
    rotate: [0, 2, -2, 0],
    transition: {
      duration: 15,
      repeat: Infinity,
      ease: "easeInOut"
    }
  }
}

const containerVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      duration: 0.6,
      staggerChildren: 0.1
    }
  }
}

const itemVariants = {
  hidden: { opacity: 0, y: 20 },
  visible: {
    opacity: 1,
    y: 0,
    transition: {
      type: "spring",
      stiffness: 300,
      damping: 24
    }
  }
}

export default function LoginPage() {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)
  const [username, setUsername] = useState("")
  const [password, setPassword] = useState("")
  
  const {
    register,
    handleSubmit,
    formState: { errors }
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
    mode: "onChange"
  })

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      setIsLoading(true)
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ username, password }),
      })

      if (!response.ok) {
        const errorData = await response.json()
        toast.error(errorData.message || "Gagal login")
        return
      }

      const data = await response.json()
      if (data.success) {
        toast.success("Login berhasil")
        router.push("/dashboard")
      } else {
        toast.error(data.message || "Gagal login")
      }
    } catch (error) {
      console.error("Error during login:", error)
      toast.error("Terjadi kesalahan saat login")
    } finally {
      setIsLoading(false)
    }
  }

  const togglePassword = () => setShowPassword(!showPassword)

  return (
    <div className="min-h-screen bg-gradient-to-br from-primary/5 via-base-100 to-secondary/5 flex items-center justify-center p-4 relative overflow-hidden">
      {/* Animated Background */}
      <div className="absolute inset-0 overflow-hidden">
        <motion.div
          variants={backgroundVariants}
          initial="initial"
          animate="animate"
          className="absolute -top-1/2 -left-1/2 w-[150%] h-[150%] bg-[radial-gradient(circle,rgba(var(--primary-rgb),0.1)1%,transparent_80%)]"
        />
        <motion.div
          variants={backgroundVariants}
          initial="initial"
          animate="animate"
          className="absolute -bottom-1/2 -right-1/2 w-[150%] h-[150%] bg-[radial-gradient(circle,rgba(var(--secondary-rgb),0.1)1%,transparent_80%)]"
        />
      </div>

      <div className="container mx-auto flex justify-center items-center gap-16">
        {/* Feature Cards */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="hidden lg:flex flex-col gap-6 max-w-sm"
        >
          <motion.div variants={itemVariants} className="card bg-base-100/80 backdrop-blur shadow-xl hover:shadow-2xl transition-all duration-300 border border-base-200">
            <div className="card-body">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg">
                  <Clock className="w-6 h-6 text-primary" />
                </div>
                <div>
                  <h3 className="font-bold text-lg text-black">Absensi Real-time</h3>
                  <p className="text-sm text-black/70">Monitor kehadiran karyawan secara langsung</p>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div variants={itemVariants} className="card bg-base-100/80 backdrop-blur shadow-xl hover:shadow-2xl transition-all duration-300 border border-base-200">
            <div className="card-body">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-secondary/10 rounded-lg">
                  <Building2 className="w-6 h-6 text-secondary" />
                </div>
                <div>
                  <h3 className="font-bold text-lg text-black">Manajemen Departemen</h3>
                  <p className="text-sm text-black/70">Kelola struktur organisasi dengan mudah</p>
                </div>
              </div>
            </div>
          </motion.div>

          <motion.div variants={itemVariants} className="card bg-base-100/80 backdrop-blur shadow-xl hover:shadow-2xl transition-all duration-300 border border-base-200">
            <div className="card-body">
              <div className="flex items-center gap-3">
                <div className="p-2 bg-accent/10 rounded-lg">
                  <Wallet className="w-6 h-6 text-accent" />
                </div>
                <div>
                  <h3 className="font-bold text-lg text-black">Penggajian Otomatis</h3>
                  <p className="text-sm text-black/70">Hitung dan proses gaji dengan cepat</p>
                </div>
              </div>
            </div>
          </motion.div>
        </motion.div>

        {/* Login Form */}
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
          className="card w-full max-w-md bg-white shadow-2xl backdrop-blur-lg border border-base-200"
        >
          <div className="card-body p-8">
            <motion.div variants={itemVariants} className="flex flex-col items-center mb-8">
              <div className="relative">
                <div className="avatar placeholder mb-4">
                  <div className="bg-gradient-to-br from-primary via-secondary to-accent text-primary-content rounded-xl w-24 h-24">
                    <span className="text-3xl font-bold text-white">BT</span>
                  </div>
                </div>
                <motion.div
                  animate={{
                    scale: [1, 1.2, 1],
                    rotate: [0, 360],
                  }}
                  transition={{
                    duration: 3,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                  className="absolute -top-2 -right-2"
                >
                  <Sparkles className="w-6 h-6 text-primary" />
                </motion.div>
              </div>
              <h2 className="text-2xl font-bold text-center bg-gradient-to-r from-primary via-secondary to-accent bg-clip-text text-transparent">
                Breaktime
              </h2>
              <p className="text-black/70 text-sm mt-2">Sistem Penggajian Modern</p>
            </motion.div>

            <form onSubmit={handleLogin} className="space-y-6">
              <AnimatePresence mode="popLayout">
                <motion.div key="username-field" variants={itemVariants} className="form-control">
                  <div className="relative">
                    <div className="absolute left-3 top-1/2 -translate-y-1/2">
                      <User className="w-5 h-5 text-black/50" />
                    </div>
                    <input 
                      type="text"
                      placeholder="Username"
                      className={`input input-bordered w-full pl-12 bg-white text-black placeholder:text-black/40 focus:bg-white transition-all duration-300 ${
                        errors.username ? "input-error" : "focus:input-primary"
                      }`}
                      {...register("username")}
                      value={username}
                      onChange={(e) => setUsername(e.target.value)}
                    />
                  </div>
                  {errors.username && (
                    <motion.label
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="label"
                    >
                      <span className="label-text-alt text-error flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.username.message}
                      </span>
                    </motion.label>
                  )}
                </motion.div>

                <motion.div key="password-field" variants={itemVariants} className="form-control">
                  <div className="relative">
                    <div className="absolute left-3 top-1/2 -translate-y-1/2">
                      <Lock className="w-5 h-5 text-black/50" />
                    </div>
                    <input 
                      type={showPassword ? "text" : "password"}
                      placeholder="Password"
                      className={`input input-bordered w-full pl-12 pr-12 bg-white text-black placeholder:text-black/40 focus:bg-white transition-all duration-300 ${
                        errors.password ? "input-error" : "focus:input-primary"
                      }`}
                      {...register("password")}
                      value={password}
                      onChange={(e) => setPassword(e.target.value)}
                    />
                    <button 
                      type="button"
                      onClick={togglePassword}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-black/60 hover:text-primary transition-colors"
                    >
                      {showPassword ? (
                        <EyeOff className="w-5 h-5" />
                      ) : (
                        <Eye className="w-5 h-5" />
                      )}
                    </button>
                  </div>
                  {errors.password && (
                    <motion.label
                      initial={{ opacity: 0, y: -10 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, y: -10 }}
                      className="label"
                    >
                      <span className="label-text-alt text-error flex items-center gap-1">
                        <AlertCircle className="w-4 h-4" />
                        {errors.password.message}
                      </span>
                    </motion.label>
                  )}
                  <label className="label justify-end">
                    <a href="#" className="label-text-alt link link-hover text-primary font-medium">Lupa password?</a>
                  </label>
                </motion.div>

                <motion.div key="submit-button" variants={itemVariants} className="form-control mt-6">
                  <button 
                    type="submit"
                    className="btn btn-primary w-full hover:brightness-110 hover:shadow-lg hover:shadow-primary/20 transition-all duration-300"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <span className="flex items-center gap-2">
                        <span className="loading loading-spinner loading-sm"></span>
                        Loading...
                      </span>
                    ) : (
                      <span className="flex items-center gap-2">
                        <LogIn className="w-5 h-5" />
                        Masuk
                      </span>
                    )}
                  </button>
                </motion.div>
              </AnimatePresence>
            </form>
          </div>
        </motion.div>
      </div>
    </div>
  )
} 