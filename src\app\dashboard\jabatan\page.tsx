"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Plus, Pencil, Trash2, Search, Copy } from "lucide-react"
import { Position } from "@/types/position"
import { getPositions, createPosition, updatePosition, deletePosition, duplicatePosition } from "@/services/position"
import { toast } from "react-hot-toast"

// Helper function untuk format angka dengan titik
function formatNumber(value: number | undefined | null): string {
  if (value === undefined || value === null) return "0"
  return value.toLocaleString('id-ID')
}

// Helper function untuk parse string angka dengan titik
function parseNumber(value: string): number {
  return Number(value.replace(/[^\d]/g, ''))
}

interface FormData {
  name: string
  omsetPercentage: number | null
  gajiPokok: number
  tunjangan: {
    transport: number
    pulsa: number
    makan: number
  }
  isActive: boolean
  isKontrak: boolean
  targetKontrak: number
}

export default function PositionPage() {
  const [positions, setPositions] = useState<Position[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [deletingPosition, setDeletingPosition] = useState<string | null>(null)
  const [editingPosition, setEditingPosition] = useState<Position | null>(null)
  const [formData, setFormData] = useState<FormData>({
    name: "",
    omsetPercentage: null,
    gajiPokok: 0,
    tunjangan: {
      transport: 0,
      pulsa: 0,
      makan: 0
    },
    isActive: true,
    isKontrak: false,
    targetKontrak: 0
  })
  const [openTunjanganId, setOpenTunjanganId] = useState<string | null>(null)

  // Add click outside listener
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      if (!target.closest('.tunjangan-dropdown') && !target.closest('.tunjangan-button')) {
        setOpenTunjanganId(null)
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [])

  // Fetch positions
  useEffect(() => {
    fetchPositions()
  }, [])

  const fetchPositions = async () => {
    try {
      const data = await getPositions()
      setPositions(data)
    } catch (error) {
      toast.error("Gagal mengambil data jabatan")
    } finally {
      setIsLoading(false)
    }
  }

  // Filter positions based on search query
  const filteredPositions = positions.filter(position => 
    position.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    (position.isKontrak ? 'kontrak' : 'tetap').includes(searchQuery.toLowerCase()) ||
    formatNumber(position.gajiPokok).includes(searchQuery) ||
    formatNumber(position.omsetPercentage).includes(searchQuery)
  )

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      const positionData = {
        name: formData.name,
        omsetPercentage: formData.isKontrak ? 0 : (formData.omsetPercentage === null ? 0 : Number(formData.omsetPercentage)),
        gajiPokok: Number(formData.gajiPokok),
        tunjangan: {
          transport: Number(formData.tunjangan.transport),
          pulsa: Number(formData.tunjangan.pulsa),
          makan: Number(formData.tunjangan.makan)
        },
        isKontrak: formData.isKontrak,
        targetKontrak: formData.isKontrak ? Number(formData.targetKontrak) : 0
      }

      if (editingPosition) {
        await updatePosition({
          id: editingPosition.id,
          ...positionData
        })
        toast.success("Jabatan berhasil diupdate")
      } else {
        await createPosition(positionData)
        toast.success("Jabatan berhasil dibuat")
      }
      
      setIsModalOpen(false)
      setEditingPosition(null)
      setFormData({ 
        name: "", 
        omsetPercentage: null,
        gajiPokok: 0,
        tunjangan: {
          transport: 0,
          pulsa: 0,
          makan: 0
        },
        isActive: true,
        isKontrak: false,
        targetKontrak: 0
      })
      fetchPositions()
    } catch (error) {
      toast.error("Gagal menyimpan jabatan")
    }
  }

  // Handle position deletion
  const handleDelete = (id: string) => {
    setDeletingPosition(id)
    setIsDeleteModalOpen(true)
  }

  const handleConfirmDelete = async () => {
    if (!deletingPosition) return

    try {
      const position = positions.find(p => p.id === deletingPosition)
      if (!position) {
        toast.error("Jabatan tidak ditemukan")
        return
      }

      const response = await fetch(`/api/positions/${deletingPosition}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json'
        }
      })

      const data = await response.json()

      if (response.ok) {
        toast.success("Jabatan berhasil dihapus")
        fetchPositions()
      } else {
        toast.error(data.message || "Gagal menghapus jabatan")
      }
    } catch (error) {
      console.error("Error deleting position:", error)
      toast.error("Terjadi kesalahan saat menghapus jabatan")
    } finally {
      setIsDeleteModalOpen(false)
      setDeletingPosition(null)
    }
  }

  // Handle edit button click
  const handleEdit = (position: Position) => {
    setEditingPosition(position)
    setFormData({
      name: position.name,
      omsetPercentage: position.omsetPercentage || null,
      gajiPokok: position.gajiPokok || 0,
      tunjangan: {
        transport: position.tunjangan?.transport || 0,
        pulsa: position.tunjangan?.pulsa || 0,
        makan: position.tunjangan?.makan || 0
      },
      isActive: position.isActive,
      isKontrak: position.isKontrak ?? false,
      targetKontrak: position.targetKontrak || 0
    })
    setIsModalOpen(true)
  }

  // Tambahkan console.log untuk debugging
  useEffect(() => {
    if (editingPosition) {
      console.log('Editing Position:', editingPosition)
      console.log('Form Data:', formData)
    }
  }, [editingPosition, formData])

  // Handle toggle status
  const handleToggleStatus = async (position: Position) => {
    try {
      await fetch(`/api/positions/${position.id}/status`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          isActive: !position.isActive
        })
      })
      toast.success("Status jabatan berhasil diupdate")
      fetchPositions()
    } catch (error) {
      toast.error("Gagal mengupdate status jabatan")
    }
  }

  // Handle number input change with format
  const handleNumberChange = (value: string, field: keyof FormData, subfield?: string) => {
    const numericValue = value.replace(/\D/g, '')
    const numberValue = numericValue === '' ? 0 : Number(numericValue)
    
    if (subfield) {
      setFormData({
        ...formData,
        tunjangan: {
          ...formData.tunjangan,
          [subfield]: numberValue
        }
      })
    } else {
      setFormData({
        ...formData,
        [field]: numberValue
      })
    }
  }

  const handleNestedNumberInput = (value: string, field: keyof FormData, subfield: string) => {
    const numberValue = Number(value.replace(/[^0-9]/g, ""))
    if (field === "tunjangan") {
      setFormData({
        ...formData,
        tunjangan: {
          ...formData.tunjangan,
          [subfield]: numberValue
        }
      })
    }
  }

  // Tambahkan fungsi untuk menangani duplikasi
  const handleDuplicate = async (position: Position) => {
    try {
      await duplicatePosition(position)
      toast.success("Jabatan berhasil diduplikasi")
      fetchPositions()
    } catch (error) {
      toast.error("Gagal menduplikasi jabatan")
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h2 className="text-2xl font-bold bg-gradient-to-r from-breaktime-primary to-breaktime-secondary bg-clip-text text-transparent">
          Manajemen Jabatan
        </h2>
        <button
          onClick={() => {
            setEditingPosition(null)
            setFormData({
              name: "",
              omsetPercentage: null,
              gajiPokok: 0,
              tunjangan: {
                transport: 0,
                pulsa: 0,
                makan: 0
              },
              isActive: true,
              isKontrak: false,
              targetKontrak: 0
            })
            setIsModalOpen(true)
          }}
          className="btn bg-gradient-to-r from-breaktime-primary to-breaktime-secondary text-white hover:from-breaktime-primary/90 hover:to-breaktime-secondary/90 border-0"
        >
          <Plus className="h-5 w-5" />
          Tambah Jabatan
        </button>
      </div>

      {/* Search Bar */}
      <div className="form-control">
        <div className="relative">
          <input
            type="text"
            placeholder="Cari berdasarkan nama jabatan, status, gaji pokok, atau persentase omset..."
            className="input input-bordered w-full pr-12 placeholder:text-base-content/50 text-base-content"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <button className="btn btn-ghost btn-sm absolute right-0 top-0 h-full px-3 hover:bg-transparent">
            <Search className="h-5 w-5 text-base-content/60" />
          </button>
        </div>
      </div>

      {/* Positions Table */}
      <div className="overflow-x-auto bg-base-100 rounded-box shadow-sm border border-base-200">
        <table className="table">
          <thead className="bg-base-200/50">
            <tr>
              <th className="font-semibold text-base-content">Nama Jabatan</th>
              <th className="font-semibold text-base-content">Status</th>
              <th className="font-semibold text-base-content">Gaji Pokok</th>
              <th className="font-semibold text-base-content">Persentase Omset</th>
              <th className="font-semibold text-base-content">Target Kontrak</th>
              <th className="font-semibold text-base-content">Status Aktif</th>
              <th className="font-semibold text-base-content">Aksi</th>
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              <tr>
                <td colSpan={7} className="text-center">
                  <span className="loading loading-spinner loading-md text-primary"></span>
                </td>
              </tr>
            ) : filteredPositions.length === 0 ? (
              <tr>
                <td colSpan={7} className="text-center text-base-content">
                  {searchQuery ? "Tidak ada jabatan yang sesuai dengan pencarian" : "Tidak ada data jabatan"}
                </td>
              </tr>
            ) : (
              filteredPositions.map((position) => (
                <tr key={position.id} className="hover:bg-base-200/30">
                  <td className="font-medium text-base-content">
                    <div className="flex items-center gap-2">
                      {position.name}
                      <div className="relative">
                        <button
                          className="btn btn-ghost btn-xs btn-circle text-info hover:bg-info/10 tunjangan-button"
                          onClick={(e) => {
                            e.stopPropagation()
                            setOpenTunjanganId(openTunjanganId === position.id ? null : position.id)
                          }}
                          title="Lihat Tunjangan"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </button>
                        {openTunjanganId === position.id && (
                          <div className="absolute z-50 top-full left-0 mt-2 w-72 p-4 bg-base-100 rounded-lg shadow-lg border border-base-300 tunjangan-dropdown">
                            <div className="space-y-2">
                              <div className="flex items-center justify-between mb-3">
                                <h4 className="font-medium text-base-content">Detail Kompensasi</h4>
                                <button
                                  className="btn btn-ghost btn-xs btn-circle hover:bg-base-200"
                                  onClick={() => setOpenTunjanganId(null)}
                                >
                                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                                  </svg>
                                </button>
                              </div>

                              {/* Gaji Pokok */}
                              <div className="flex justify-between items-center py-2 border-b border-base-200">
                                <span className="text-base-content/70">Gaji Pokok:</span>
                                <span className="font-medium text-base-content">
                                  Rp {formatNumber(position.gajiPokok || 0)}
                                </span>
                              </div>

                              {/* Rasio Omzet/Target */}
                              <div className="flex justify-between items-center py-2 border-b border-base-200">
                                <span className="text-base-content/70">
                                  {position.isKontrak ? 'Target Kontrak:' : 'Rasio Omzet:'}
                                </span>
                                <span className="font-medium text-base-content">
                                  {position.isKontrak 
                                    ? `Rp ${formatNumber(position.targetKontrak || 0)}`
                                    : (position.omsetPercentage === null || position.omsetPercentage === 0) ? "-" : `${position.omsetPercentage}%`
                                  }
                                </span>
                              </div>

                              <div className="divider text-xs text-base-content/50 my-1">Tunjangan</div>

                              <div className="flex justify-between items-center py-2 border-b border-base-200">
                                <span className="text-base-content/70">Transport:</span>
                                <span className="font-medium text-base-content">
                                  Rp {formatNumber(position.tunjangan?.transport || 0)}
                                </span>
                              </div>
                              <div className="flex justify-between items-center py-2 border-b border-base-200">
                                <span className="text-base-content/70">Pulsa:</span>
                                <span className="font-medium text-base-content">
                                  Rp {formatNumber(position.tunjangan?.pulsa || 0)}
                                </span>
                              </div>
                              <div className="flex justify-between items-center py-2 border-b border-base-200">
                                <span className="text-base-content/70">Makan:</span>
                                <span className="font-medium text-base-content">
                                  Rp {formatNumber(position.tunjangan?.makan || 0)}
                                </span>
                              </div>

                              <div className="divider text-xs text-base-content/50 my-1">Total</div>

                              <div className="flex justify-between items-center pt-2">
                                <span className="text-base-content font-medium">Total Tunjangan:</span>
                                <span className="font-bold text-primary">
                                  Rp {formatNumber(
                                    (position.tunjangan?.transport || 0) +
                                    (position.tunjangan?.pulsa || 0) +
                                    (position.tunjangan?.makan || 0)
                                  )}
                                </span>
                              </div>

                              <div className="flex justify-between items-center pt-2">
                                <span className="text-base-content font-medium">Total Kompensasi:</span>
                                <span className="font-bold text-success">
                                  Rp {formatNumber(
                                    (position.gajiPokok || 0) +
                                    (position.tunjangan?.transport || 0) +
                                    (position.tunjangan?.pulsa || 0) +
                                    (position.tunjangan?.makan || 0)
                                  )}
                                </span>
                              </div>

                              {position.isKontrak && (
                                <div className="mt-3 text-xs text-info">
                                  <span>* Gaji akan dihitung berdasarkan pencapaian target</span>
                                </div>
                              )}
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </td>
                  <td>
                    <span className={`badge ${
                      position.isKontrak
                        ? "bg-warning/20 text-warning border-warning"
                        : "bg-success/20 text-success border-success"
                    }`}>
                      {position.isKontrak ? "Kontrak" : "Tetap"}
                    </span>
                  </td>
                  <td className="text-base-content">
                    Rp {formatNumber(position.gajiPokok)}
                  </td>
                  <td className="text-base-content">
                    {position.isKontrak ? "-" : (position.omsetPercentage === null || position.omsetPercentage === 0) ? "-" : `${position.omsetPercentage}%`}
                  </td>
                  <td className="text-base-content">
                    {position.isKontrak ? `Rp ${formatNumber(position.targetKontrak)}` : "-"}
                  </td>
                  <td>
                    <span className={`badge ${
                      position.isActive
                        ? "bg-success/20 text-success border-success"
                        : "bg-error/20 text-error border-error"
                    }`}>
                      {position.isActive ? "Aktif" : "Tidak Aktif"}
                    </span>
                  </td>
                  <td>
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleEdit(position)}
                        className="btn btn-square btn-sm btn-ghost hover:bg-warning/20 text-warning"
                      >
                        <Pencil className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDuplicate(position)}
                        className="btn btn-square btn-sm btn-ghost hover:bg-info/20 text-info"
                      >
                        <Copy className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDelete(position.id)}
                        className="btn btn-square btn-sm btn-ghost hover:bg-error/20 text-error"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Modal Delete */}
      {isDeleteModalOpen && (
        <div className="modal modal-open">
          <div className="modal-box bg-base-100 border border-base-300">
            <h3 className="font-bold text-lg text-base-content">Konfirmasi Hapus</h3>
            <p className="py-4 text-base-content">Apakah Anda yakin ingin menghapus jabatan ini?</p>
            <div className="modal-action">
              <button
                onClick={() => {
                  setIsDeleteModalOpen(false)
                  setDeletingPosition(null)
                }}
                className="btn btn-ghost text-base-content hover:bg-base-200"
              >
                Batal
              </button>
              <button
                onClick={handleConfirmDelete}
                className="btn bg-error hover:bg-error/90 text-error-content border-none"
              >
                Hapus
              </button>
            </div>
          </div>
          <div className="modal-backdrop bg-base-300/50"></div>
        </div>
      )}

      {/* Modal Form */}
      {isModalOpen && (
        <div className="modal modal-open">
          <div className="modal-box bg-base-100 border border-base-300">
            <h3 className="font-bold text-lg bg-gradient-to-r from-breaktime-primary to-breaktime-secondary bg-clip-text text-transparent">
              {editingPosition ? "Edit Jabatan" : "Tambah Jabatan Baru"}
            </h3>
            <form onSubmit={handleSubmit} className="space-y-4 mt-4">
              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium text-base-content">Nama Jabatan</span>
                </label>
                <input
                  type="text"
                  className="input input-bordered text-base-content placeholder:text-base-content/50"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  placeholder="Masukkan nama jabatan"
                  required
                />
              </div>

              <div className="form-control">
                <label className="label cursor-pointer justify-start gap-2">
                  <input
                    type="checkbox"
                    className="checkbox checkbox-primary"
                    checked={formData.isKontrak}
                    onChange={(e) => setFormData(prev => ({
                      ...prev,
                      isKontrak: e.target.checked,
                      omsetPercentage: e.target.checked ? 0 : prev.omsetPercentage,
                      targetKontrak: e.target.checked ? prev.targetKontrak : 0
                    }))}
                  />
                  <span className="label-text text-base-content">Jabatan Kontrak</span>
                </label>
              </div>

              {!formData.isKontrak && (
                <div className="form-control w-full">
                  <label className="label">
                    <span className="label-text text-base-content">Persentase Omzet</span>
                  </label>
                  <div className="join">
                    <input
                      type="number"
                      step="0.01"
                      className="input input-bordered join-item w-full text-base-content placeholder:text-base-content/50"
                      value={formData.omsetPercentage === null ? "" : formData.omsetPercentage}
                      onChange={(e) => {
                        const value = e.target.value === "" ? null : parseFloat(e.target.value)
                        setFormData(prev => ({
                          ...prev,
                          omsetPercentage: value
                        }))
                      }}
                      placeholder="Masukkan persentase omzet"
                    />
                    <span className="btn join-item bg-base-200 border-base-300 text-base-content hover:bg-base-300">%</span>
                  </div>
                  <label className="label">
                    <span className="label-text-alt text-info">
                      Bisa dikosongkan jika tidak ada persentase omzet
                    </span>
                  </label>
                </div>
              )}

              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium text-base-content">Gaji Pokok</span>
                </label>
                <div className="join">
                  <span className="btn join-item bg-base-200 border-base-300 text-base-content hover:bg-base-300">Rp</span>
                  <input
                    type="text"
                    className="input input-bordered join-item w-full text-base-content placeholder:text-base-content/50"
                    value={formatNumber(formData.gajiPokok)}
                    onChange={(e) => handleNumberChange(e.target.value, 'gajiPokok')}
                    placeholder="Masukkan gaji pokok"
                    required
                  />
                </div>
              </div>

              {formData.isKontrak && (
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium text-base-content">Target Kontrak</span>
                  </label>
                  <div className="join">
                    <span className="btn join-item bg-base-200 border-base-300 text-base-content hover:bg-base-300">Rp</span>
                    <input
                      type="text"
                      className="input input-bordered join-item w-full text-base-content placeholder:text-base-content/50"
                      value={formatNumber(formData.targetKontrak)}
                      onChange={(e) => handleNumberChange(e.target.value, 'targetKontrak')}
                      placeholder="Masukkan target kontrak"
                      required={formData.isKontrak}
                    />
                  </div>
                  <label className="label">
                    <span className="label-text-alt text-info">
                      Gaji akan dihitung di halaman penggajian dengan rumus: (((omzet * 100) / target kontrak) * 0,01) * gaji pokok
                    </span>
                  </label>
                </div>
              )}

              <div className="divider text-sm text-base-content/70">Tunjangan</div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium text-base-content">Transport</span>
                </label>
                <div className="join">
                  <span className="btn join-item bg-base-200 border-base-300 text-base-content hover:bg-base-300">Rp</span>
                  <input
                    type="text"
                    className="input input-bordered join-item w-full text-base-content placeholder:text-base-content/50"
                    value={formatNumber(formData.tunjangan.transport)}
                    onChange={(e) => handleNumberChange(e.target.value, 'tunjangan', 'transport')}
                    placeholder="Masukkan tunjangan transport"
                    required
                  />
                </div>
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium text-base-content">Pulsa</span>
                </label>
                <div className="join">
                  <span className="btn join-item bg-base-200 border-base-300 text-base-content hover:bg-base-300">Rp</span>
                  <input
                    type="text"
                    className="input input-bordered join-item w-full text-base-content placeholder:text-base-content/50"
                    value={formatNumber(formData.tunjangan.pulsa)}
                    onChange={(e) => handleNumberChange(e.target.value, 'tunjangan', 'pulsa')}
                    placeholder="Masukkan tunjangan pulsa"
                    required
                  />
                </div>
              </div>

              <div className="form-control">
                <label className="label">
                  <span className="label-text font-medium text-base-content">Makan</span>
                </label>
                <div className="join">
                  <span className="btn join-item bg-base-200 border-base-300 text-base-content hover:bg-base-300">Rp</span>
                  <input
                    type="text"
                    className="input input-bordered join-item w-full text-base-content placeholder:text-base-content/50"
                    value={formatNumber(formData.tunjangan.makan)}
                    onChange={(e) => handleNumberChange(e.target.value, 'tunjangan', 'makan')}
                    placeholder="Masukkan tunjangan makan"
                    required
                  />
                </div>
              </div>

              <div className="modal-action">
                <button
                  type="button"
                  className="btn btn-outline hover:bg-base-200 hover:border-base-300 text-base-content"
                  onClick={() => {
                    setIsModalOpen(false)
                    setEditingPosition(null)
                    setFormData({ 
                      name: "", 
                      omsetPercentage: null,
                      gajiPokok: 0,
                      tunjangan: {
                        transport: 0,
                        pulsa: 0,
                        makan: 0
                      },
                      isActive: true,
                      isKontrak: false,
                      targetKontrak: 0
                    })
                  }}
                >
                  Batal
                </button>
                <button type="submit" className="btn bg-gradient-to-r from-breaktime-primary to-breaktime-secondary text-white hover:from-breaktime-primary/90 hover:to-breaktime-secondary/90 border-0">
                  Simpan
                </button>
              </div>
            </form>
          </div>
        </div>
      )}
    </div>
  )
} 