"use client"

import { useState, useEffect } from "react"
import { motion } from "framer-motion"
import { Plus, Pencil, Trash2, Search, Phone, MapPin, Calendar, Building } from "lucide-react"
import { Employee, EmployeeStatus, Role } from "@/types/employee"
import { Position } from "@/types/position"
import { Outlet } from "@/types/outlet"
import { getEmployees, createEmployee, updateEmployee, deleteEmployee, formatPhoneNumber, formatDate } from "@/services/employee"
import { getPositions } from "@/services/position"
import { toast } from "react-hot-toast"
import { getOutlets, createOutlet, updateOutlet, deleteOutlet } from "@/services/outlet"

// Fungsi untuk menghasilkan warna berdasar<PERSON> string
const stringToColor = (str: string) => {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    hash = str.charCodeAt(i) + ((hash << 5) - hash)
  }
  
  const colors = [
    { bg: 'bg-primary/10', text: 'text-primary', border: 'border-primary' },
    { bg: 'bg-secondary/10', text: 'text-secondary', border: 'border-secondary' },
    { bg: 'bg-accent/10', text: 'text-accent', border: 'border-accent' },
    { bg: 'bg-info/10', text: 'text-info', border: 'border-info' },
    { bg: 'bg-success/10', text: 'text-success', border: 'border-success' },
    { bg: 'bg-warning/10', text: 'text-warning', border: 'border-warning' },
    { bg: 'bg-error/10', text: 'text-error', border: 'border-error' },
    { bg: 'bg-pink-500/10', text: 'text-pink-500', border: 'border-pink-500' },
    { bg: 'bg-purple-500/10', text: 'text-purple-500', border: 'border-purple-500' },
    { bg: 'bg-indigo-500/10', text: 'text-indigo-500', border: 'border-indigo-500' },
    { bg: 'bg-blue-500/10', text: 'text-blue-500', border: 'border-blue-500' },
    { bg: 'bg-cyan-500/10', text: 'text-cyan-500', border: 'border-cyan-500' },
    { bg: 'bg-teal-500/10', text: 'text-teal-500', border: 'border-teal-500' },
    { bg: 'bg-emerald-500/10', text: 'text-emerald-500', border: 'border-emerald-500' },
    { bg: 'bg-lime-500/10', text: 'text-lime-500', border: 'border-lime-500' },
    { bg: 'bg-amber-500/10', text: 'text-amber-500', border: 'border-amber-500' },
    { bg: 'bg-orange-500/10', text: 'text-orange-500', border: 'border-orange-500' },
    { bg: 'bg-rose-500/10', text: 'text-rose-500', border: 'border-rose-500' }
  ]

  // Gunakan hash untuk memilih warna dari array
  const colorIndex = Math.abs(hash) % colors.length
  return `${colors[colorIndex].bg} ${colors[colorIndex].text} ${colors[colorIndex].border}`
}

// Fungsi untuk mendapatkan warna outlet
const getOutletColor = (outletName: string) => {
  if (!outletName) return 'bg-neutral/10 text-neutral border-neutral'
  return stringToColor(outletName)
}

// Fungsi untuk generate NIK
const generateNIK = (name: string, joinDate: string) => {
  const date = new Date(joinDate)
  const year = date.getFullYear().toString().slice(-2)
  const month = (date.getMonth() + 1).toString().padStart(2, '0')
  const day = date.getDate().toString().padStart(2, '0')
  const randomNum = Math.floor(1000 + Math.random() * 9000)
  const nameInitials = name.split(' ').map(word => word[0]).join('').toUpperCase()
  return `${year}${month}${day}${randomNum}${nameInitials}`
}

// Fungsi untuk generate username
const generateUsername = (name: string) => {
  return name.toLowerCase()
    .replace(/\s+/g, '.') // Ganti spasi dengan titik
    .replace(/[^a-z0-9.]/g, '') // Hapus karakter selain huruf, angka, dan titik
}

// Tambahkan fungsi debounce
const debounce = (func: Function, wait: number) => {
  let timeout: NodeJS.Timeout
  return (...args: any[]) => {
    clearTimeout(timeout)
    timeout = setTimeout(() => func(...args), wait)
  }
}

export default function EmployeePage() {
  const [employees, setEmployees] = useState<Employee[]>([])
  const [positions, setPositions] = useState<Position[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState("")
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [editingEmployee, setEditingEmployee] = useState<Employee | null>(null)
  const [formData, setFormData] = useState({
    name: "",
    role: "EMPLOYEE" as Role,
    positionId: "",
    alamat: "",
    noTelp: "",
    status: "ACTIVE" as EmployeeStatus,
    joinDate: new Date().toISOString().split("T")[0],
    outletId: ""
  })
  const [isOutletModalOpen, setIsOutletModalOpen] = useState(false)
  const [outlets, setOutlets] = useState<Outlet[]>([])
  const [outletFormData, setOutletFormData] = useState({
    name: "",
    code: ""
  })
  const [selectedOutlet, setSelectedOutlet] = useState<string>("")
  const [selectedEmployees, setSelectedEmployees] = useState<string[]>([])
  const [isDeleteModalOpen, setIsDeleteModalOpen] = useState(false)
  const [deletingEmployee, setDeletingEmployee] = useState<Employee | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [outletUpdates, setOutletUpdates] = useState<{[key: string]: Outlet}>({})
  const [isDeletingOutlet, setIsDeletingOutlet] = useState(false)
  const [deletingOutletId, setDeletingOutletId] = useState<string | null>(null)
  const [targetOutletId, setTargetOutletId] = useState<string>("")
  const [isOutletDeleteModalOpen, setIsOutletDeleteModalOpen] = useState(false)
  const [isConfirmDeleteOutletOpen, setIsConfirmDeleteOutletOpen] = useState(false)
  const [outletToDelete, setOutletToDelete] = useState<Outlet | null>(null)

  // Tambahkan debounced update
  const debouncedOutletUpdate = debounce(async (outlet: Outlet) => {
    try {
      await updateOutlet(outlet.id, { name: outlet.name, code: outlet.code })
      toast.success("Outlet berhasil diupdate")
      fetchData()
    } catch (error) {
      console.error("Error updating outlet:", error)
      toast.error(error instanceof Error ? error.message : "Gagal mengupdate outlet")
    }
  }, 1000)

  // Ganti handleOutletEdit
  const handleOutletEdit = (outlet: Outlet, field: keyof Outlet, value: string) => {
    const updatedOutlet = { ...outlet, [field]: value }
    setOutletUpdates(prev => ({ ...prev, [outlet.id]: updatedOutlet }))
    debouncedOutletUpdate(updatedOutlet)
  }

  // Fetch data
  useEffect(() => {
    fetchData()
  }, [])

  const fetchData = async () => {
    try {
      const [employeesData, positionsData, outletsData] = await Promise.all([
        getEmployees(),
        getPositions(),
        getOutlets()
      ])
      setEmployees(employeesData)
      setPositions(positionsData)
      setOutlets(outletsData)
    } catch (error) {
      toast.error("Gagal mengambil data")
    } finally {
      setIsLoading(false)
    }
  }

  // Filter employees based on search query
  const filteredEmployees = employees.filter(employee =>
    employee.user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
    employee.user.position.toLowerCase().includes(searchQuery.toLowerCase())
  )

  const resetForm = () => {
    setFormData({
      name: "",
      role: "EMPLOYEE" as Role,
      positionId: "",
      alamat: "",
      noTelp: "",
      status: "ACTIVE" as EmployeeStatus,
      joinDate: new Date().toISOString().split("T")[0],
      outletId: ""
    })
  }

  // Handle edit button click
  const handleEdit = (employee: Employee) => {
    setEditingEmployee(employee)
    setFormData({
      name: employee.user.name,
      role: employee.user.role,
      positionId: employee.positionId,
      alamat: employee.alamat,
      noTelp: employee.noTelp,
      status: employee.status,
      joinDate: new Date(employee.user.joinDate).toISOString().split("T")[0],
      outletId: employee.outletId
    })
    setIsModalOpen(true)
  }

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      // Validasi data
      if (!formData.name || !formData.positionId || !formData.alamat || !formData.noTelp || !formData.outletId || !formData.joinDate) {
        toast.error("Mohon lengkapi semua data")
        return
      }

      if (editingEmployee) {
        // Update existing employee
        await updateEmployee({
          id: editingEmployee.id,
          name: formData.name,
          positionId: formData.positionId,
          alamat: formData.alamat,
          noTelp: formData.noTelp,
          status: formData.status,
          outletId: formData.outletId,
          joinDate: new Date(formData.joinDate)
        })
        toast.success("Data karyawan berhasil diupdate")
      } else {
        // Create new employee
        const nik = generateNIK(formData.name, formData.joinDate)
        const username = generateUsername(formData.name)
        const password = `${username}${Math.floor(1000 + Math.random() * 9000)}`

        await createEmployee({
          name: formData.name,
          username: username,
          password: password,
          nik: nik,
          department: "Staff",
          position: formData.positionId,
          joinDate: new Date(formData.joinDate),
          positionId: formData.positionId,
          outletId: formData.outletId,
          alamat: formData.alamat,
          noTelp: formData.noTelp,
          role: "EMPLOYEE"
        })

        // Tampilkan informasi login untuk karyawan baru
        toast.success(
          <div>
            Karyawan berhasil ditambahkan<br/>
            Username: {username}<br/>
            Password: {password}<br/>
            NIK: {nik}
          </div>,
          {
            duration: 5000,
            style: {
              minWidth: '300px',
            },
          }
        )
      }
      
      setIsModalOpen(false)
      setEditingEmployee(null)
      resetForm()
      fetchData()
    } catch (error) {
      console.error("Error saving employee:", error)
      toast.error(error instanceof Error ? error.message : "Gagal menyimpan data karyawan")
    }
  }

  // Handle delete button click
  const handleDeleteClick = (employee: Employee) => {
    setDeletingEmployee(employee)
    setIsDeleteModalOpen(true)
  }

  // Handle delete confirmation
  const handleDelete = async () => {
    if (!deletingEmployee) {
      toast.error("Data karyawan tidak ditemukan")
      return
    }

    if (isDeleting) return // Prevent multiple clicks

    setIsDeleting(true)
    const toastId = "deleteEmployee"

    try {
      toast.loading("Menghapus karyawan...", { id: toastId })
      
      const result = await deleteEmployee(deletingEmployee.id)
      
      if (result.success) {
        toast.success(result.message, { id: toastId })
        setIsDeleteModalOpen(false)
        setDeletingEmployee(null)
        await fetchData()
      } else {
        toast.error(result.message, { id: toastId })
      }
    } catch (error) {
      console.error("Error deleting employee:", error)
      toast.error("Terjadi kesalahan sistem", { id: toastId })
    } finally {
      setIsDeleting(false)
    }
  }

  // Handle outlet form submission
  const handleOutletSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    try {
      if (outletFormData.name && outletFormData.code) {
        await createOutlet(outletFormData)
        toast.success("Outlet berhasil ditambahkan")
        setOutletFormData({ name: "", code: "" })
        fetchData()
      } else {
        toast.error("Mohon lengkapi semua data outlet")
      }
    } catch (error) {
      console.error("Error saving outlet:", error)
      toast.error(error instanceof Error ? error.message : "Gagal menyimpan outlet")
    }
  }

  // Handle outlet deletion
  const handleOutletDelete = async (id: string) => {
    const employeesInOutlet = employees.filter(emp => emp.outletId === id)
    const outlet = outlets.find(o => o.id === id)
    
    if (employeesInOutlet.length > 0) {
      setDeletingOutletId(id)
      setIsOutletDeleteModalOpen(true)
    } else {
      setOutletToDelete(outlet || null)
      setIsConfirmDeleteOutletOpen(true)
    }
  }

  // Handle confirm delete outlet
  const handleConfirmDeleteOutlet = async () => {
    if (!outletToDelete) return

    try {
      await deleteOutlet(outletToDelete.id)
      toast.success("Outlet berhasil dihapus")
      setIsConfirmDeleteOutletOpen(false)
      setOutletToDelete(null)
      fetchData()
    } catch (error) {
      console.error("Error deleting outlet:", error)
      toast.error(error instanceof Error ? error.message : "Gagal menghapus outlet")
    }
  }

  // Handle move employees and delete outlet
  const handleMoveEmployeesAndDeleteOutlet = async () => {
    if (!targetOutletId || !deletingOutletId) {
      toast.error("Pilih outlet tujuan terlebih dahulu")
      return
    }

    setIsDeletingOutlet(true)
    const toastId = "moveAndDeleteOutlet"

    try {
      toast.loading("Memindahkan karyawan dan menghapus outlet...", { id: toastId })

      // Pindahkan semua karyawan ke outlet baru
      const employeesToMove = employees.filter(emp => emp.outletId === deletingOutletId)
      
      await Promise.all(employeesToMove.map(async (employee) => {
        const response = await fetch(`/api/employee/${employee.id}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            id: employee.id,
            name: employee.user.name,
            positionId: employee.positionId,
            alamat: employee.alamat,
            noTelp: employee.noTelp,
            status: employee.status,
            outletId: targetOutletId,
            joinDate: employee.user.joinDate
          })
        })

        if (!response.ok) {
          const errorData = await response.json()
          throw new Error(errorData.message || `Gagal memindahkan karyawan: ${employee.user.name}`)
        }
      }))

      // Hapus outlet menggunakan service
      await deleteOutlet(deletingOutletId)

      toast.success("Karyawan berhasil dipindahkan dan outlet berhasil dihapus", { id: toastId })
      setIsOutletDeleteModalOpen(false)
      setDeletingOutletId(null)
      setTargetOutletId("")
      fetchData()
    } catch (error) {
      console.error("Error:", error)
      toast.error(error instanceof Error ? error.message : "Terjadi kesalahan", { id: toastId })
    } finally {
      setIsDeletingOutlet(false)
    }
  }

  // Tambahkan handler untuk bulk assign outlet
  const handleBulkAssignOutlet = async () => {
    if (!selectedOutlet || selectedEmployees.length === 0) {
      toast.error("Pilih outlet dan minimal satu karyawan")
      return
    }

    try {
      // Update setiap karyawan yang dipilih
      const updatePromises = selectedEmployees.map(async (employeeId) => {
        const response = await fetch(`/api/employee/${employeeId}`, {
          method: "PUT",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            outletId: selectedOutlet
          }),
        })

        const result = await response.json()
        if (!result.success) {
          throw new Error(`Gagal mengupdate karyawan: ${result.message}`)
        }
        return result
      })

      await Promise.all(updatePromises)
      
      toast.success(`${selectedEmployees.length} karyawan berhasil dipindahkan ke outlet baru`)
      setSelectedOutlet("")
      setSelectedEmployees([])
      fetchData()
    } catch (error) {
      console.error("Error updating employee outlets:", error)
      toast.error("Gagal memindahkan outlet karyawan")
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between">
        <h2 className="text-2xl font-bold bg-gradient-to-r from-breaktime-primary to-breaktime-secondary bg-clip-text text-transparent">
          Manajemen Karyawan
        </h2>
        <div className="flex gap-2">
          <button
            onClick={() => {
              setEditingEmployee(null)
              setFormData({
                name: "",
                role: "EMPLOYEE" as Role,
                positionId: "",
                alamat: "",
                noTelp: "",
                status: "ACTIVE" as EmployeeStatus,
                joinDate: new Date().toISOString().split("T")[0],
                outletId: ""
              })
              setIsModalOpen(true)
            }}
            className="btn bg-gradient-to-r from-breaktime-primary to-breaktime-secondary text-white hover:from-breaktime-primary/90 hover:to-breaktime-secondary/90 border-0"
          >
            <Plus className="h-5 w-5" />
            Tambah Karyawan
          </button>
          <button
            onClick={() => setIsOutletModalOpen(true)}
            className="btn bg-gradient-to-r from-breaktime-primary to-breaktime-secondary text-white hover:from-breaktime-primary/90 hover:to-breaktime-secondary/90 border-0"
          >
            <Plus className="h-5 w-5" />
            Kelola Outlet
          </button>
        </div>
      </div>

      {/* Search Bar */}
      <div className="form-control">
        <div className="relative">
          <input
            type="text"
            placeholder="Cari karyawan atau jabatan..."
            className="input input-bordered w-full pr-12 placeholder:text-base-content/50 text-base-content"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
          <button className="btn btn-ghost btn-sm absolute right-0 top-0 h-full px-3 hover:bg-transparent">
            <Search className="h-5 w-5 text-base-content/60" />
          </button>
        </div>
      </div>

      {/* Employees Table */}
      <div className="overflow-x-auto bg-base-100 rounded-box shadow-sm border border-base-200">
        <table className="table">
          <thead className="bg-base-200/50">
            <tr>
              <th className="font-semibold text-base-content">Nama Karyawan</th>
              <th className="font-semibold text-base-content">Jabatan</th>
              <th className="font-semibold text-base-content">Outlet</th>
              <th className="font-semibold text-base-content">Kontak</th>
              <th className="font-semibold text-base-content">Tanggal Bergabung</th>
              <th className="font-semibold text-base-content">Status</th>
              <th className="font-semibold text-base-content">Aksi</th>
            </tr>
          </thead>
          <tbody>
            {isLoading ? (
              <tr>
                <td colSpan={7} className="text-center">
                  <span className="loading loading-spinner loading-md text-primary"></span>
                </td>
              </tr>
            ) : filteredEmployees.length === 0 ? (
              <tr>
                <td colSpan={7} className="text-center text-base-content">
                  Tidak ada data karyawan
                </td>
              </tr>
            ) : (
              filteredEmployees.map((employee) => (
                <tr key={employee.id} className="hover:bg-base-200/30">
                  <td className="font-medium text-base-content">{employee.user.name}</td>
                  <td>
                    <div className="flex flex-col">
                      <span className="font-medium text-base-content">{employee.position.name}</span>
                      <span className="text-sm text-primary font-medium">Karyawan</span>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center gap-2">
                      <span className={`badge border ${getOutletColor(outlets.find(o => o.id === employee.outletId)?.name || '')}`}>
                        {outlets.find(o => o.id === employee.outletId)?.name || 'Tidak ada outlet'}
                      </span>
                    </div>
                  </td>
                  <td>
                    <div className="flex flex-col gap-1">
                      <div className="flex items-center gap-2 text-base-content">
                        <Phone className="h-4 w-4" />
                        <span className="text-sm">{formatPhoneNumber(employee.noTelp)}</span>
                      </div>
                      <div className="flex items-center gap-2 text-base-content">
                        <MapPin className="h-4 w-4" />
                        <span className="text-sm line-clamp-1">{employee.alamat}</span>
                      </div>
                    </div>
                  </td>
                  <td>
                    <div className="flex items-center gap-2 text-base-content">
                      <Calendar className="h-4 w-4" />
                      <span className="text-sm">{formatDate(employee.user.joinDate)}</span>
                    </div>
                  </td>
                  <td>
                    <span className={`badge ${
                      employee.status === 'ACTIVE'
                        ? "bg-success/20 text-success border-success" 
                        : "bg-error/20 text-error border-error"
                    }`}>
                      {employee.status === 'ACTIVE' ? "Aktif" : "Tidak Aktif"}
                    </span>
                  </td>
                  <td>
                    <div className="flex gap-2">
                      <button
                        onClick={() => handleEdit(employee)}
                        className="btn btn-square btn-sm btn-ghost hover:bg-warning/20 text-warning"
                      >
                        <Pencil className="h-4 w-4" />
                      </button>
                      <button
                        onClick={() => handleDeleteClick(employee)}
                        className="btn btn-square btn-sm btn-ghost hover:bg-error/20 text-error"
                      >
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Modal Form */}
      {isModalOpen && (
        <div className="modal modal-open">
          <div className="modal-box bg-base-100 border border-base-300">
            <h3 className="text-xl font-bold bg-gradient-to-r from-breaktime-primary to-breaktime-secondary bg-clip-text text-transparent mb-6">
              {editingEmployee ? "Edit Data Karyawan" : "Tambah Karyawan Baru"}
            </h3>
            <form onSubmit={handleSubmit} className="space-y-5">
              <div className="form-control">
                <label className="text-base text-base-content/80 mb-2">
                  Nama Karyawan
                </label>
                <input
                  type="text"
                  className="input input-bordered w-full bg-base-100 text-base-content"
                  value={formData.name}
                  onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                  required
                />
              </div>

              <div className="form-control">
                <label className="text-base text-base-content/80 mb-2">
                  Jabatan
                </label>
                <select
                  className="select select-bordered w-full bg-base-100 text-base-content"
                  value={formData.positionId}
                  onChange={(e) => setFormData({ ...formData, positionId: e.target.value })}
                  required
                >
                  <option value="">Pilih Jabatan</option>
                  {positions.map((position) => (
                    <option key={position.id} value={position.id}>
                      {position.name}
                    </option>
                  ))}
                </select>
              </div>

              <div className="form-control">
                <label className="text-base text-base-content/80 mb-2">
                  Nomor Telepon
                </label>
                <input
                  type="tel"
                  className="input input-bordered w-full bg-base-100 text-base-content"
                  placeholder="Contoh: 081234567890"
                  value={formData.noTelp}
                  onChange={(e) => setFormData({ ...formData, noTelp: e.target.value })}
                  required
                />
              </div>

              <div className="form-control">
                <label className="text-base text-base-content/80 mb-2">
                  Alamat
                </label>
                <textarea
                  className="textarea textarea-bordered w-full bg-base-100 text-base-content"
                  placeholder="Masukkan alamat lengkap"
                  value={formData.alamat}
                  onChange={(e) => setFormData({ ...formData, alamat: e.target.value })}
                  required
                />
              </div>

              <div className="form-control">
                <label className="text-base text-base-content/80 mb-2">
                  Tanggal Bergabung
                </label>
                <input
                  type="date"
                  className="input input-bordered w-full bg-base-100 text-base-content"
                  value={formData.joinDate}
                  onChange={(e) => setFormData({ ...formData, joinDate: e.target.value })}
                  required
                />
              </div>

              <div className="form-control">
                <label className="text-base text-base-content/80 mb-2">
                  Outlet
                </label>
                <select
                  className="select select-bordered w-full bg-base-100 text-base-content"
                  value={formData.outletId}
                  onChange={(e) => setFormData({ ...formData, outletId: e.target.value })}
                  required
                >
                  <option value="">Pilih Outlet</option>
                  {outlets.map((outlet) => (
                    <option key={outlet.id} value={outlet.id}>
                      {outlet.name}
                    </option>
                  ))}
                </select>
              </div>

              {editingEmployee && (
                <div className="form-control">
                  <label className="text-base text-base-content/80 mb-2">
                    Status
                  </label>
                  <select
                    className="select select-bordered w-full bg-base-100 text-base-content"
                    value={formData.status}
                    onChange={(e) => setFormData({ ...formData, status: e.target.value as EmployeeStatus })}
                    required
                  >
                    <option value="ACTIVE">Aktif</option>
                    <option value="INACTIVE">Tidak Aktif</option>
                  </select>
                </div>
              )}

              <div className="modal-action flex justify-end gap-2 pt-4">
                <button
                  type="button"
                  className="btn btn-ghost bg-base-200 hover:bg-base-300 text-base-content"
                  onClick={() => {
                    setIsModalOpen(false)
                    setEditingEmployee(null)
                    resetForm()
                  }}
                >
                  Batal
                </button>
                <button
                  type="submit"
                  className="btn bg-gradient-to-r from-breaktime-primary to-breaktime-secondary text-white hover:from-breaktime-primary/90 hover:to-breaktime-secondary/90 border-0"
                >
                  {editingEmployee ? "Update" : "Simpan"}
                </button>
              </div>
            </form>
          </div>
          <div className="modal-backdrop bg-base-content/20" onClick={() => {
            setIsModalOpen(false)
            setEditingEmployee(null)
            resetForm()
          }}></div>
        </div>
      )}

      {/* Outlet Modal */}
      {isOutletModalOpen && (
        <div className="modal modal-open">
          <div className="modal-box max-w-4xl w-11/12 bg-base-100 dark:bg-base-900 shadow-lg">
            <h3 className="font-bold text-lg bg-gradient-to-r from-breaktime-primary to-breaktime-secondary bg-clip-text text-transparent">
              Kelola Outlet
            </h3>
            
            {/* Form Tambah/Edit Outlet */}
            <form onSubmit={handleOutletSubmit} className="space-y-4 mt-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium text-base-content">Nama Outlet</span>
                  </label>
                  <input
                    type="text"
                    className="input input-bordered bg-base-100 dark:bg-base-800 text-base-content placeholder:text-base-content/60"
                    value={outletFormData.name}
                    onChange={(e) => setOutletFormData({ ...outletFormData, name: e.target.value })}
                    placeholder="Masukkan nama outlet"
                    required
                  />
                </div>
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium text-base-content">Kode Outlet</span>
                  </label>
                  <input
                    type="text"
                    className="input input-bordered bg-base-100 dark:bg-base-800 text-base-content placeholder:text-base-content/60"
                    value={outletFormData.code}
                    onChange={(e) => setOutletFormData({ ...outletFormData, code: e.target.value })}
                    placeholder="Masukkan kode outlet"
                    required
                  />
                </div>
              </div>
              <div className="modal-action">
                <button 
                  type="button" 
                  className="btn bg-base-200 hover:bg-base-300 border-base-300 dark:bg-base-800 dark:hover:bg-base-700 dark:border-base-700 text-base-content"
                  onClick={() => setIsOutletModalOpen(false)}
                >
                  Batal
                </button>
                <button 
                  type="submit" 
                  className="btn bg-gradient-to-r from-breaktime-primary to-breaktime-secondary text-white hover:from-breaktime-primary/90 hover:to-breaktime-secondary/90 border-0"
                >
                  Simpan
                </button>
              </div>
            </form>

            {/* Daftar Outlet */}
            <div className="mt-6">
              <h4 className="text-lg font-semibold text-base-content mb-4">Daftar Outlet</h4>
              <div className="overflow-x-auto">
                <table className="table table-zebra w-full">
                  <thead className="bg-base-200/50 dark:bg-base-800/50">
                    <tr>
                      <th className="font-semibold text-base-content">Nama Outlet</th>
                      <th className="font-semibold text-base-content">Kode Outlet</th>
                      <th className="font-semibold text-base-content w-20">Aksi</th>
                    </tr>
                  </thead>
                  <tbody>
                    {outlets.map((outlet) => (
                      <tr key={outlet.id} className="hover:bg-base-200 dark:hover:bg-base-800">
                        <td>
                          <input
                            type="text"
                            className="input input-bordered input-sm w-full max-w-xs bg-base-100 dark:bg-base-800 text-base-content"
                            value={outletUpdates[outlet.id]?.name ?? outlet.name}
                            onChange={(e) => handleOutletEdit(outlet, 'name', e.target.value)}
                          />
                        </td>
                        <td>
                          <input
                            type="text"
                            className="input input-bordered input-sm w-full max-w-xs bg-base-100 dark:bg-base-800 text-base-content"
                            value={outletUpdates[outlet.id]?.code ?? outlet.code}
                            onChange={(e) => handleOutletEdit(outlet, 'code', e.target.value)}
                          />
                        </td>
                        <td>
                          <button
                            onClick={() => handleOutletDelete(outlet.id)}
                            className="btn btn-square btn-sm btn-ghost hover:bg-error/20 text-error"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>

            {/* Bulk Assign Outlet */}
            <div className="mt-8 border-t border-base-200 dark:border-base-700 pt-6">
              <h4 className="text-lg font-semibold text-base-content mb-4 flex items-center gap-2">
                <Building className="h-5 w-5" />
                Atur Outlet Karyawan
              </h4>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div className="form-control">
                  <label className="label">
                    <span className="label-text font-medium text-base-content">Pilih Outlet</span>
                  </label>
                  <select
                    className="select select-bordered w-full bg-base-100 dark:bg-base-800 text-base-content"
                    value={selectedOutlet}
                    onChange={(e) => setSelectedOutlet(e.target.value)}
                  >
                    <option value="" className="text-base-content">Pilih outlet</option>
                    {outlets.map((outlet) => (
                      <option key={outlet.id} value={outlet.id} className="text-base-content">
                        {outlet.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div className="bg-base-200 dark:bg-base-800 rounded-lg p-4">
                <div className="font-medium text-base-content mb-2">Pilih Karyawan:</div>
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 max-h-48 overflow-y-auto">
                  {employees.map((employee) => (
                    <label key={employee.id} className="flex items-center gap-2 p-2 hover:bg-base-300 dark:hover:bg-base-700 rounded cursor-pointer">
                      <input
                        type="checkbox"
                        className="checkbox checkbox-sm"
                        checked={selectedEmployees.includes(employee.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedEmployees([...selectedEmployees, employee.id])
                          } else {
                            setSelectedEmployees(selectedEmployees.filter(id => id !== employee.id))
                          }
                        }}
                      />
                      <span className="text-sm text-base-content">{employee.user.name}</span>
                      <span className="text-xs text-base-content/60">
                        ({outlets.find(o => o.id === employee.outletId)?.name || 'Tidak ada outlet'})
                      </span>
                    </label>
                  ))}
                </div>
              </div>

              <div className="mt-4 flex justify-end">
                <button
                  onClick={handleBulkAssignOutlet}
                  disabled={!selectedOutlet || selectedEmployees.length === 0}
                  className="btn bg-gradient-to-r from-breaktime-primary to-breaktime-secondary text-white hover:from-breaktime-primary/90 hover:to-breaktime-secondary/90 border-0 disabled:opacity-50"
                >
                  Update Outlet Karyawan ({selectedEmployees.length})
                </button>
              </div>
            </div>
          </div>
          <div className="modal-backdrop bg-base-content/20" onClick={() => setIsOutletModalOpen(false)}></div>
        </div>
      )}

      {/* Modal Delete */}
      {isDeleteModalOpen && (
        <div className="modal modal-open">
          <div className="modal-box bg-base-100 border border-base-300 max-w-2xl">
            <h3 className="text-xl font-bold text-base-content mb-4">Konfirmasi Hapus</h3>
            <p className="text-base-content mb-4">
              Apakah Anda yakin ingin menghapus karyawan berikut beserta semua data terkait:
            </p>

            {/* Detail Karyawan */}
            <div className="bg-base-200 p-4 rounded-lg mb-6 space-y-4">
              <div className="flex items-center gap-3 pb-3 border-b border-base-300">
                <div className="w-10 h-10 rounded-full bg-error/20 flex items-center justify-center">
                  <span className="text-xl font-bold text-error">
                    {deletingEmployee?.user.name.charAt(0).toUpperCase()}
                  </span>
                </div>
                <div>
                  <h4 className="font-medium text-base-content">{deletingEmployee?.user.name}</h4>
                  <p className="text-sm text-base-content/70">{deletingEmployee?.position.name}</p>
                </div>
              </div>

              {/* Data yang akan dihapus */}
              <div className="space-y-2">
                <h5 className="font-medium text-base-content">Data yang akan dihapus:</h5>
                <ul className="space-y-2 text-sm">
                  <li className="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-error" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    <span className="text-error">Data Pribadi Karyawan</span>
                    <span className="text-base-content/60">
                      (Nama, NIK, Alamat, No. Telp)
                    </span>
                  </li>
                  <li className="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-error" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    <span className="text-error">Data Akun</span>
                    <span className="text-base-content/60">
                      (Username, Password, Role)
                    </span>
                  </li>
                  <li className="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-error" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    <span className="text-error">Data Gaji & Komisi</span>
                    <span className="text-base-content/60">
                      (Slip Gaji, Riwayat Gaji, Komisi Harian)
                    </span>
                  </li>
                  <li className="flex items-center gap-2">
                    <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5 text-error" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    <span className="text-error">Data Kasbon</span>
                    <span className="text-base-content/60">
                      (Riwayat Kasbon)
                    </span>
                  </li>
                </ul>
              </div>
            </div>

            <div className="bg-error/10 p-4 rounded-lg mb-6">
              <p className="text-sm text-error font-medium flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                Peringatan:
              </p>
              <p className="text-sm text-error mt-1">
                Tindakan ini tidak dapat dibatalkan dan akan menghapus permanen semua data terkait karyawan ini.
              </p>
            </div>

            <div className="modal-action">
              <button
                type="button"
                className="btn btn-ghost bg-base-200 hover:bg-base-300 text-base-content"
                onClick={() => {
                  setIsDeleteModalOpen(false)
                  setDeletingEmployee(null)
                }}
                disabled={isDeleting}
              >
                Batal
              </button>
              <button
                type="button"
                className="btn bg-error hover:bg-error/90 text-white border-0"
                onClick={handleDelete}
                disabled={isDeleting}
              >
                {isDeleting ? (
                  <>
                    <span className="loading loading-spinner loading-sm"></span>
                    Menghapus...
                  </>
                ) : (
                  "Hapus Semua Data"
                )}
              </button>
            </div>
          </div>
          <div 
            className="modal-backdrop bg-base-content/20" 
            onClick={() => {
              if (!isDeleting) {
                setIsDeleteModalOpen(false)
                setDeletingEmployee(null)
              }
            }}
          ></div>
        </div>
      )}

      {/* Modal Delete Outlet */}
      {isOutletDeleteModalOpen && (
        <div className="modal modal-open">
          <div className="modal-box bg-base-100 border border-base-300 max-w-2xl">
            <h3 className="text-xl font-bold text-base-content mb-4">Pindahkan Karyawan & Hapus Outlet</h3>
            <p className="text-base-content mb-4">
              Outlet ini masih memiliki karyawan aktif. Pilih outlet tujuan untuk memindahkan karyawan berikut:
            </p>

            {/* Daftar Karyawan */}
            <div className="bg-base-200 p-4 rounded-lg mb-6">
              <h4 className="font-medium text-base-content mb-3">Daftar Karyawan:</h4>
              <div className="space-y-2">
                {employees
                  .filter(emp => emp.outletId === deletingOutletId)
                  .map(emp => (
                    <div key={emp.id} className="flex items-center gap-2 p-2 bg-base-100 rounded-lg">
                      <div className="w-8 h-8 rounded-full bg-primary/10 flex items-center justify-center">
                        <span className="text-sm font-bold text-primary">
                          {emp.user.name.charAt(0).toUpperCase()}
                        </span>
                      </div>
                      <div>
                        <p className="font-medium text-base-content">{emp.user.name}</p>
                        <p className="text-sm text-base-content/70">{emp.position.name}</p>
                      </div>
                    </div>
                  ))
                }
              </div>
            </div>

            {/* Pilih Outlet Tujuan */}
            <div className="form-control mb-6">
              <label className="label">
                <span className="label-text font-medium text-base-content">Pilih Outlet Tujuan</span>
              </label>
              <select
                className="select select-bordered w-full bg-base-100 text-base-content"
                value={targetOutletId}
                onChange={(e) => setTargetOutletId(e.target.value)}
                required
              >
                <option value="">Pilih outlet tujuan</option>
                {outlets
                  .filter(o => o.id !== deletingOutletId)
                  .map((outlet) => (
                    <option key={outlet.id} value={outlet.id}>
                      {outlet.name}
                    </option>
                  ))}
              </select>
            </div>

            <div className="bg-warning/10 p-4 rounded-lg mb-6">
              <p className="text-sm text-warning font-medium flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                Peringatan:
              </p>
              <p className="text-sm text-warning mt-1">
                Setelah karyawan dipindahkan, outlet akan dihapus secara permanen dan tidak dapat dikembalikan.
              </p>
            </div>

            <div className="modal-action">
              <button
                type="button"
                className="btn btn-ghost bg-base-200 hover:bg-base-300 text-base-content"
                onClick={() => {
                  setIsOutletDeleteModalOpen(false)
                  setDeletingOutletId(null)
                  setTargetOutletId("")
                }}
                disabled={isDeletingOutlet}
              >
                Batal
              </button>
              <button
                type="button"
                className="btn bg-error hover:bg-error/90 text-white border-0"
                onClick={handleMoveEmployeesAndDeleteOutlet}
                disabled={!targetOutletId || isDeletingOutlet}
              >
                {isDeletingOutlet ? (
                  <>
                    <span className="loading loading-spinner loading-sm"></span>
                    Memproses...
                  </>
                ) : (
                  "Pindahkan & Hapus"
                )}
              </button>
            </div>
          </div>
          <div 
            className="modal-backdrop bg-base-content/20" 
            onClick={() => {
              if (!isDeletingOutlet) {
                setIsOutletDeleteModalOpen(false)
                setDeletingOutletId(null)
                setTargetOutletId("")
              }
            }}
          ></div>
        </div>
      )}

      {/* Modal Confirm Delete Outlet */}
      {isConfirmDeleteOutletOpen && (
        <div className="modal modal-open">
          <div className="modal-box bg-base-100 border border-base-300">
            <h3 className="text-xl font-bold text-base-content mb-4">Konfirmasi Hapus Outlet</h3>
            
            {/* Detail Outlet */}
            <div className="bg-base-200 p-4 rounded-lg mb-6">
              <div className="flex items-center gap-3 pb-3 border-b border-base-300">
                <div className="w-10 h-10 rounded-full bg-error/20 flex items-center justify-center">
                  <Building className="h-6 w-6 text-error" />
                </div>
                <div>
                  <h4 className="font-medium text-base-content">{outletToDelete?.name}</h4>
                  <p className="text-sm text-base-content/70">Kode: {outletToDelete?.code}</p>
                </div>
              </div>
            </div>

            <div className="bg-error/10 p-4 rounded-lg mb-6">
              <p className="text-sm text-error font-medium flex items-center gap-2">
                <svg xmlns="http://www.w3.org/2000/svg" className="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                </svg>
                Peringatan:
              </p>
              <p className="text-sm text-error mt-1">
                Outlet akan dihapus secara permanen dan tidak dapat dikembalikan.
              </p>
            </div>

            <div className="modal-action">
              <button
                type="button"
                className="btn btn-ghost bg-base-200 hover:bg-base-300 text-base-content"
                onClick={() => {
                  setIsConfirmDeleteOutletOpen(false)
                  setOutletToDelete(null)
                }}
              >
                Batal
              </button>
              <button
                type="button"
                className="btn bg-error hover:bg-error/90 text-white border-0"
                onClick={handleConfirmDeleteOutlet}
              >
                Hapus Outlet
              </button>
            </div>
          </div>
          <div 
            className="modal-backdrop bg-base-content/20" 
            onClick={() => {
              setIsConfirmDeleteOutletOpen(false)
              setOutletToDelete(null)
            }}
          ></div>
        </div>
      )}
    </div>
  )
} 