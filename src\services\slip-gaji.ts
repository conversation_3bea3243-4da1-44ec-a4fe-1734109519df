import { Slip<PERSON><PERSON><PERSON>, CreateSlipGajiDTO, SlipGajiResponse } from "@/types/slip-gaji"
import { jsPDF } from "jspdf"
import * as XLSX from "xlsx"
import { ApiResponse } from "@/types/api"
import { PrismaClient } from "@prisma/client"
import { getOutlets } from "@/services/outlet"

const prisma = new PrismaClient()

// Get all slip gaji
export async function getSlipGaji(month?: number, year?: number): Promise<SlipGaji[]> {
  const queryParams = new URLSearchParams()
  if (month) queryParams.append('month', month.toString())
  if (year) queryParams.append('year', year.toString())

  const response = await fetch(`/api/slip-gaji?${queryParams.toString()}`)
  const data: SlipGajiResponse = await response.json()

  if (!data.success) {
    throw new Error(data.error || data.message)
  }

  return data.data as SlipGaji[]
}

// Get slip gaji by ID
export async function getSlipGajiById(id: string): Promise<SlipGaji | null> {
  const response = await fetch(`/api/slip-gaji/${id}`)
  const data: SlipGajiResponse = await response.json()

  if (!data.success) {
    if (response.status === 404) return null
    throw new Error(data.error || data.message)
  }

  return data.data as SlipGaji
}

// Create new slip gaji
export const createSlipGaji = async (data: CreateSlipGajiDTO): Promise<ApiResponse<SlipGaji>> => {
  try {
    const response = await fetch('/api/slip-gaji', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        ...data,
        gaji: {
          ...data.gaji,
          komisi: data.gaji.komisi
        }
      }),
    })

    const result = await response.json()
    if (!response.ok) {
      return {
        success: false,
        error: result.error || 'Gagal membuat slip gaji'
      }
    }

    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Terjadi kesalahan saat membuat slip gaji'
    }
  }
}

// Update slip gaji
export const updateSlipGaji = async (id: string, data: CreateSlipGajiDTO): Promise<ApiResponse<SlipGaji>> => {
  try {
    const response = await fetch(`/api/slip-gaji/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data),
    })

    const result = await response.json()
    if (!response.ok) {
      return {
        success: false,
        error: result.error || 'Gagal mengupdate slip gaji'
      }
    }

    return {
      success: true,
      data: result
    }
  } catch (error) {
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Terjadi kesalahan saat mengupdate slip gaji'
    }
  }
}

// Delete slip gaji
export async function deleteSlipGaji(id: string): Promise<void> {
  const response = await fetch(`/api/slip-gaji/${id}`, {
    method: 'DELETE'
  })

  const result: SlipGajiResponse = await response.json()

  if (!result.success) {
    throw new Error(result.error || result.message)
  }
}

// Format currency
export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0,
    maximumFractionDigits: 0
  }).format(amount)
}

// Format date
export function formatDate(date: Date | string): string {
  const d = new Date(date)
  return d.toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  })
}

// Get month name
export function getNamaBulan(bulan: number): string {
  const namaBulan = [
    "Januari", "Februari", "Maret", "April", "Mei", "Juni",
    "Juli", "Agustus", "September", "Oktober", "November", "Desember"
  ]
  return namaBulan[bulan - 1] || ""
}

// Format periode
export function formatPeriode(periode: { bulan: number, tahun: number }): string {
  return `${getNamaBulan(periode.bulan)} ${periode.tahun}`
}

// Calculate total potongan
export function calculateTotalPotongan(potongan: {
  kasbon: number,
  piutang: number,
  bpjsTP: number,
  bpjsTK: number,
  lainnya: number,
  penyesuaian?: number
}): number {
  return potongan.kasbon +
    potongan.piutang +
    potongan.bpjsTP +
    potongan.bpjsTK +
    potongan.lainnya +
    (potongan.penyesuaian || 0)
}

// Calculate total pendapatan
export function calculateTotalPendapatan(gaji: {
  pokok: number,
  tunjangan: {
    transport: number,
    pulsa: number,
    makan: number,
    bpjs: number,
    penyesuaian: number
  },
  komisi: number,
  lembur: number,
  bonus: number
}): number {
  return gaji.pokok +
    gaji.tunjangan.transport +
    gaji.tunjangan.pulsa +
    gaji.tunjangan.makan +
    gaji.tunjangan.bpjs +
    gaji.tunjangan.penyesuaian +
    gaji.komisi +
    gaji.lembur +
    gaji.bonus
}

// Colors from Breaktime logo
const COLORS = {
  primary: "#3EBAB4", // Breaktime teal
  secondary: "#F5B344", // Breaktime yellow
  text: "#2D3748", // Dark gray for text
  lightGray: "#F7FAFC", // Light background
  mediumGray: "#E2E8F0", // Medium gray for borders
  white: "#FFFFFF"
}

// Interface untuk jsPDF GState
interface GState {
  opacity: number;
}

// Generate PDF slip gaji
export async function generatePDFSlipGaji(slip: SlipGaji): Promise<Blob> {
  // Create PDF with A4 size and compression
  const doc = new jsPDF({
    orientation: "portrait",
    unit: "mm",
    format: "a4",
    compress: true,
    putOnlyUsedFonts: true,
    floatPrecision: 16
  })

  const pageWidth = doc.internal.pageSize.width
  const pageHeight = doc.internal.pageSize.height
  const margin = 15
  let y = margin

  // Set font
  doc.setFont("helvetica")

  // Add watermark logo
  try {
    const response = await fetch("/logo.png")
    const blob = await response.blob()
    const reader = new FileReader()

    await new Promise((resolve, reject) => {
      reader.onload = () => {
        try {
          const base64 = reader.result as string
          // Menambahkan logo sebagai watermark di tengah halaman
          const watermarkWidth = 100 // Lebar watermark dalam mm
          const watermarkHeight = 100 // Tinggi watermark dalam mm
          const watermarkX = (pageWidth - watermarkWidth) / 2
          const watermarkY = (pageHeight - watermarkHeight) / 2

          // Simpan state grafis saat ini
          doc.saveGraphicsState()

          // Set opacity untuk watermark dengan type yang benar
          const gState: GState = { opacity: 0.1 }
          doc.setGState(gState as any)

          // Tambahkan watermark
          doc.addImage(base64, "PNG", watermarkX, watermarkY, watermarkWidth, watermarkHeight)

          // Kembalikan state grafis
          doc.restoreGraphicsState()

          resolve(null)
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })
  } catch (error) {
    console.error("Error loading watermark:", error)
  }

  // Add company logo with image optimization
  try {
    const response = await fetch("/logo.png")
    const blob = await response.blob()
    const reader = new FileReader()

    await new Promise((resolve, reject) => {
      reader.onload = () => {
        try {
          const base64 = reader.result as string
          // Menggunakan opsi kompresi untuk gambar
          doc.addImage(base64, "PNG", margin, y, 20, 20) // Perkecil logo header
          resolve(null)
        } catch (error) {
          reject(error)
        }
      }
      reader.onerror = reject
      reader.readAsDataURL(blob)
    })

    // Adjust company info position with reduced spacing
    y += 4 // Kurangi spacing setelah logo
    doc.setFontSize(12)
    doc.setTextColor(COLORS.text)
    doc.setFont("helvetica", "bold")
    doc.text("Breaktime", margin + 25, y)
    y += 4 // Kurangi spacing
    doc.setFontSize(9)
    doc.setFont("helvetica", "normal")
    doc.text("Badan Segar Urusan Lancar", margin + 25, y)
    y += 3 // Kurangi spacing
    doc.text("Jl. Setia Budi", margin + 25, y)
    y += 3 // Kurangi spacing
    doc.text("Palu, Sulawesi Tengah 94111", margin + 25, y)
    y += 8 // Kurangi spacing sebelum divider
  } catch (error) {
    console.error("Error loading logo:", error)
    // Fallback to text only header with reduced spacing
    doc.setFontSize(16)
    doc.setFont("helvetica", "bold")
    doc.setTextColor(COLORS.primary)
    doc.text("Breaktime", margin, y)
    y += 4
    doc.setFontSize(10)
    doc.setTextColor(COLORS.text)
    doc.text("Badan Segar Urusan Lancar", margin, y)
    y += 3
    doc.setFontSize(9)
    doc.setFont("helvetica", "normal")
    doc.text("Jl. Setia Budi", margin, y)
    y += 3
    doc.text("Palu, Sulawesi Tengah 94111", margin, y)
    y += 8
  }

  // Divider
  doc.setDrawColor(COLORS.primary)
  doc.setLineWidth(0.5)
  doc.line(margin, y, pageWidth - margin, y)
  y += 8 // Kurangi spacing setelah divider

  // Header with reduced spacing
  doc.setFontSize(14)
  doc.setFont("helvetica", "bold")
  doc.setTextColor(COLORS.primary)
  doc.text("SLIP GAJI KARYAWAN", pageWidth / 2, y, { align: "center" })
  y += 6 // Kurangi spacing

  doc.setFontSize(11)
  doc.setTextColor(COLORS.text)
  doc.text(`Periode: ${formatPeriode(slip.periode)}`, pageWidth / 2, y, { align: "center" })
  y += 8 // Kurangi spacing

  // Informasi Karyawan with reduced spacing
  doc.setFontSize(10)
  doc.setFont("helvetica", "bold")
  doc.setTextColor(COLORS.primary)
  doc.text("INFORMASI KARYAWAN", margin, y)
  y += 6 // Kurangi spacing

  // Employee info in two columns with reduced spacing
  doc.setFont("helvetica", "normal")
  doc.setFontSize(9)
  doc.setTextColor(COLORS.text)

  // Left column
  const leftColX = margin + 25
  doc.text("Nama", margin, y)
  doc.text(":", margin + 20, y)
  doc.text(slip.employee.user.name, leftColX, y)
  y += 5
  doc.text("Jabatan", margin, y)
  doc.text(":", margin + 20, y)
  doc.text(slip.employee.position.name, leftColX, y)

  // Right column with adjusted spacing
  const rightStart = pageWidth / 2
  const rightColX = rightStart + 35 // Increased spacing for longer text
  doc.text("Kontak", rightStart, y - 5)
  doc.text(":", rightStart + 25, y - 5)
  doc.text(slip.employee.noTelp, rightColX, y - 5)
  y += 5
  doc.text("Tanggal Bergabung", rightStart, y - 5)
  doc.text(":", rightStart + 25, y - 5)
  const joinDate = formatDate(slip.employee.user.joinDate).replace("Januari", "January")
  doc.text(joinDate, rightColX, y - 5)
  y += 7

  // Rincian Pendapatan with compact layout
  y += 6 // Kurangi spacing sebelum rincian pendapatan
  doc.setFontSize(10)
  doc.setFont("helvetica", "bold")
  doc.setTextColor(COLORS.primary)
  doc.text("RINCIAN PENDAPATAN", margin, y)
  y += 4 // Kurangi spacing

  // Table header
  doc.setFillColor(COLORS.lightGray)
  doc.rect(margin, y, pageWidth - (2 * margin), 7, "F")
  doc.setTextColor(COLORS.text)
  doc.setFontSize(9)
  doc.text("Komponen", margin + 5, y + 5)
  doc.text("Jumlah", pageWidth - margin - 5, y + 5, { align: "right" })
  y += 7

  // Table content with reduced row height
  doc.setFont("helvetica", "normal")
  const addTableRow = (label: string, value: string, isTotal = false) => {
    if (isTotal) {
      doc.setFillColor(COLORS.lightGray)
      doc.rect(margin, y, pageWidth - (2 * margin), 7, "F")
      doc.setFont("helvetica", "bold")
    }
    doc.text(label, margin + 5, y + 5)
    doc.text(value, pageWidth - margin - 5, y + 5, { align: "right" })
    y += 7
  }

  addTableRow("Gaji Pokok", formatCurrency(slip.gaji.pokok))
  addTableRow("Tunjangan Transport", formatCurrency(slip.gaji.tunjangan.transport))
  addTableRow("Tunjangan Pulsa", formatCurrency(slip.gaji.tunjangan.pulsa))
  addTableRow("Tunjangan Makan", formatCurrency(slip.gaji.tunjangan.makan))
  addTableRow("Tunjangan BPJS", formatCurrency(slip.gaji.tunjangan.bpjs))
  addTableRow("Tunjangan Penyesuaian", formatCurrency(slip.gaji.tunjangan.penyesuaian))
  addTableRow("Komisi", formatCurrency(slip.gaji.komisi))
  addTableRow("Lembur", formatCurrency(slip.gaji.lembur))
  addTableRow("Bonus", formatCurrency(slip.gaji.bonus))
  addTableRow("Total Pendapatan", formatCurrency(calculateTotalPendapatan(slip.gaji)), true)
  y += 5

  // Rincian Potongan with compact layout
  y += 4 // Kurangi spacing sebelum rincian potongan
  doc.setFontSize(10)
  doc.setFont("helvetica", "bold")
  doc.setTextColor(COLORS.primary)
  doc.text("RINCIAN POTONGAN", margin, y)
  y += 4 // Kurangi spacing

  // Table header
  doc.setFillColor(COLORS.lightGray)
  doc.rect(margin, y, pageWidth - (2 * margin), 7, "F")
  doc.setTextColor(COLORS.text)
  doc.setFontSize(9)
  doc.text("Komponen", margin + 5, y + 5)
  doc.text("Jumlah", pageWidth - margin - 5, y + 5, { align: "right" })
  y += 7

  // Table content
  doc.setFont("helvetica", "normal")
  addTableRow("Kasbon", formatCurrency(slip.gaji.potongan.kasbon))
  addTableRow("Piutang", formatCurrency(slip.gaji.potongan.piutang))
  addTableRow("BPJS TK", formatCurrency(slip.gaji.potongan.bpjsTK))
  addTableRow("BPJS TP", formatCurrency(slip.gaji.potongan.bpjsTP))
  addTableRow("Lainnya", formatCurrency(slip.gaji.potongan.lainnya))
  addTableRow("Penyesuaian", formatCurrency(slip.gaji.potongan.penyesuaian || 0))
  if (slip.gaji.potongan.keterangan) {
    doc.setFontSize(8)
    doc.text(`Keterangan: ${slip.gaji.potongan.keterangan}`, margin + 5, y + 3)
    y += 5
  }
  addTableRow("Total Potongan", formatCurrency(calculateTotalPotongan(slip.gaji.potongan)), true)
  y += 5

  // Total Gaji Bersih with reduced height
  y += 4 // Kurangi spacing sebelum total
  doc.setFillColor(hexToRgb(COLORS.primary).r, hexToRgb(COLORS.primary).g, hexToRgb(COLORS.primary).b, 0.1)
  doc.rect(margin, y, pageWidth - (2 * margin), 10, "F") // Kurangi tinggi box
  doc.setFontSize(11)
  doc.setFont("helvetica", "bold")
  doc.setTextColor(COLORS.primary)
  doc.text("TOTAL GAJI BERSIH", margin + 5, y + 7)
  doc.text(formatCurrency(slip.gaji.total), pageWidth - margin - 5, y + 7, { align: "right" })
  y += 15 // Kurangi spacing setelah total

  // Tanda Tangan with compact layout
  doc.setFontSize(9)
  doc.setFont("helvetica", "normal")
  doc.setTextColor(COLORS.text)
  const today = formatDate(new Date())
  doc.text(`Palu, ${today}`, pageWidth - margin - 40, y, { align: "center" })
  y += 12 // Kurangi spacing
  doc.text("( Wahyudy Effendy )", pageWidth - margin - 40, y, { align: "center" })
  y += 4 // Kurangi spacing
  doc.text("General Manager", pageWidth - margin - 40, y, { align: "center" })

  // Footer dengan posisi di paling bawah halaman
  y = pageHeight - 10 // Naikkan posisi dari bawah (sebelumnya 3mm)
  doc.setFontSize(7.5)
  doc.setFont("helvetica", "italic")
  doc.setTextColor(hexToRgb(COLORS.text).r, hexToRgb(COLORS.text).g, hexToRgb(COLORS.text).b, 0.7)
  doc.text("* Slip gaji ini digenerate secara otomatis dan sah tanpa tanda tangan", pageWidth / 2, y, { align: "center" })

  // Tambahkan tanggal dan jam cetak dengan jarak yang lebih rapat
  y += 3 // Sesuaikan jarak (sebelumnya 2.5mm)
  doc.setFont("helvetica", "normal")
  const now = new Date()
  const hours = String(now.getHours()).padStart(2, '0')
  const minutes = String(now.getMinutes()).padStart(2, '0')
  doc.text(`Dicetak pada: ${formatDate(now)} ${hours}:${minutes} WITA`, pageWidth / 2, y, { align: "center" })

  // Return PDF as blob
  return doc.output('blob')
}

// Helper function to convert hex to RGB
function hexToRgb(hex: string) {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : { r: 0, g: 0, b: 0 }
}

// Helper function untuk menerapkan format Rupiah ke cell
function applyRupiahFormat(ws: XLSX.WorkSheet, row: number, col: number) {
  const cellRef = XLSX.utils.encode_cell({ r: row, c: col })
  if (ws[cellRef]) {
    ws[cellRef].z = '_("Rp"* #,##0_);_("Rp"* (#,##0);_("Rp"* "-"_);_(@_)'
  }
}

// Generate Excel slip gaji
export async function generateExcelSlipGaji(slips: SlipGaji[], month: number, year: number): Promise<Blob> {
  try {
    // Ambil data outlet
    const outlets = await getOutlets()

    // Filter slip gaji berdasarkan bulan dan tahun
    const filteredSlips = slips.filter(slip =>
      slip.periode.bulan === month && slip.periode.tahun === year
    )

    // Kelompokkan slip berdasarkan outlet
    const slipsByOutlet: Record<string, SlipGaji[]> = {}
    filteredSlips.forEach(slip => {
      const outletName = outlets.find(o => o.id === slip.employee.outletId)?.name || 'Tidak Ada Outlet'
      if (!slipsByOutlet[outletName]) {
        slipsByOutlet[outletName] = []
      }
      slipsByOutlet[outletName].push(slip)
    })

    // Style untuk judul
    const titleStyle = {
      font: {
        bold: true,
        size: 14,
        color: { rgb: "FFFFFF" }
      },
      fill: { fgColor: { rgb: "4F46E5" } },
      alignment: { horizontal: "center", vertical: "center" }
    }

    // Style untuk header
    const headerStyle = {
      font: { bold: true },
      fill: { fgColor: { rgb: "E5E7EB" } },
      alignment: { horizontal: "center" },
      border: {
        top: { style: "thin" },
        bottom: { style: "thin" },
        left: { style: "thin" },
        right: { style: "thin" }
      }
    }

    // Style untuk total
    const totalStyle = {
      font: { bold: true },
      fill: { fgColor: { rgb: "E5E7EB" } },
      alignment: { horizontal: "right" },
      border: {
        top: { style: "thin" },
        bottom: { style: "thin" },
        left: { style: "thin" },
        right: { style: "thin" }
      }
    }

    // Format Rupiah
    const rupiahFormat = '_("Rp"* #,##0_);_("Rp"* (#,##0);_("Rp"* "-"_);_(@_)'

    const workbook = XLSX.utils.book_new()

    // Buat worksheet untuk setiap outlet
    Object.entries(slipsByOutlet).forEach(([outletName, outletSlips]) => {
      const headers = [
        ['SLIP GAJI KARYAWAN'],
        ['Outlet', outletName],
        ['Periode', formatPeriode(outletSlips[0].periode)],
        [''],
        ['No', 'Nama Karyawan', 'Jabatan', 'Gaji Pokok', 'Tunjangan Transport', 'Tunjangan Pulsa', 'Tunjangan Makan', 'Tunjangan BPJS', 'Penyesuaian', 'Lembur', 'Bonus', 'Komisi', 'Penyesuaian Rasio', 'Omzet', 'Potongan Kasbon', 'Potongan Piutang', 'Potongan BPJS', 'Potongan Lainnya', 'Potongan Penyesuaian', 'Total Gaji']
      ]

      const data = outletSlips.map((slip, index) => [
        index + 1,
        slip.employee.user.name,
        slip.employee.position.name,
        slip.gaji.pokok,
        slip.gaji.tunjangan.transport,
        slip.gaji.tunjangan.pulsa,
        slip.gaji.tunjangan.makan,
        slip.gaji.tunjangan.bpjs,
        slip.gaji.tunjangan.penyesuaian,
        slip.gaji.lembur,
        slip.gaji.bonus,
        slip.gaji.komisi,
        slip.gaji.tunjangan.penyesuaian,
        slip.gaji.omzet,
        slip.gaji.potongan.kasbon,
        slip.gaji.potongan.piutang,
        slip.gaji.potongan.bpjsTP + slip.gaji.potongan.bpjsTK,
        slip.gaji.potongan.lainnya,
        slip.gaji.potongan.penyesuaian,
        slip.gaji.total
      ])

      // Hitung total
      const totals = data.reduce((acc, row) => {
        for (let i = 3; i < row.length; i++) {
          acc[i] = ((acc[i] || 0) as number) + (row[i] as number)
        }
        return acc
      }, [] as number[])

      // Tambahkan baris total
      const totalRow = [
        '',
        'TOTAL',
        '',
        ...totals.slice(3)
      ]

      const ws = XLSX.utils.aoa_to_sheet([...headers, ...data, totalRow])

      // Set lebar kolom
      const colWidths = [
        { wch: 5 },  // No
        { wch: 25 }, // Nama
        { wch: 20 }, // Jabatan
        { wch: 15 }, // Gaji Pokok
        { wch: 15 }, // Transport
        { wch: 15 }, // Pulsa
        { wch: 15 }, // Makan
        { wch: 15 }, // BPJS
        { wch: 15 }, // Penyesuaian
        { wch: 15 }, // Lembur
        { wch: 15 }, // Bonus
        { wch: 15 }, // Komisi
        { wch: 15 }, // Penyesuaian Rasio
        { wch: 15 }, // Omzet
        { wch: 15 }, // Kasbon
        { wch: 15 }, // Piutang
        { wch: 15 }, // BPJS
        { wch: 15 }, // Lainnya
        { wch: 15 }, // Potongan Penyesuaian
        { wch: 15 }  // Total
      ]
      ws['!cols'] = colWidths

      // Merge cells untuk judul
      ws['!merges'] = [
        { s: { r: 0, c: 0 }, e: { r: 0, c: 19 } }, // Title
        { s: { r: 1, c: 1 }, e: { r: 1, c: 19 } }, // Outlet
        { s: { r: 2, c: 1 }, e: { r: 2, c: 19 } }  // Period
      ]

      // Apply styles and Rupiah format
      for (let row = 0; row < data.length + 6; row++) {
        for (let col = 0; col < ws['!cols'].length; col++) {
          const cellRef = XLSX.utils.encode_cell({ r: row, c: col })
          if (!ws[cellRef]) continue

          // Apply styles
          if (row === 0) {
            ws[cellRef].s = titleStyle
          } else if (row === 4) {
            ws[cellRef].s = headerStyle
          } else if (row === data.length + 5) {
            ws[cellRef].s = totalStyle
            // Format Rupiah untuk baris total (kecuali kolom No, Nama, Jabatan)
            if (col >= 3) {
              applyRupiahFormat(ws, row, col)
            }
          } else if (row > 4) {
            // Format Rupiah untuk semua kolom nominal (mulai dari Gaji Pokok)
            if (col >= 3) {
              applyRupiahFormat(ws, row, col)
            }
          }
        }
      }

      XLSX.utils.book_append_sheet(workbook, ws, outletName)
    })

    // Buat summary sheet
    const summaryHeaders = [
      ['RINGKASAN SLIP GAJI'],
      [''],
      ['Outlet', 'Total Gaji', 'Total Komisi', 'Total Tunjangan', 'Total Pendapatan', 'Total Potongan']
    ]

    const summaryData = Object.entries(slipsByOutlet).map(([outletName, outletSlips]) => {
      const totalGaji = outletSlips.reduce((sum, slip) => sum + slip.gaji.pokok, 0)
      const totalKomisi = outletSlips.reduce((sum, slip) => sum + slip.gaji.komisi, 0)
      const totalTunjangan = outletSlips.reduce((sum, slip) =>
        sum + slip.gaji.tunjangan.transport +
        slip.gaji.tunjangan.pulsa +
        slip.gaji.tunjangan.makan +
        slip.gaji.tunjangan.bpjs +
        slip.gaji.tunjangan.penyesuaian, 0)
      const totalPendapatan = totalGaji + totalKomisi + totalTunjangan
      const totalPotongan = outletSlips.reduce((sum, slip) =>
        sum + slip.gaji.potongan.kasbon +
        slip.gaji.potongan.bpjsTK +
        slip.gaji.potongan.bpjsTP +
        slip.gaji.potongan.lainnya, 0)

      return [outletName, totalGaji, totalKomisi, totalTunjangan, totalPendapatan, totalPotongan]
    })

    // Hitung grand total
    const grandTotal = summaryData.reduce((acc, row) => {
      for (let i = 1; i < row.length; i++) {
        acc[i] = ((acc[i] || 0) as number) + (row[i] as number)
      }
      return acc
    }, [] as number[])

    const summaryWs = XLSX.utils.aoa_to_sheet([
      ...summaryHeaders,
      ...summaryData,
      ['GRAND TOTAL', ...grandTotal.slice(1)]
    ])

    // Set lebar kolom summary
    summaryWs['!cols'] = [
      { wch: 20 }, // Outlet
      { wch: 15 }, // Total Gaji
      { wch: 15 }, // Total Komisi
      { wch: 15 }, // Total Tunjangan
      { wch: 18 }, // Total Pendapatan
      { wch: 15 }  // Total Potongan
    ]

    // Apply styles untuk summary
    for (let row = 0; row < summaryData.length + 4; row++) {
      for (let col = 0; col < 6; col++) {
        const cellRef = XLSX.utils.encode_cell({ r: row, c: col })
        if (!summaryWs[cellRef]) continue

        // Apply styles
        if (row === 0) {
          summaryWs[cellRef].s = titleStyle
        } else if (row === 2) {
          summaryWs[cellRef].s = headerStyle
        } else if (row === summaryData.length + 3) {
          summaryWs[cellRef].s = {
            font: {
              bold: true,
              color: { rgb: "FFFFFF" }
            },
            fill: { fgColor: { rgb: "4F46E5" } },
            alignment: { horizontal: col === 0 ? "left" : "right" },
            border: {
              top: { style: "thin" },
              bottom: { style: "thin" },
              left: { style: "thin" },
              right: { style: "thin" }
            }
          }
          // Format Rupiah untuk baris grand total (kecuali kolom Outlet)
          if (col > 0) {
            applyRupiahFormat(summaryWs, row, col)
          }
        } else if (row > 2) {
          // Format Rupiah untuk semua kolom nominal (kecuali kolom Outlet)
          if (col > 0) {
            applyRupiahFormat(summaryWs, row, col)
          }
        }
      }
    }

    XLSX.utils.book_append_sheet(workbook, summaryWs, 'Ringkasan')

    // Convert workbook to blob
    const wbout = XLSX.write(workbook, {
      bookType: 'xlsx',
      type: 'array'
    })

    return new Blob([wbout], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' })
  } catch (error) {
    console.error('Error generating Excel:', error)
    throw error
  }
}