export interface Salary {
  id: string
  employeeId: string
  employee: {
    name: string
    position: {
      id: string
      name: string
      omsetPercentage: number
    }
  }
  periodStart: Date
  periodEnd: Date
  baseOmset: number
  totalOmset: number
  salaryAmount: number
  earnings: {
    baseSalary: number
    transportation: number
    phoneAllowance: number
    mealAllowance: number
    commission: number
    overtime: number
    bpjsAllowance: number
    adjustmentRatio: number
  }
  deductions: any
  bonuses: any
  finalAmount: number
  isPaid: boolean
  paidAt: Date | null
  createdAt: Date
  updatedAt: Date
}

export interface CreateSalaryInput {
  employeeId: string
  positionId: string
  periodStart: Date
  periodEnd: Date
  baseOmset: number
  totalOmset: number
  earnings: {
    baseSalary: number
    transportation: number
    phoneAllowance: number
    mealAllowance: number
    commission: number
    overtime: number
    bpjsAllowance: number
    adjustmentRatio: number
  }
  deductions: {
    loans: number
    debt: number
    bpjsTP: number
    bpjsTK: number
    other: number
    description?: string
  }
  bonuses: {
    amount: number
    description?: string
  }
}

export interface UpdateSalaryInput {
  id: string
  baseOmset?: number
  totalOmset?: number
  earnings?: {
    baseSalary: number
    transportation: number
    phoneAllowance: number
    mealAllowance: number
    commission: number
    overtime: number
    bpjsAllowance: number
    adjustmentRatio: number
  }
  deductions?: {
    loans: number
    debt: number
    bpjsTP: number
    bpjsTK: number
    other: number
    description?: string
  }
  bonuses?: {
    amount: number
    description?: string
  }
  isPaid?: boolean
}

export interface SalaryResponse {
  success: boolean
  message: string
  data?: Salary | Salary[]
  error?: string
} 