import { NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"

const prisma = new PrismaClient({
  datasources: {
    db: {
      url: process.env.DATABASE_URL
    }
  }
})

export async function GET(request: Request) {
  try {
    // Extract API key from headers
    const apiKey = request.headers.get('x-api-key')
    const expectedApiKey = process.env.PAYROLL_API_KEY || "c4a1c8fdcde5513a3feeb9b8462ada8909a4a54af4211ea11efaa87fa9eefb56"
    
    // Validate API key
    if (!apiKey || apiKey !== expectedApiKey) {
      return NextResponse.json(
        {
          success: false,
          message: "API key tidak valid atau tidak ditemukan",
          data: null
        },
        { status: 401 }
      )
    }

    const { searchParams } = new URL(request.url)
    const bulan = searchParams.get('bulan')
    const tahun = searchParams.get('tahun') 
    const startDate = searchParams.get('startDate')
    const endDate = searchParams.get('endDate')
    const outletId = searchParams.get('outletId')

    console.log("📊 Payroll API called with params:", { bulan, tahun, startDate, endDate, outletId })

    // Generate periode string
    const currentDate = new Date()
    const targetBulan = bulan ? parseInt(bulan) : (currentDate.getMonth() + 1)
    const targetTahun = tahun ? parseInt(tahun) : currentDate.getFullYear()
    
    const monthNames = [
      "Januari", "Februari", "Maret", "April", "Mei", "Juni",
      "Juli", "Agustus", "September", "Oktober", "November", "Desember"
    ]
    const periode = `${monthNames[targetBulan - 1]} ${targetTahun}`

    // TODO: Implementasi integrasi real dengan BreakTime API untuk payroll summary
    // Endpoint ini memerlukan integrasi dengan BreakTime API untuk mendapatkan summary semua terapis
    console.error("❌ Payroll summary endpoint belum terintegrasi dengan BreakTime API")
    
    return NextResponse.json(
      {
        success: false,
        message: "Endpoint payroll summary belum terintegrasi dengan BreakTime API. Gunakan endpoint /api/payroll/therapist atau /api/payroll/all-outlets untuk data terapis individual.",
        data: null,
        note: "Untuk mendapatkan data real, gunakan auto-fill pada form penggajian yang sudah terintegrasi dengan BreakTime API"
      },
      { status: 501 } // 501 Not Implemented
    )



  } catch (error) {
    console.error("❌ Error in GET /api/payroll:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Terjadi kesalahan saat mengambil data payroll",
        data: null
      },
      { status: 500 }
    )
  }
} 