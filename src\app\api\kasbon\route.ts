import { NextResponse } from "next/server"
import prisma from "@/lib/prisma"
import { authMiddleware } from "@/lib/auth"
import { cookies } from "next/headers"

// Tambahkan config untuk route segment
export const dynamic = 'force-dynamic'
export const revalidate = 0

// GET /api/kasbon
export async function GET(request: Request) {
  try {
    const auth = await authMiddleware(request)
    if (!auth) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    // Ambil query parameters
    const url = new URL(request.url)
    const status = url.searchParams.get('status')
    const type = url.searchParams.get('type')
    const search = url.searchParams.get('search')
    const employeeId = url.searchParams.get('employeeId')
    const startDate = url.searchParams.get('startDate')
    const endDate = url.searchParams.get('endDate')
    
    console.log("API Kasbon dipanggil dengan parameter:", {
      status,
      type,
      search,
      employeeId,
      startDate,
      endDate
    });

    // Buat query filter
    const where: any = {}
    
    if (status) {
      where.status = status;
      console.log(`Filter status: ${status}`);
    }
    
    if (type) {
      where.type = type;
      console.log(`Filter type: ${type}`);
    }

    if (employeeId) {
      console.log(`Filter employeeId: ${employeeId}`);
      
      // Cari userId berdasarkan employeeId
      const employee = await prisma.employee.findUnique({
        where: { id: employeeId },
        select: { userId: true }
      });
      
      if (employee) {
        console.log(`Berhasil menemukan userId: ${employee.userId} untuk employeeId: ${employeeId}`);
        where.userId = employee.userId;
      } else {
        console.log(`Tidak menemukan userId untuk employeeId: ${employeeId}`);
        // Jika employee tidak ditemukan, gunakan filter yang tidak akan menemukan data apa pun
        where.userId = "tidak-ada";
      }
    }

    if (startDate && endDate) {
      console.log(`Filter tanggal: ${startDate} hingga ${endDate}`);
      const parsedStartDate = new Date(startDate);
      const parsedEndDate = new Date(endDate);
      
      console.log(`Tanggal yang diparse:`, {
        parsedStartDate: parsedStartDate.toISOString(),
        parsedEndDate: parsedEndDate.toISOString()
      });
      
      where.createdAt = {
        gte: parsedStartDate,
        lte: parsedEndDate
      }
    }

    if (search) {
      where.OR = [
        {
          user: {
            name: {
              contains: search,
              mode: 'insensitive'
            }
          }
        },
        {
          description: {
            contains: search,
            mode: 'insensitive'
          }
        }
      ]
    }

    // Log untuk debugging
    console.log("Query where:", JSON.stringify(where, null, 2));

    const kasbon = await prisma.kasbon.findMany({
      where,
      include: {
        user: {
          select: {
            name: true,
            employee: {
              select: {
                position: {
                  select: {
                    name: true
                  }
                }
              }
            }
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    });
    
    console.log(`Jumlah data kasbon ditemukan: ${kasbon.length}`);
    if (kasbon.length > 0) {
      console.log("Contoh data kasbon pertama:", JSON.stringify(kasbon[0], null, 2));
    }

    return NextResponse.json(kasbon);
  } catch (error) {
    console.error("Kasbon Error:", error)
    return NextResponse.json(
      { message: "Internal Server Error", error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    )
  }
}

// POST /api/kasbon
export async function POST(request: Request) {
  try {
    const auth = await authMiddleware(request)
    if (!auth) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    const body = await request.json()
    console.log("Received body:", body)

    // Validasi input
    if (!body.employeeId) {
      return NextResponse.json(
        { message: "ID Karyawan harus diisi" },
        { status: 400 }
      )
    }

    if (!body.amount || body.amount <= 0) {
      return NextResponse.json(
        { message: "Nominal harus lebih dari 0" },
        { status: 400 }
      )
    }

    if (!body.type || !["KASBON", "PIUTANG"].includes(body.type)) {
      return NextResponse.json(
        { message: "Tipe kasbon tidak valid" },
        { status: 400 }
      )
    }

    if (!body.notes?.trim()) {
      return NextResponse.json(
        { message: "Keterangan harus diisi" },
        { status: 400 }
      )
    }

    // Cek apakah employee exists
    const employee = await prisma.employee.findUnique({
      where: { id: body.employeeId },
      select: {
        userId: true
      }
    })

    if (!employee) {
      return NextResponse.json(
        { message: "Karyawan tidak ditemukan" },
        { status: 404 }
      )
    }

    try {
      // Log untuk debugging
      console.log("Creating kasbon with data:", {
        userId: employee.userId,
        amount: Number(body.amount),
        description: body.notes.trim(),
        status: "PENDING",
        type: body.type
      })

      const kasbon = await prisma.kasbon.create({
        data: {
          userId: employee.userId,
          amount: Number(body.amount),
          description: body.notes.trim(),
          status: "PENDING",
          type: body.type
        }
      })

      console.log("Created kasbon:", kasbon)

      return NextResponse.json({
        message: "Kasbon berhasil dibuat",
        data: kasbon
      }, { status: 201 })

    } catch (error) {
      console.error("Database Error:", error)
      return NextResponse.json(
        { 
          message: "Gagal menyimpan data ke database",
          error: error instanceof Error ? error.message : "Unknown error"
        },
        { status: 500 }
      )
    }

  } catch (error) {
    console.error("Server Error:", error)
    return NextResponse.json(
      { message: "Internal Server Error" },
      { status: 500 }
    )
  }
}

// PUT /api/kasbon/[id]/route.ts
export async function PUT(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies()
    const token = cookieStore.get("auth_token")

    if (!token) {
      return NextResponse.json(
        { message: "Unauthorized - No token provided" },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { id } = params

    if (!id) {
      return NextResponse.json(
        { message: "Missing kasbon ID" },
        { status: 400 }
      )
    }

    // Validasi status
    if (!["PENDING", "APPROVED", "REJECTED", "PAID"].includes(body.status)) {
      return NextResponse.json(
        { message: "Invalid status" },
        { status: 400 }
      )
    }

    const kasbon = await prisma.kasbon.update({
      where: { id },
      data: {
        status: body.status,
        approvedAt: body.status === "APPROVED" ? new Date() : undefined,
        paidAt: body.status === "PAID" ? new Date() : undefined
      },
      include: {
        employee: {
          include: {
            user: {
              select: {
                name: true
              }
            },
            position: true
          }
        }
      }
    })

    return NextResponse.json(kasbon)
  } catch (error) {
    console.error("Kasbon Error:", error)
    return NextResponse.json(
      { message: "Internal Server Error", error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    )
  }
}

// DELETE /api/kasbon/[id]/route.ts
export async function DELETE(
  request: Request,
  { params }: { params: { id: string } }
) {
  try {
    const cookieStore = cookies()
    const token = cookieStore.get("auth_token")

    if (!token) {
      return NextResponse.json(
        { message: "Unauthorized - No token provided" },
        { status: 401 }
      )
    }

    const { id } = params

    if (!id) {
      return NextResponse.json(
        { message: "Missing kasbon ID" },
        { status: 400 }
      )
    }

    await prisma.kasbon.delete({
      where: { id }
    })

    return NextResponse.json({ message: "Kasbon berhasil dihapus" })
  } catch (error) {
    console.error("Kasbon Error:", error)
    return NextResponse.json(
      { message: "Internal Server Error", error: error instanceof Error ? error.message : "Unknown error" },
      { status: 500 }
    )
  }
} 