# 🧪 Testing Guide - Auto-Fill Payroll Feature

## 📋 Prerequisites Testing

Pastikan hal-hal berikut sudah siap sebelum testing:

### 1. Environment Setup
```bash
# File .env atau .env.local harus mengandung:
NEXT_PUBLIC_PAYROLL_API_KEY="c4a1c8fdcde5513a3feeb9b8462ada8909a4a54af4211ea11efaa87fa9eefb56"
NEXT_PUBLIC_API_URL="http://localhost:3000"
```

### 2. API Payroll BreakTime
- ✅ API endpoint `/api/payroll/therapist` tersedia
- ✅ API key sudah valid dan aktif
- ✅ Data terapis tersedia di sistem BreakTime

### 3. Database Setup
- ✅ Tabel employees dan positions sudah terisi
- ✅ Ada data terapis dengan nama yang sama di sistem BreakTime

## 🎯 Test Scenarios

### Scenario 1: Happy Path - Data Ditemukan
**Langkah:**
1. Buka halaman `/dashboard/penggajian`
2. Klik tombol "Tambah Data"
3. Pilih terapis dari dropdown (contoh: "Ahmad Spa")
4. Set periode bulan dan tahun (contoh: Desember 2024)
5. Tombol "🚀 Auto-Fill Data Payroll" akan muncul
6. Klik tombol Auto-Fill

**Expected Result:**
- ✅ Loading indicator muncul dengan text "Mengambil..."
- ✅ API call berhasil ke `/api/payroll/therapist`
- ✅ Field Omzet, Komisi, Lembur terisi otomatis
- ✅ Toast success dengan breakdown data
- ✅ Status message "✅ Data terapis berhasil ditemukan..."

**Console Logs Expected:**
```
🚀 Mengambil data payroll untuk terapis: Ahmad Spa
📅 Periode: 12/2024
📊 Data payroll yang diterima: {isFound: true, totalOmzet: 15500000, ...}
✅ Auto-fill berhasil:
   • Omzet: Rp 15.500.000
   • Komisi: Rp 2.325.000
   • Lembur: Rp 450.000
```

### Scenario 2: Data Tidak Ditemukan
**Langkah:**
1. Pilih terapis yang tidak ada di sistem BreakTime
2. Set periode yang valid
3. Klik tombol Auto-Fill

**Expected Result:**
- ⚠️ Toast warning: "Data terapis tidak ditemukan di sistem payroll..."
- ⚠️ Status message: "⚠️ Data terapis tidak ditemukan untuk periode yang diminta"
- ⚠️ Field tetap kosong (nilai 0)

**Console Logs Expected:**
```
⚠️ Data tidak ditemukan:
   • Terapis: [Nama Terapis]
   • Periode: [Bulan/Tahun]
```

### Scenario 3: Network/API Error
**Langkah:**
1. Simulasikan network error (disconnect internet atau server down)
2. Pilih terapis dan periode yang valid
3. Klik tombol Auto-Fill

**Expected Result:**
- ❌ Toast error dengan pesan error spesifik
- ❌ Status message: "❌ [Error message]"
- ❌ Field tetap kosong

**Console Logs Expected:**
```
❌ Error saat auto-fill: [Error details]
```

### Scenario 4: Validasi Input
**Langkah:**
1. Coba klik Auto-Fill tanpa memilih terapis
2. Pilih terapis tapi periode tidak lengkap

**Expected Result:**
- ❌ Toast error: "Silakan pilih karyawan terlebih dahulu"
- ❌ Tombol Auto-Fill tidak muncul jika periode tidak lengkap

### Scenario 5: UI/UX Testing
**Langkah:**
1. Test responsiveness di berbagai screen size
2. Test dark mode compatibility
3. Test accessibility (keyboard navigation)

**Expected Result:**
- ✅ UI responsive di mobile dan desktop
- ✅ Dark mode styling bekerja dengan benar
- ✅ Button accessible via keyboard

## 📊 Data Validation Testing

### Test Data Accuracy
Bandingkan data yang di-auto-fill dengan data asli di sistem BreakTime:

| Field | Expected Source | Validation Method |
|-------|----------------|-------------------|
| Omzet | `response.data.summary.totalOmzet` | Cross-check manual |
| Komisi | `response.data.summary.totalKomisi` | Verify calculation |
| Lembur | `response.data.summary.totalLembur` | Check overtime records |

### Sample Test Data
```javascript
// Test dengan data terapis yang sudah diketahui:
const testTherapis = [
  {
    name: "Ahmad Spa",
    expectedOmzet: 15500000,
    expectedKomisi: 2325000,
    expectedLembur: 450000,
    periode: { bulan: 12, tahun: 2024 }
  },
  {
    name: "Budi Therapist", 
    expectedOmzet: 12000000,
    expectedKomisi: 1800000,
    expectedLembur: 300000,
    periode: { bulan: 11, tahun: 2024 }
  }
]
```

## 🐛 Bug Report Template

Jika menemukan bug, gunakan template ini:

```markdown
## 🐛 Bug Report

**Environment:**
- Browser: [Chrome/Firefox/Safari]
- OS: [Windows/Mac/Linux]
- Screen Size: [Desktop/Tablet/Mobile]

**Steps to Reproduce:**
1. [Step 1]
2. [Step 2] 
3. [Step 3]

**Expected Behavior:**
[What should happen]

**Actual Behavior:**
[What actually happened]

**Console Logs:**
```
[Copy console logs here]
```

**Screenshot:**
[Attach screenshot if applicable]

**Additional Context:**
[Any other relevant information]
```

## ⚡ Performance Testing

### Load Testing
1. Test dengan multiple users menggunakan auto-fill bersamaan
2. Monitor API response time
3. Check memory usage saat proses auto-fill

**Metrics to Monitor:**
- API response time: < 2 seconds
- UI response time: < 500ms
- Memory usage: Tidak ada memory leaks

### Network Testing
1. Test dengan slow network connection
2. Test dengan intermittent connection
3. Test offline behavior

## 📱 Cross-Platform Testing

### Browser Compatibility
- [ ] Chrome (latest)
- [ ] Firefox (latest)
- [ ] Safari (latest)
- [ ] Edge (latest)
- [ ] Mobile browsers

### Device Testing
- [ ] Desktop (1920x1080)
- [ ] Laptop (1366x768)
- [ ] Tablet (768x1024)
- [ ] Mobile (375x667)

## 🔧 Developer Testing

### Code Quality
```bash
# Run linting
npm run lint

# Run type checking
npm run type-check

# Run tests
npm run test
```

### API Testing dengan Postman/cURL
```bash
# Test API endpoint langsung
curl -X GET "http://localhost:3000/api/payroll/therapist?therapistName=Ahmad%20Spa&bulan=12&tahun=2024" \
     -H "x-api-key: c4a1c8fdcde5513a3feeb9b8462ada8909a4a54af4211ea11efaa87fa9eefb56" \
     -H "Content-Type: application/json"
```

## 📈 Success Criteria

Feature dianggap berhasil jika:

- ✅ 95% test scenarios passed
- ✅ Tidak ada critical bugs
- ✅ Performance requirements terpenuhi
- ✅ UI/UX sesuai design specifications
- ✅ Cross-browser compatibility confirmed
- ✅ Accessibility requirements met

## 📋 Testing Checklist

### Functional Testing
- [ ] Auto-fill button muncul kondisional
- [ ] API integration bekerja dengan benar
- [ ] Data mapping akurat
- [ ] Error handling comprehensive
- [ ] Loading states bekerja
- [ ] Toast notifications muncul

### UI/UX Testing  
- [ ] Responsive design
- [ ] Dark mode support
- [ ] Accessibility compliance
- [ ] Visual feedback
- [ ] Keyboard navigation
- [ ] Touch interaction (mobile)

### Performance Testing
- [ ] API response time acceptable
- [ ] UI response time fast
- [ ] No memory leaks
- [ ] Graceful degradation
- [ ] Network error handling
- [ ] Cache behavior

### Integration Testing
- [ ] Form submission setelah auto-fill
- [ ] Data persistence
- [ ] Conflict resolution dengan data existing
- [ ] Multiple employee testing
- [ ] Periode edge cases

---

**Last Updated**: Desember 2024  
**Tester**: [Your Name]  
**Status**: Ready for Testing ✅ 