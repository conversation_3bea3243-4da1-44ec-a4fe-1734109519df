# ✅ Real BreakTime API Integration

## API Endpoint Update

Sistem auto-fill payroll sekarang menggunakan **BreakTime API yang real** di domain resmi mybreaktime.co.id! 🎯

## API Configuration

### **Real BreakTime API URL**
```typescript
const BREAKTIME_API_URL = "https://mybreaktime.co.id/api/payroll/therapist"
```

### **API Request Structure**
```typescript
const apiPayload = {
  therapist_name: "Ahmad Spa",
  start_date: "2024-12-01",    // Format ISO: YYYY-MM-DD
  end_date: "2024-12-31",      // Format ISO: YYYY-MM-DD  
  outlet: "Breaktime Palu Setiabudi",
  outlet_code: "BT-PALU-01"
}

const response = await fetch("https://mybreaktime.co.id/api/payroll/therapist", {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${apiKey}`,
    'X-API-Key': 'c4a1c8fdcde5513a3feeb9b8462ada8909a4a54af4211ea11efaa87fa9eefb56'
  },
  body: JSON.stringify(apiPayload)
})
```

### **Expected API Response**
```json
{
  "success": true,
  "data": {
    "summary": {
      "totalOmzet": 15500000,
      "totalKomisi": 2325000,
      "totalLembur": 450000,
      "totalLemburMinutes": 180,
      "totalTransaksi": 85
    },
    "therapistName": "Ahmad Spa",
    "outletName": "Breaktime Palu Setiabudi",
    "periode": "Desember 2024"
  }
}
```

## Implementation Details

### 1. **Date Format Conversion** 📅

**Sebelum** (Human readable):
```javascript
start_date: "01 Juli 2025"
end_date: "31 Juli 2025"
```

**Sesudah** (ISO Standard):
```javascript
start_date: "2025-07-01"  // YYYY-MM-DD
end_date: "2025-07-31"    // YYYY-MM-DD
```

**Code Implementation**:
```typescript
// Generate ISO date format untuk BreakTime API
const startDate = `${tahun}-${bulan.toString().padStart(2, '0')}-01`
const endDate = `${tahun}-${bulan.toString().padStart(2, '0')}-${new Date(tahun, bulan, 0).getDate()}`

console.log("📅 Date range:", { startDate, endDate })
// Output: { startDate: "2025-07-01", endDate: "2025-07-31" }
```

### 2. **Enhanced Logging** 📝

Sistem sekarang memberikan log yang lebih detail:

```javascript
📞 Calling BreakTime API for: {
  therapist: "Ahmad Spa",
  outlet: "Breaktime Palu Setiabudi",
  code: "BT-PALU-01"
}

📅 Date range: {
  startDate: "2025-07-01",
  endDate: "2025-07-31" 
}

📤 API Request payload: {
  therapist_name: "Ahmad Spa",
  start_date: "2025-07-01",
  end_date: "2025-07-31",
  outlet: "Breaktime Palu Setiabudi",
  outlet_code: "BT-PALU-01"
}

✅ Real BreakTime API response: {
  success: true,
  data: { summary: { totalOmzet: 15500000, ... } }
}
```

### 3. **API Integration Pattern** 🔗

Sistem mengikuti pattern yang sama seperti contoh user:

```typescript
// Setup di aplikasi penggajian
const api = new BreakTimePayrollAPI('your-api-key', 'https://mybreaktime.co.id');

// Get data terapis  
const payrollData = await api.getTherapistPayroll('Ahmad Spa', '2024-12-01', '2024-12-31');

// Data sudah bisa langsung dipakai untuk hitung gaji
console.log(payrollData.data.summary.totalOmzet);  // 15500000
console.log(payrollData.data.summary.totalKomisi); // 2325000
console.log(payrollData.data.summary.totalLembur); // 450000
```

## Auto-Fill Flow dengan Real API

### **Complete Integration Flow** 🔄

1. **User Action**: Pilih nama terapis → Klik "Auto-Fill Payroll"

2. **Database Query**: Cari terapis di database dengan outlet info
   ```sql
   SELECT * FROM employee 
   JOIN outlet ON employee.outlet_id = outlet.id 
   WHERE user.name ILIKE '%Ahmad Spa%'
   ```

3. **API Call Preparation**:
   ```typescript
   const apiPayload = {
     therapist_name: "Ahmad Spa",
     start_date: "2025-07-01", 
     end_date: "2025-07-31",
     outlet: "Breaktime Palu Setiabudi",
     outlet_code: "BT-PALU-01"
   }
   ```

4. **Real BreakTime API Call**:
   ```typescript
   POST https://mybreaktime.co.id/api/payroll/therapist
   Headers: { Authorization: Bearer <api-key>, X-API-Key: <api-key> }
   Body: { therapist_name, start_date, end_date, outlet, outlet_code }
   ```

5. **Response Processing**:
   ```typescript
   const { totalOmzet, totalKomisi, totalLembur } = response.data.summary
   
   // Update form fields dengan data real
   setFormData({
     omzet: totalOmzet,     // 15,500,000
     komisi: totalKomisi,   // 2,325,000  
     lembur: totalLembur    // 450,000
   })
   ```

6. **UI Update**: Form auto-terisi dengan data real dari BreakTime

## Error Handling

### **API Error Scenarios** ⚠️

1. **BreakTime Server Down**:
   ```json
   {
     "success": false,
     "message": "Gagal mengakses BreakTime API: fetch failed"
   }
   ```

2. **Invalid API Key**:
   ```json
   {
     "success": false, 
     "message": "BreakTime API error: 401 - Unauthorized"
   }
   ```

3. **Terapis Not Found in BreakTime**:
   ```json
   {
     "success": false,
     "message": "BreakTime API error: 404 - Therapist not found"
   }
   ```

4. **Timeout/Network Issues**:
   ```json
   {
     "success": false,
     "message": "Gagal mengakses BreakTime API: timeout"
   }
   ```

## Testing dengan Real API

### **Test Scenarios** 🧪

1. **Valid Therapist - Success**:
   - Input: "Ahmad Spa" (exists in BreakTime)
   - Expected: Real payroll data loaded
   - API Response: 200 OK dengan data lengkap

2. **Valid Therapist - Different Outlet**:
   - Input: "Sari Spa" (different outlet)
   - Expected: Data sesuai outlet terapis
   - API Call: Dengan outlet_code yang berbeda

3. **Invalid Therapist**:
   - Input: "John Doe" (not in BreakTime)
   - Expected: 404 Error, form tetap kosong
   - Toast: "Terapis tidak ditemukan di BreakTime"

4. **API Key Issues**:
   - Scenario: Wrong/expired API key
   - Expected: 401 Error, clear error message
   - Toast: "API key tidak valid"

## Benefits of Real Integration

- ✅ **Authentic Data**: Langsung dari sistem BreakTime production
- ✅ **Real-time Sync**: Data selalu up-to-date dengan BreakTime
- ✅ **Proper Error Handling**: User tahu status integrasi yang sebenarnya
- ✅ **Standard Format**: Menggunakan ISO date format yang universal
- ✅ **Outlet Accuracy**: Data sesuai outlet tempat terapis bekerja

## Files Modified

1. **`src/app/api/payroll/therapist/route.ts`**
   - ✅ Updated API URL to `https://mybreaktime.co.id/api/payroll/therapist`
   - ✅ Changed date format to ISO (YYYY-MM-DD)
   - ✅ Enhanced logging dengan date range info

2. **`src/app/api/payroll/all-outlets/route.ts`** 
   - ✅ Updated API URL to real BreakTime domain
   - ✅ Consistent date formatting
   - ✅ Better error logging

---

**Status**: ✅ **INTEGRATED** - Sistem sekarang menggunakan BreakTime API yang real!

Auto-fill payroll akan memanggil `https://mybreaktime.co.id/api/payroll/therapist` dengan format yang benar dan memberikan data yang akurat sesuai sistem BreakTime production. 🚀 