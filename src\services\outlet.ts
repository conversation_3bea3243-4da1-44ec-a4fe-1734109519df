import { Outlet } from "@/types/outlet";

export async function getOutlets(): Promise<Outlet[]> {
  try {
    const response = await fetch("/api/outlet");
    const data = await response.json();
    
    if (!data.success) {
      throw new Error(data.message || "Gagal mengambil data outlet");
    }
    
    return data.data;
  } catch (error) {
    console.error("Error fetching outlets:", error);
    throw error;
  }
}

export async function createOutlet(data: { name: string; code: string }): Promise<Outlet> {
  try {
    const response = await fetch("/api/outlet", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    
    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.message || "Gagal membuat outlet");
    }
    
    return result.data;
  } catch (error) {
    console.error("Error creating outlet:", error);
    throw error;
  }
}

export async function updateOutlet(id: string, data: { name: string; code: string }): Promise<Outlet> {
  try {
    const response = await fetch(`/api/outlet/${id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    
    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.message || "Gagal mengupdate outlet");
    }
    
    return result.data;
  } catch (error) {
    console.error("Error updating outlet:", error);
    throw error;
  }
}

export async function deleteOutlet(id: string): Promise<void> {
  try {
    const response = await fetch(`/api/outlet/${id}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json"
      }
    });
    
    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.message || "Gagal menghapus outlet");
    }
  } catch (error) {
    console.error("Error deleting outlet:", error);
    throw error;
  }
} 