import { NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"
import { UpdateEmployeeInput } from "@/types/employee"
import { authMiddleware } from "@/lib/auth"

const prisma = new PrismaClient()

export async function PUT(request: Request, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    // Tambahkan autentikasi
    const auth = await authMiddleware(request)
    if (!auth) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    const body = await request.json() as UpdateEmployeeInput
    
    // Ambil data employee untuk mendapatkan userId
    const currentEmployee = await prisma.employee.findUnique({
      where: { id: params.id },
      select: { userId: true }
    })

    if (!currentEmployee) {
      return NextResponse.json({
        success: false,
        message: "Karyawan tidak ditemukan"
      }, { status: 404 })
    }

    // Update data dalam transaksi
    const employee = await prisma.$transaction(async (tx) => {
      // Update user data jika ada perubahan nama atau tanggal bergabung
      if (body.name || body.joinDate) {
        await tx.user.update({
          where: { id: currentEmployee.userId },
          data: {
            name: body.name,
            joinDate: body.joinDate ? new Date(body.joinDate) : undefined
          }
        })
      }

      // Update employee data
      return await tx.employee.update({
        where: {
          id: params.id
        },
        data: {
          positionId: body.positionId,
          status: body.status,
          nik: body.nik,
          alamat: body.alamat,
          noTelp: body.noTelp,
          outletId: body.outletId
        },
        include: {
          user: {
            select: {
              name: true,
              username: true,
              department: true,
              position: true,
              joinDate: true
            }
          },
          position: {
            select: {
              name: true,
              omsetPercentage: true
            }
          },
          outlet: true
        }
      })
    })

    return NextResponse.json({
      success: true,
      message: "Berhasil mengupdate karyawan",
      data: employee
    })
  } catch (error) {
    console.error("Error updating employee:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Gagal mengupdate karyawan",
        error: error instanceof Error ? error.message : "Unknown error occurred"
      },
      { status: 500 }
    )
  }
}

export async function DELETE(request: Request, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    // Tambahkan autentikasi
    const auth = await authMiddleware(request)
    if (!auth) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      )
    }

    // Ambil data employee untuk mendapatkan userId
    const employee = await prisma.employee.findUnique({
      where: { id: params.id },
      select: { 
        userId: true,
        id: true
      }
    })

    if (!employee) {
      return NextResponse.json({
        success: false,
        message: "Karyawan tidak ditemukan"
      }, { status: 404 })
    }

    // Hapus semua data terkait dalam satu transaksi
    await prisma.$transaction(async (tx) => {
      // 1. Hapus daily commissions
      await tx.dailyCommission.deleteMany({
        where: { employeeId: employee.id }
      })

      // 2. Hapus slip gaji
      await tx.slipGaji.deleteMany({
        where: { employeeId: employee.id }
      })

      // 3. Hapus salaries
      await tx.salary.deleteMany({
        where: { employeeId: employee.id }
      })

      // 4. Hapus kasbon
      await tx.kasbon.deleteMany({
        where: { userId: employee.userId }
      })

      // 5. Hapus employee
      await tx.employee.delete({
        where: { id: employee.id }
      })

      // 6. Terakhir, hapus user
      await tx.user.delete({
        where: { id: employee.userId }
      })
    })

    return NextResponse.json({
      success: true,
      message: "Berhasil menghapus karyawan dan semua data terkait"
    })
  } catch (error) {
    console.error("Error deleting employee:", error)
    return NextResponse.json(
      {
        success: false,
        message: "Gagal menghapus karyawan dan data terkait",
        error: error instanceof Error ? error.message : "Unknown error occurred"
      },
      { status: 500 }
    )
  }
} 