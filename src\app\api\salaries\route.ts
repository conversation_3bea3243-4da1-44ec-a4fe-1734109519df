import { NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"
import type { <PERSON><PERSON>, SalaryResponse } from "@/types/salary"

const prisma = new PrismaClient()

// GET /api/salaries
export async function GET() {
  try {
    const salaries = await prisma.salary.findMany({
      include: {
        employee: {
          include: {
            user: {
              select: {
                name: true
              }
            },
            position: true
          }
        }
      },
      orderBy: {
        createdAt: 'desc'
      }
    })

    // Transform data to match expected format
    const transformedSalaries = salaries.map((salary) => ({
      id: salary.id,
      employeeId: salary.employeeId,
      employee: {
        name: salary.employee.user.name,
        position: {
          id: salary.employee.position.id,
          name: salary.employee.position.name,
          omsetPercentage: salary.employee.position.omsetPercentage
        }
      },
      periodStart: salary.periodStart,
      periodEnd: salary.periodEnd,
      baseOmset: salary.baseOmset,
      totalOmset: salary.totalOmset,
      salaryAmount: salary.salaryAmount,
      deductions: salary.deductions,
      bonuses: salary.bonuses,
      finalAmount: salary.finalAmount,
      isPaid: salary.isPaid,
      paidAt: salary.paidAt,
      createdAt: salary.createdAt,
      updatedAt: salary.updatedAt
    })) as Salary[]

    const response: SalaryResponse = {
      success: true,
      message: "Data gaji berhasil diambil",
      data: transformedSalaries
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error("Error fetching salaries:", error)
    const response: SalaryResponse = {
      success: false,
      message: "Terjadi kesalahan saat mengambil data gaji",
      error: error instanceof Error ? error.message : "Unknown error"
    }
    return NextResponse.json(response, { status: 500 })
  }
}

// POST /api/salaries
export async function POST(request: Request) {
  try {
    const body = await request.json()
    const salary = await prisma.salary.create({
      data: {
        employeeId: body.employeeId,
        periodStart: new Date(body.periodStart),
        periodEnd: new Date(body.periodEnd),
        baseOmset: body.baseOmset,
        totalOmset: body.totalOmset,
        salaryAmount: body.salaryAmount,
        deductions: body.deductions,
        bonuses: body.bonuses,
        finalAmount: body.finalAmount,
        isPaid: false
      },
      include: {
        employee: {
          include: {
            user: {
              select: {
                name: true
              }
            },
            position: true
          }
        }
      }
    })

    // Transform data to match expected format
    const transformedSalary = {
      id: salary.id,
      employeeId: salary.employeeId,
      employee: {
        name: salary.employee.user.name,
        position: {
          id: salary.employee.position.id,
          name: salary.employee.position.name,
          omsetPercentage: salary.employee.position.omsetPercentage
        }
      },
      periodStart: salary.periodStart,
      periodEnd: salary.periodEnd,
      baseOmset: salary.baseOmset,
      totalOmset: salary.totalOmset,
      salaryAmount: salary.salaryAmount,
      earnings: {
        baseSalary: 0,
        transportation: 0,
        phoneAllowance: 0,
        mealAllowance: 0,
        commission: 0,
        overtime: 0,
        bpjsAllowance: 0,
        adjustmentRatio: 0
      },
      deductions: salary.deductions,
      bonuses: salary.bonuses,
      finalAmount: salary.finalAmount,
      isPaid: salary.isPaid,
      paidAt: salary.paidAt,
      createdAt: salary.createdAt,
      updatedAt: salary.updatedAt
    }

    const response: SalaryResponse = {
      success: true,
      message: "Data gaji berhasil dibuat",
      data: transformedSalary
    }

    return NextResponse.json(response)
  } catch (error) {
    console.error("Error creating salary:", error)
    const response: SalaryResponse = {
      success: false,
      message: "Terjadi kesalahan saat membuat data gaji",
      error: error instanceof Error ? error.message : "Unknown error"
    }
    return NextResponse.json(response, { status: 500 })
  }
} 