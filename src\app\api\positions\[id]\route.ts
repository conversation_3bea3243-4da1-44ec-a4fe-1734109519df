import { NextResponse } from "next/server"
import { PrismaClient } from "@prisma/client"

const prisma = new PrismaClient()

// PUT /api/positions/:id
export async function PUT(request: Request, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    const body = await request.json()
    const position = await prisma.position.update({
      where: { id: params.id },
      data: {
        name: body.name,
        omsetPercentage: body.omsetPercentage,
        gajiPokok: body.gajiPokok,
        tunjangan: body.tunjangan,
        isActive: body.isActive,
        isKontrak: body.isKontrak,
        targetKontrak: body.targetKontrak
      }
    })
    return NextResponse.json(position)
  } catch (error) {
    console.error("Error updating position:", error)
    return NextResponse.json(
      { error: "Terjadi kesalahan saat mengupdate jabatan" },
      { status: 500 }
    )
  }
}

// DELETE /api/positions/:id
export async function DELETE(request: Request, props: { params: Promise<{ id: string }> }) {
  const params = await props.params;
  try {
    await prisma.position.delete({
      where: { id: params.id }
    })
    return NextResponse.json({ success: true })
  } catch (error) {
    console.error("Error deleting position:", error)
    return NextResponse.json(
      { error: "Terjadi kesalahan saat menghapus jabatan" },
      { status: 500 }
    )
  }
} 